<script setup lang="ts">
import { ElMessage, ElMessageBox } from "element-plus"
import { ref } from "vue"

interface Plugin {
  id: string
  name: string
  version: string
  status: "active" | "inactive"
  description: string
}

const pluginList = ref<Plugin[]>([
  {
    id: "1",
    name: "示例插件",
    version: "1.0.0",
    status: "active",
    description: "这是一个示例插件"
  }
])

function handleAdd() {
  ElMessage.info("添加插件功能开发中")
}

function handleEdit(row: Plugin) {
  ElMessage.info("编辑插件功能开发中")
}

function handleToggle(row: Plugin) {
  row.status = row.status === "active" ? "inactive" : "active"
  ElMessage.success(`插件${row.status === "active" ? "启用" : "禁用"}成功`)
}

function handleDelete(row: Plugin) {
  ElMessageBox.confirm("确定要删除该插件吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const index = pluginList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      pluginList.value.splice(index, 1)
      ElMessage.success("删除成功")
    }
  })
}
</script>

<template>
  <div class="plugin-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>插件管理</span>
          <el-button type="primary" @click="handleAdd">
            添加插件
          </el-button>
        </div>
      </template>
      <el-table :data="pluginList" style="width: 100%">
        <el-table-column prop="name" label="插件名称" />
        <el-table-column prop="version" label="版本" width="120" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="{ row }">
            <el-tag :type="row.status === 'active' ? 'success' : 'info'">
              {{ row.status === 'active' ? '已启用' : '已禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="primary" link @click="handleToggle(row)">
                {{ row.status === 'active' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" link @click="handleDelete(row)">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.plugin-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  width: 100%;
}
</style>
