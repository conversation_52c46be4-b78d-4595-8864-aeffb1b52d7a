<script lang="ts" setup>
import type {
  ChatHistoryResponse,
  IChatDialogueSchema,
  IApp,
  UserChatContent,
} from "@@/apis/chat/type"
import { provide, ref, watch } from "vue"
import { useChat } from "./composables/useChat"
import ChatContentContainer from "./components/ChatContentContainer.vue"
import ChatInputPanel from "./components/ChatInputPanel.vue"

// 响应式数据
const history = ref<ChatHistoryResponse>([])
const replyLoading = ref(false)
const canAbort = ref(false)
const currentDialogue = ref<IChatDialogueSchema>({} as IChatDialogueSchema)
const appInfo = ref<IApp>({})
const temperatureValue = ref(0.6)
const maxNewTokensValue = ref(4000)
const resourceValue = ref<any>(null)
const modelValue = ref("")
const dialogueList = ref([])
const listLoading = ref(false)
const historyLoading = ref(false)

// 聊天相关
const { chat, ctrl } = useChat({
  app_code: currentDialogue.value.app_code || "",
})

// 滚动引用
const scrollRef = ref<any>(null)

// 处理聊天
async function handleChat(content: UserChatContent, data?: Record<string, any>): Promise<void> {
  return new Promise<void>((resolve) => {
    setReplyLoading(true)

    // 添加用户消息到历史记录
    const userMessage = {
      role: "human" as const,
      context: typeof content === "string" ? content : JSON.stringify(content),
      model_name: data?.model_name || modelValue.value,
      order: history.value.length + 1,
      time_stamp: Date.now(),
    }

    // 添加AI思考消息
    const aiMessage = {
      role: "view" as const,
      context: "",
      model_name: data?.model_name || modelValue.value,
      order: history.value.length + 2,
      time_stamp: Date.now(),
      thinking: true,
    }

    const tempHistory = [...history.value, userMessage, aiMessage] as any
    const aiMessageIndex = tempHistory.length - 1
    setHistory(tempHistory)

    // 调用聊天API
    chat({
      data: {
        user_input: content,
        model_name: modelValue.value,
        ...data,
      },
      chatId: currentDialogue.value.conv_uid || "default",
      onMessage: (message: string) => {
        setCanAbort(true)
        tempHistory[aiMessageIndex].context += message
        tempHistory[aiMessageIndex].thinking = false
        setHistory([...tempHistory])
      },
      onDone: () => {
        setReplyLoading(false)
        setCanAbort(false)
        resolve()
      },
      onError: (message: string) => {
        setReplyLoading(false)
        setCanAbort(false)
        tempHistory[aiMessageIndex].context = message
        tempHistory[aiMessageIndex].thinking = false
        setHistory([...tempHistory])
        resolve()
      },
      ctrl,
    })
  })
}

// 设置函数
function setHistory(newHistory: ChatHistoryResponse) {
  history.value = newHistory
}

function setReplyLoading(loading: boolean) {
  replyLoading.value = loading
}

function setCanAbort(abort: boolean) {
  canAbort.value = abort
}

function setAppInfo(info: IApp) {
  appInfo.value = info
}

function setResourceValue(value: any) {
  resourceValue.value = value
}

function refreshDialogList() {
  // 刷新对话列表的逻辑
  console.log("刷新对话列表")
}

function refreshHistory() {
  // 刷新历史记录的逻辑
  console.log("刷新历史记录")
}

function refreshAppInfo() {
  // 刷新应用信息的逻辑
  console.log("刷新应用信息")
}

// 提供数据给子组件
provide("history", history)
provide("replyLoading", replyLoading)
provide("canAbort", canAbort)
provide("handleChat", handleChat)
provide("appInfo", appInfo)
provide("currentDialogue", currentDialogue)
provide("temperatureValue", temperatureValue)
provide("maxNewTokensValue", maxNewTokensValue)
provide("resourceValue", resourceValue)
provide("setResourceValue", setResourceValue)
provide("refreshDialogList", refreshDialogList)
provide("scrollRef", scrollRef)

// 监听滚动引用变化
watch(scrollRef, (newRef) => {
  if (newRef?.scrollRef) {
    // [Vue warn]: provide() can only be used inside setup().但是注释掉可能影响滚动条
    provide("scrollRef", newRef.scrollRef)
  }
})
</script>

<template>
  <el-container class="chat-layout">
    <!-- 侧边栏 -->
    <el-aside width="300px" class="chat-sider">
      <div class="sider-content">
        <h3>对话列表</h3>
        <div v-loading="listLoading">
          <div class="dialogue-list">
            <p v-if="dialogueList.length === 0">
              暂无对话记录
            </p>
            <!-- 这里可以添加对话列表组件 -->
          </div>
        </div>
      </div>
    </el-aside>
    <el-main class="main-layout">
      <div v-loading="historyLoading" class="chat-container">
        <!-- 聊天内容容器 -->
        <ChatContentContainer ref="scrollRef" />

        <!-- 输入面板 -->
        <ChatInputPanel :ctrl="ctrl" />
      </div>
    </el-main>
  </el-container>
</template>

<style lang="scss" scoped>
.chat-layout {
  height: 100%;
  background: transparent;
}

.chat-sider {
  background: rgba(255, 255, 255, 0.95);
  border-right: 1px solid #e5e7eb;

  .dark & {
    background: rgba(0, 0, 0, 0.8);
    border-right-color: rgba(255, 255, 255, 0.1);
  }
}

.sider-content {
  padding: 1rem;
  height: 100%;

  h3 {
    margin: 0 0 1rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;

    .dark & {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

.dialogue-list {
  p {
    color: #6b7280;
    text-align: center;
    margin: 2rem 0;
  }
}

.el-main.main-layout {
  padding: 0;
  overflow: hidden;
  background: transparent;
}

.chat-spin {
  height: 100%;

  :deep(.el-spin-container) {
    height: 100%;
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  .dark & {
    background: rgba(0, 0, 0, 0.8);
  }
}
</style>
