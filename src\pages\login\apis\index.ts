import type * as Auth from "./type"
import { request } from "@/http/axios"

/** 获取登录验证码 */
export function getCaptchaApi() {
  return request<Auth.CaptchaResponseData>({
    url: "auth/captcha",
    method: "get"
  })
}

/** 登录并返回 Token */
export function loginApi(data: Auth.LoginRequestData) {
  return request<Auth.LoginResponseData>({
    url: "auth/login",
    method: "post",
    data,
    version:'/api'
  })
}

/** 刷新Token */
export function refreshTokenApi(refresh_token: string) {
  return request({
    url: "auth/refresh-token",
    method: "post",
    version:'/api',
    data: { refresh_token }
  })
}

/** 获取用户权限 */
export function getUserPermissionApi(data:any) {
  return request({
    url: "/auth/permission_list",
    method: "get",
    params:data,
    version:'/api',
  })
}
