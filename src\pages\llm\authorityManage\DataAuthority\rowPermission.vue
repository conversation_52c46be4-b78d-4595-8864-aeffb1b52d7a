<template>
  <el-form label-width="90px">
    <el-form-item label="数据连接">
      <el-select v-model="form.datasource" placeholder="请选择数据连接" style="width: 100%;" @change="changeDataSource">
        <el-option v-for="item in connectionOptions" :key="item.id" :label="item.params.database" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="权限规则">
      <el-radio-group v-model="form.rule">
        <el-radio label="and">AND</el-radio>
        <el-radio label="or">OR</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="权限字段" class="fields-list" v-if="form.datasource">
      <!-- 数据脱敏 -->
      <div v-for="(item, idx) in form.fields" :key="'mask-' + idx" class="field-row">
        <div class="left-row">
          <div class="table-width">
            <el-select v-model="item.table" placeholder="请选择表" class="table-select mr5"
              @change="changeTableData(item)">
              <el-option v-for="table in tableList" :key="table.table_name" :label="table.table_name" :value="table.table_name" />
            </el-select>
          </div>
          <el-select v-model="item.field" placeholder="请选择字段" class="column-select" :disabled="!item.table"
            @change="val => onColumnChange(item, val, item.table)"
            @visible-change="val => onFieldDropdownVisible(val, item)">
            <el-option v-for="col in item.fieldList" :key="col.alias" :label="col.alias" :value="col.field" />
          </el-select>
          <el-select v-model="item.condition" class="mask-select" :disabled="!item.field">
            <el-option v-for="i in conditionList" :key="i.type" :label="i.name" :value="i.type" />
          </el-select>
          <el-select
            v-model="item.attribute"
            placeholder="请选择属性"
            class="mask-select"
            :disabled="!item.field"
            filterable
            allow-create
            default-first-option
            multiple
            collapse-tags
            @visible-change="val => onAttributeDropdownVisible(val, item)"
          >
            <el-option v-for="i in item.attributeList" :key="i.key" :label="i.value" :value="i.key" />
          </el-select>
        </div>
        <el-button type="text" icon="Delete" @click="removeField(idx)" />
      </div>
      <div>
        <el-button type="primary" plain size="small" icon="Plus" @click="addRow" class="add-row-btn">新增一行</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { getDatasourcesListApi, getDatasourceTableListApi, getDatasourceTableFieldListApi } from "@/common/apis/llm"
import type { Database } from '@/pages/llm/dataSourceManage/typs'
import { getDatasourceTableFieldEnumApi } from '@/common/apis/llm';
const connectionOptions = ref<any>([]);
interface RowField {
  table: string;
  field: string;
  condition: string;
  attribute: [];
  fieldList: Array<{ fieldId: string; alias: string; fieldType: string,field:string }>;
  attributeList:  Array<{ key: string; value: string }>;
}

const form = ref<{
  datasource?: string;
  rule?: string;
  fields: RowField[];
}>({
  datasource: '',
  rule: 'and',
  fields: [
    { table: '', field: '', condition: 'eq', attribute: [], fieldList: [], attributeList: [] }
  ]
});
interface Props {
  selectedAuth: Object
}
const props = defineProps<Props>()
const tableList = ref([]);  //表
const fieldList = ref([]) //字段
const conditionList = ref([{
  type:'eq',
  name:'等于',
},{
  type:'exist',
  name:'存在',
},{
  type:'notexist',
  name:'不存在',
}]);
watch(() => props.selectedAuth, (v:any) => {
  setInitData()
});
watch(
  () => form.value.datasource,
  (val) => {
    if (val) {
      changeDataSource();
    }
  }
);
onMounted(async () => {
  getDatasetList()
  setInitData()
  form.value.fields = (form.value.fields || [{ table: '', field: '', condition: 'eq', attribute: [] }]).map(f => ({ ...f, fieldList: [], attributeList: [] }))
})
function setInitData(){
  const v = props.selectedAuth as any
  form.value.datasource = v.datasource || 1
  form.value.rule = v.rule || 'and'
  form.value.fields = v.fields
}
// 获取数据源
function getDatasetList() {
  getDatasourcesListApi().then((res) => {
    const response = res as { data: Database[] }
    connectionOptions.value = response?.data || []
  })
}
// 获取数据源下的表
async function changeDataSource() {
  tableList.value = []
  const response = await getDatasourceTableListApi(Number(form.value.datasource))
  tableList.value = (response as any).data
}
// 选择表事件
async function changeTableData(item: any) {
  item.field = '';
  item.fieldList = [];
}
// 获取表下字段
async function onFieldDropdownVisible(visible: boolean, item: any) {
  if (visible && item.table) {
    // mask 模式表id 可能是 id，forbid 模式是 table_name
    let tableId = item.table;
    if (typeof tableId !== 'number') {
      const table = tableList.value.find((t:any) => t.table_name === item.table);
      if (!table) return;
      tableId = table.id;
    }
    const response = await getDatasourceTableFieldListApi(Number(tableId));
    const data = (response as any).data;
    if (data) {
      item.fieldList = data.map((field: any) => ({
        fieldId: field.id,
        alias: field.alias || field.field || '未知字段',
        field:field.field,
        fieldType: field.data_type || 'string',
      }));
    } else {
      item.fieldList = [];
    }
  }
}
// 选择字段事件
function onColumnChange(item: any, val: string, table: string) {
  const col = fieldList.value.find(c => c.fieldId == val);
}
function addRow() {
  form.value.fields.push({ table: '', field: '', condition: 'eq', attribute: [], fieldList: [], attributeList: [] });
}
function removeField(idx: number) {
  if (form.value.fields.length > 0) {
    form.value.fields.splice(idx, 1);
  }
}
function saveEvent(){
  for(const [idx, item] of form.value.fields.entries()){
    if(!item.table || !item.field){
      ElMessage.error(`第${idx+1}行表和字段必填`);
      return;
    }
  }
  form.value.fields.forEach((val:any)=>{
    delete val.attributeList
    delete val.fieldList
  })
  // 校验通过
  return form.value
}
defineExpose({ saveEvent });
// 获取字段属性（枚举）
async function onAttributeDropdownVisible(visible: boolean, item: any) {
  if (visible && item.field) {
    // 字段id为 fieldId
    let fieldId = item.field;
    // 如果 fieldId 不是数字，尝试通过 fieldList 查找
    if (typeof fieldId !== 'number') {
      const field = item.fieldList.find((f:any) => f.alias === item.field);
      if (!field) return;
      fieldId = field.fieldId;
    }
    const response = await getDatasourceTableFieldEnumApi(Number(fieldId));
    const data = (response as any).data;
    if (data) {
      item.attributeList = data;
    } else {
      item.attributeList = [];
    }
  }
}
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 18px;

  &.fields-list {
    :deep(.el-form-item__content) {
      display: block;
    }
  }
}

.data-select {
  width: 300px;
}

.field-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .left-row {
    display: flex;
    flex: 1;
    white-space: nowrap;
    align-items: center;

    .table-width {
      width: 130px;
      margin-right: 8px;
    }

    .table-select {
      width: 100%
    }

    .column-select {
      width: 130px;
    }

    .mask-select {
      flex: 1;
      margin-right: 8px;
    }
  }
}

.column-select {
  margin-right: 8px;
}

.field-type {
  display: flex;
  align-items: center;
  margin-right: 8px;
  min-width: 60px;
  color: #888;
}

.type-icon {
  margin-right: 2px;
}

.perm-checkbox {
  margin-right: 8px;
}

.add-field-btn {
  color: #1677ff;
  margin-right: 8px;
  font-size: 14px;
  padding: 0 4px;
}

.add-row-btn {
  margin-top: 4px;
}
</style>
