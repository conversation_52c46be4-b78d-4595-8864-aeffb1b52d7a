<template>
  <el-dialog
    v-model="dialogVisible"
    title="日期筛选器"
    width="650px"
    :close-on-click-modal="false"
    @closed="handleClose"
  >
    <div class="date-picker-container">
      <el-alert type="success" show-icon :closable="false">
        <span>选择时间: {{ getDisplayTime }}</span>
      </el-alert>

      <el-tabs v-model="rangeType" @tab-click="handleTabChange">
        <el-tab-pane label="快速选择" name="quick">
          <div class="quick-select">
            <!-- 动态生成时间选择区域 -->
            <div
              v-for="(section, key) in timeOptions"
              :key="key"
              class="section"
            >
              <div class="section-title">{{ section.label }}</div>
              <el-radio-group
                v-model="selectedQuick"
                @change="handleQuickSelect"
              >
                <div
                  class="radio-item"
                  v-for="item in section.children"
                  :key="item.value"
                >
                  <el-radio :key="item.value" :label="item.value">
                    {{ item.label }}
                  </el-radio>
                  <el-checkbox
                    v-if="item.includeToday && item.value === selectedQuick"
                    v-model="includeToday"
                    @change="handleQuickSelect(selectedQuick)"
                  >
                    包含今天
                  </el-checkbox>
                </div>
              </el-radio-group>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="固定时间" name="fixed">
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            ref="fixedDateRangePicker"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 100%"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
  
<script setup lang="ts">
import {
  ref,
  defineProps,
  defineEmits,
  defineExpose,
  computed,
  toRaw,
  nextTick,
} from "vue";
import dayjs from "dayjs";
import { timeOptions, getTimeRange } from "./config";
import { ElAlert } from "element-plus";

const props = defineProps({
  initialValue: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:visible", "confirm", "cancel"]);

// 弹窗是否显示
const dialogVisible = ref<boolean>(false);
// 当前选中的tab
const rangeType = ref("quick");
// 快速选择
const selectedQuick = ref("");
// 包含今天
const includeToday = ref(true);
// 固定时间
const dateRange = ref<[Date, Date] | null>(null);
// 固定时间选择器
const fixedDateRangePicker = ref<any>(null);
// format格式
const format = ref<string>("YYYY:MM:DD   HH:mm");
// 显示时间
const getDisplayTime = computed(() => {
  const [start, end] = toRaw(dateRange.value) || [];
  if (selectedQuick.value === "all") {
    return "全部时间";
  } else if (start && end) {
    return dayjs(start).format(format.value) + "   -   " + dayjs(end).format(format.value);
  }
  return "";
});

// -----------------------------------------------------------

// 处理快速选择
const handleQuickSelect = (value: string) => {
  let options = {
    includeToday: includeToday.value,
  };
  const range = getTimeRange(value, options);
  if (range) {
    dateRange.value = range;
  }
};

// 确认选择
const handleConfirm = () => {
  // 移除这些代码
  let [min, max] = toRaw(dateRange.value) || [];
  const result: any = {
    rangeType: rangeType.value,
    selectedQuick: selectedQuick.value,
    showText: getDisplayTime.value,
  };
  if (min && selectedQuick.value !== "all") {
    result.min = {
      value:`'${dayjs(min).format(format.value)}'`,
      include: true,
    };
  }
  if (max && selectedQuick.value !== "all") {
    result.max = {
      value:`'${dayjs(max).format(format.value)}'`,
      include: true,
    };
  }
  emit("confirm", result);
  dialogVisible.value = false;
};

// 取消选择
const handleCancel = () => {
  emit("cancel");
  dialogVisible.value = false;
};

// 弹窗关闭
const handleClose = () => {
  rangeType.value = "quick";
  selectedQuick.value = "";
  dateRange.value = null;
};

// 打开弹窗
const open = (params: any = {}) => {
  dialogVisible.value = true;
  const {
    rangeType: pRangeType = "quick",
    selectedQuick: pSelectedQuick = "",
    min,
    max,
  } = params;
  rangeType.value = pRangeType;
  selectedQuick.value = pSelectedQuick;
  if (pSelectedQuick !== "all" && min.value && max.value) {
    dateRange.value = [dayjs(min.value, format.value).toDate(),
    dayjs(max.value, format.value).toDate()];
  } else {
    dateRange.value = null;
  }
};

// 切换tab
const handleTabChange = ({ paneName }: any) => {
  //   if (rangeType.value === "quick") {
  dateRange.value = null;
  selectedQuick.value = "";
  if (paneName === "fixed") {
    nextTick(() => {
      fixedDateRangePicker.value.handleOpen();
    });
  }
  //   }
};

defineExpose({
  open,
});
</script>
  
  <style lang="scss" scoped>
.date-picker-container {
  :deep(.el-tabs__content) {
    padding: 5px 15px;
  }
  .section {
    margin-bottom: 16px;
    display: flex;
    gap: 20px;

    .section-title {
      font-weight: bold;
      width: 50px;
      //   margin-bottom: 8px;
      color: #606266;
    }

    :deep(.el-radio-group) {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 8px;
      row-gap: 20px;
      flex: 1;
      .radio-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
      .el-radio,
      .el-checkbox {
        // 使用 :deep 来确保样式能够作用到内部元素
        height: max-content !important;
        margin-right: 0; // 移除默认的右边距
        display: flex;
        align-items: center; // 垂直居中
      }
    }
  }
}

.dialog-footer {
  margin-top: 16px;
  text-align: right;
}
</style>