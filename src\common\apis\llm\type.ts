export interface CreateModelRequestData {
  // 模型地址
  "host": string,
  "port": number,
  "model": string,
  "worker_type": 'llm' | "text2vec" | "reranker",
  "params": Record<string, any>,
}

export interface DeleteModelRequestData extends CreateModelRequestData {
  delete_after: boolean
}

export interface GetLLMDetailRequestData {
  model: string
  worker_type: 'llm' | "text2vec" | "reranker"
  healthy: boolean | string
}

/**
 * @param checkbox 展示checkbox的params
 * @param allDis 新增全部不处理，编辑时 根据allDis 和 editDis 判断是否禁用
 * @param editDis 编辑时禁用的params
 * @param row 独占一行的params
 * @param labelAlias 特殊labelAlias
 */
export interface LLMSpecialParamsConfig {
  checkbox: string[],
  allDis: string[],
  editDis: string[],
  row: string[],
  labelAlias: Record<string, string>
}

export namespace LLM {

  export interface Model {
    check_healthy: boolean,
    healthy: boolean,
    host: string,
    last_heartbeat: string,
    manager_host: string
    manager_port: number
    model_name: string
    port: number
    prompt_template: string | null
    worker_type: 'llm' | "text2vec" | "reranker",
     // 新增 logo 字段（可选）
     logo?: string;
  }

  export interface ModelTypeParam {
    default_value: string | null,
    description: string,
    ext_metadata: { order: number },
    is_array: boolean,
    label: string,
    nested_fields: string,
    param_class: string,
    param_name: string,
    param_order: number,
    param_type: string,
    required: boolean,
    valid_values: string,
  }

  export interface ModelTypeResponse {
    description: string,
    enabled: boolean,
    host: string,
    model: string,
    params: ModelTypeParam[],
    path: string,
    path_exists: boolean,
    port: number,
    provider: string,
    proxy: boolean,
    worker_type: 'llm' | "text2vec" | "reranker",
  }

}

export interface GetAgentListRequestData {
	page: number;
	pageSize: number;
}

export interface TableRequestData {
	/** 当前页码 */
	currentPage: number;
	/** 查询条数 */
	size: number;
	/** 查询参数：用户名 */
	username?: string;
	/** 查询参数：手机号 */
	phone?: string;
}

