import type { AxiosInstance, AxiosRequestConfig } from "axios"
import { getToken, setToken, getRefreshToken, setRefreshToken, removeToken, removeRefreshToken } from "@/common/utils/cache/cookies"
import axios from "axios"
import { get, merge } from "lodash-es"
import { useUserStore } from "@/pinia/stores/user"
import { refreshTokenApi } from "@/pages/login/apis/index"

/** 退出登录并强制刷新页面（会重定向到登录页） */
function logout() {
  useUserStore().logout()
  location.reload()
}

/** 创建请求实例 */
function createInstance() {
  // 创建一个 axios 实例命名为 instance
  const instance = axios.create()
  // 请求拦截器
  instance.interceptors.request.use(
    config => {
      // 自动携带token
      const token = getToken()
      if (token && config.headers) {
        config.headers["Authorization"] = `Bearer ${token}`
      }
      return config
    },
    error => Promise.reject(error)
  )

  // 刷新token相关变量
  let isRefreshing = false
  let requests: Function[] = []

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      // apiData 是 api 返回的数据
      const apiData = response.data
      // 二进制数据则直接返回
      const responseType = response.request?.responseType
      if (responseType === "blob" || responseType === "arraybuffer") return apiData

      // 检查是否为新格式（包含 success 字段）
      if (apiData.hasOwnProperty('success')) {
        // 新格式：使用 success 字段判断业务状态
        const success = apiData.success
        if (success === true) {
          // 业务成功，返回完整数据
          return apiData
        } else {
          // 业务失败，显示错误信息
          const errorMessage = apiData.err_msg || "请求失败"
          ElMessage.error(errorMessage)
          return Promise.reject(new Error(errorMessage))
        }
      }

      // 原有格式：使用 code 字段判断业务状态
      const code = apiData.code
      // 如果没有 code, 代表这不是项目后端开发的 api
      if (code === undefined) {
        ElMessage.error("非本系统的接口")
        return Promise.reject(new Error("非本系统的接口"))
      }
      switch (code) {
        case 0:
          // 本系统采用 code === 0 来表示没有业务错误
          return apiData
        case 401:
          // Token 过期时
          return logout()
        default:
          // 不是正确的 code
          ElMessage.error(apiData.message || "Error")
          return Promise.reject(new Error("Error"))
      }
    },
    async (error) => {
      const status = get(error, "response.status")
      const message = get(error, "response.data.message")
      const originalRequest = error.config
      if (status === 401 && getRefreshToken() && !originalRequest._retry) {
        if (isRefreshing) {
          // 正在刷新，队列等待
          return new Promise((resolve, reject) => {
            requests.push((token: string) => {
              originalRequest.headers["Authorization"] = `Bearer ${token}`
              resolve(instance(originalRequest))
            })
          })
        }
        originalRequest._retry = true
        isRefreshing = true
        try {
          const res: any = await refreshTokenApi(getRefreshToken())
          const { access_token, refresh_token } = (res && res.data) || {}
          if (access_token && refresh_token) {
            setToken(access_token)
            setRefreshToken(refresh_token)
            instance.defaults.headers.common["Authorization"] = `Bearer ${access_token}`
            requests.forEach(cb => cb(access_token))
            requests = []
            originalRequest.headers["Authorization"] = `Bearer ${access_token}`
            return instance(originalRequest)
          } else {
            throw new Error("刷新token失败")
          }
        } catch (e) {
          removeToken()
          removeRefreshToken()
          logout()
          return Promise.reject(e)
        } finally {
          isRefreshing = false
        }
      }
      let refreshToken = getRefreshToken() || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ4aWFvaHUiLCJleHAiOjE3NTE4NTMyMDB9.Ur_bTa0kvciZf8BR7nmLqtwoPfS0dy6pQKG8dyM03AE'
      switch (status) {
        case 400:
          error.message = "请求错误"
          break
        case 401:
          error.message = message || "未授权"
          logout()
          // refreshTokenApi(refreshToken)
           // 重新调用刷新token不退出登录
          break
        case 403:
          error.message = message || "拒绝访问"
          break
        case 404:
          error.message = "请求地址出错"
          break
        case 408:
          error.message = "请求超时"
          break
        case 500:
          error.message = "服务器内部错误"
          break
        case 501:
          error.message = "服务未实现"
          break
        case 502:
          error.message = "网关错误"
          break
        case 503:
          error.message = "服务不可用"
          break
        case 504:
          error.message = "网关超时"
          break
        case 505:
          error.message = "HTTP 版本不受支持"
          break
      }
      ElMessage.error(error.message)
      return Promise.reject(error)
    }
  )
  return instance
}
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  version?: string;
  api?: string;
}
/** 创建请求方法 */
function createRequest(instance: AxiosInstance) {
  return <T>(config: CustomAxiosRequestConfig): Promise<T> => {
    const token = getToken()
    const baseURL = config.version ? config.version : import.meta.env.VITE_BASE_URL
    // 默认配置
    const defaultConfig: CustomAxiosRequestConfig = {
      // 接口地址
      baseURL: baseURL,
      // 请求头
      headers: {
        // 携带 Token
        "Authorization": token ? `Bearer ${token}` : undefined,
        "Content-Type": "application/json"
      },
      // 请求体
      data: {},
      // 请求超时
      timeout: 120000,
      // 跨域请求时是否携带 Cookies
      withCredentials: false
    }
    // 将默认配置 defaultConfig 和传入的自定义配置 config 进行合并成为 mergeConfig
    const mergeConfig = merge(defaultConfig, config)
    return instance(mergeConfig)
  }
}

/** 用于请求的实例 */
const instance = createInstance()

/** 用于请求的方法 */
export const request = createRequest(instance)
