<script setup>
import { Smooth<PERSON>tepEdge, <PERSON><PERSON>abel<PERSON><PERSON>, getSmoothStepPath, useVueFlow } from '@vue-flow/core';
// import { computed } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  sourceX: {
    type: Number,
    required: true,
  },
  sourceY: {
    type: Number,
    required: true,
  },
  targetX: {
    type: Number,
    required: true,
  },
  targetY: {
    type: Number,
    required: true,
  },
  sourcePosition: {
    type: String,
    required: true,
  },
  targetPosition: {
    type: String,
    required: true,
  },
  markerEnd: {
    type: String,
    required: false,
  },
  style: {
    type: Object,
    required: false,
    default: () => ({ 'stroke-dasharray': '3,2', stroke: '#005EE0' }),
  },
})


const path = computed(() => getSmoothStepPath(props))
</script>

<script>
export default {
  inheritAttrs: false,
}
</script>

<template>
  <SmoothStepEdge :id="id" :style="style" :path="path[0]" :marker-end="markerEnd" />
</template>