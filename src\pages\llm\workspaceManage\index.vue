<template>
  <div class="workspace-container">
    <!-- 页面头部 -->
    <div class="workspace-header">
      <h2>工作空间管理</h2>
    </div>

    <!-- 搜索栏 -->
    <div class="search-section">
      <el-input v-model="searchKeyword" placeholder="输入关键字" class="search-input" clearable>
        <template #suffix>
          <el-icon class="search-icon">
            <Search />
          </el-icon>
        </template>
      </el-input>
      <el-button type="primary" @click="handleAddWorkspace" v-permission="['workspace:create']">
        <el-icon>
          <Plus />
        </el-icon>
        新增工作空间
      </el-button>
    </div>
    <!-- 工作空间卡片网格 -->
    <div class="workspace-grid">
      <div v-for="workspace in filteredWorkspaces" :key="workspace.id" class="workspace-card">
        <div class="card-header">
          <h3 class="workspace-name" @click="handleCardClick(workspace)">{{ workspace.workspace_name }}</h3>
          <el-tag v-if="workspace.is_default" type="primary" class="default-tag"
            @click.stop="handleSetDefault(workspace, false)">
            取消默认
          </el-tag>
          <el-tag v-else type="info" class="set-default-tag"
            @click.stop="handleSetDefault(workspace, true)">
            设为默认
          </el-tag>
        </div>

        <div class="card-content">
          <div class="workspace-info">
            <div class="info-item">
              <span class="label">所有者：</span>
              <span class="value">{{ workspace.owner_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">新增时间：</span>
              <span class="value">{{ formatDate(workspace.created_at) }}</span>
            </div>
            <div class="info-item">
              <span class="label">修改时间：</span>
              <span class="value">{{ formatDate(workspace.updated_at) }}</span>
            </div>
          </div>

          <div class="card-footer">
            <div class="member-count">
              <el-tooltip placement="top">
                <template #content> 成员：{{ workspace.member_count }}人</template>
                <span>
                  <i class="iconfont ChatData-tuandui"></i>
                  <span class="ml-1">{{ workspace.member_count }}</span>
                </span>
              </el-tooltip>
            </div>
            <div class="card-actions">
              <!-- <el-popover placement="right"
                :visible="popoverVisible && currentTransferWorkspace && currentTransferWorkspace.id === workspace.id"
                width="300" @after-leave="handleTransferCancel">
                <div class="transfer-content">
                  <h3 style="margin: 0; color: #333;">转让空间</h3>
                  <p class="transfer-tip">把空间所有者转让给：</p>
                  <el-cascader v-model="transferTarget" :options="availableUsers" :show-all-levels="false" :props="{
                    value: 'user_id',
                    label: 'name',
                    children: 'users'
                  }" />
                  <div class="dialog-footer" style="margin-top: 16px;">
                    <el-button size="small" @click="handleTransferCancel">取消</el-button>
                    <el-button size="small" type="primary" @click="handleTransferConfirm(workspace)">确认</el-button>
                  </div>
                </div>
                <template #reference >
                  <span v-if="workspace.is_owner">
                    <el-tooltip placement="top" content="转让空间">
                      <i  class="iconfont ChatData-gerenxinxi"  @click.stop="handleTransfer(workspace)"></i>
                    </el-tooltip>
                  </span>
                </template>
              </el-popover> -->
              <el-icon @click.stop="handleDelete(workspace)" v-permission="['workspace:delete']">
                <Delete />
              </el-icon>
            </div>
          </div>
        </div>

        <!-- 删除提示 -->
        <div v-if="workspace.showDeleteTip" class="delete-tip">
          删除时，需要一次性认真删除
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter,useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search, User, Delete } from '@element-plus/icons-vue'
import { getSapceListApi, deleteSapceLApi, postSapceDefaultLApi, deleteSapceDefaultLApi, putSapceTransferLApi, getUseTreeApi } from "@/common/apis/workspace"
import { useWorkspaceStore } from '@/pinia/stores/workspace'
import dayjs from 'dayjs'
const workspaceStore = useWorkspaceStore()
interface Workspace {
  id: number
  code: string,
  workspace_name: string
  description: String,
  labels: String,
  owner_id: String,
  member_count: number,
  owner_name: string,
  is_owner: false;
  created_at: String,
  updated_at: String,
  owner: string
  memberCount: number
  is_default: boolean
  showDeleteTip?: boolean
}

interface User {
  id: number
  name: string
}

const router = useRouter()
const route = useRoute()
// 响应式数据
const searchKeyword = ref('')
const popoverVisible = ref(false)
const transferTarget = ref<number | null>(null)
const currentTransferWorkspace = ref<Workspace | null>(null)

// 模拟数据
const workspaces = ref<Workspace[]>([])

const availableUsers = ref<any>([])

// 计算属性
const filteredWorkspaces = computed(() => {
  if (!searchKeyword.value) {
    return workspaces.value
  }
  return workspaces.value.filter(workspace =>
    (workspace.workspace_name || '').toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    (workspace.owner || '').toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})


const formatDate = (dateStr: any) => {
  return dateStr ? dayjs(String(dateStr)).format('YYYY-MM-DD HH:mm:ss') : '-'
}

const handleAddWorkspace = () => {
  router.push({
    path: 'workspaceManageCreate',
    query: {
      mode: 'add',
      spaceCode:route.query.spaceCode || '',
    }
  })
}

const handleCardClick = (workspace: Workspace) => {
  router.push({
    path: 'workspaceManageCreate',
    query: {
      mode: 'edit',
      spaceCode:workspace.id,
    }
  })
}

const handleSetDefault = (workspace: Workspace, is_default: boolean) => {
  if (is_default) {
    postSapceDefaultLApi(workspace.id, is_default).then((res) => {
      ElMessage.success('设置默认工作空间成功')
      getSapceList()
    })
  } else {
    deleteSapceDefaultLApi().then((res) => {
      ElMessage.success('取消默认工作空间成功')
      getSapceList()
    })
  }
}

const handleTransfer = async (workspace: Workspace) => {
  const response = await getUseTreeApi({}) as { data?: { types: any[] } }
  let dataArry: any[] = []
  if (Array.isArray(response.data)) {
    dataArry = response.data
  } else if (response.data && Array.isArray(response.data.types)) {
    dataArry = response.data.types
  }
  dataArry.forEach((item: any) => {
    item.name = item.department
    item.children = item.users || []
  })
  availableUsers.value = dataArry || []
  currentTransferWorkspace.value = workspace
  popoverVisible.value = true
}

const handleTransferCancel = () => {
  popoverVisible.value = false
  transferTarget.value = null
  currentTransferWorkspace.value = null
}

const handleTransferConfirm = (workspace: Workspace) => {
  if (!transferTarget.value) {
    ElMessage.warning('请选择转让目标用户')
    return
  }
  // 修复：transferTarget.value 可能是 number 或数组，需兼容处理
  let newOwnerId;
  if (Array.isArray(transferTarget.value)) {
    newOwnerId = transferTarget.value[transferTarget.value.length - 1];
  } else {
    newOwnerId = transferTarget.value;
  }
  putSapceTransferLApi(workspace.id, { new_owner_id: newOwnerId }).then((res) => {
    ElMessage.success('工作空间转让成功')
    getSapceList()
  });
  handleTransferCancel()
}

const handleDelete = (workspace: Workspace) => {
  ElMessageBox.confirm(
    `确定要删除工作空间"${workspace.workspace_name}"吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    deleteSapceLApi(workspace.id).then((res) => {
      ElMessage.success("删除成功");
      if(workspace.id === Number(route.query.spaceCode)) {
        // 删除路由参数 spaceCode
        const { spaceCode, ...restQuery } = route.query
        router.replace({
          name: route.name as string,
          query: restQuery
        })
      }
      getSapceList()
    });
  }).catch(() => {
    // 用户取消删除
  })
}

onMounted(() => {
  // 初始化数据
  getSapceList()
})
async function getSapceList() {
  workspaces.value = await workspaceStore.fetchSpaces()
  // getSapceListApi().then((res) => {
  //   const response = res as { data: Workspace[] }
  //   workspaces.value = response?.data || []
  // })
}
</script>

<style scoped>
.workspace-container {
  padding: 0 20px;
  background-color: #fff;
  min-height: 100vh;
}

.workspace-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.workspace-header h2 {
  height: 48px;
  line-height: 48px;
  font-size: 1.125rem;
  margin: 0;
}

.search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.search-input {
  width: 300px;
}

.search-icon {
  color: #c0c4cc;
}

.workspace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(285px, 1fr));
  gap: 20px;
}

.workspace-card {
  background: white;
  border-radius: 8px;
  padding: 15px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.workspace-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.workspace-name {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.default-tag {
  cursor: pointer;
  background: #005EE0;
  color:#fff;
  font-weight: bold;
}

.set-default-tag {
  background: #EBF3FD;
  color:#005EE0;
  cursor: pointer;
  font-weight: bold;
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.workspace-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  font-size: 14px;
}

.label {
  color: #909399;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #606266;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.member-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #409eff;
  font-size: 14px;
  .iconfont{
    color: #4C6F88;
  }
}

.card-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  .iconfont{
    color: #4C6F88;
  }
  .el-icon {
    font-size: 18px;
    color: #4C6F88;
    margin-left: 6px;
    font-weight: bold;
  }
}

.delete-tip {
  position: absolute;
  bottom: -30px;
  left: 20px;
  right: 20px;
  background: #fef0f0;
  color: #f56c6c;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  border: 1px solid #fbc4c4;
}

.transfer-tip {
  margin-bottom: 16px;
  color: #606266;
}

.transfer-select {
  width: 100%;
}

.dialog-footer {
  display: flex;
  gap: 10px;
  padding-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .workspace-grid {
    grid-template-columns: 1fr;
  }

  .workspace-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
</style>
