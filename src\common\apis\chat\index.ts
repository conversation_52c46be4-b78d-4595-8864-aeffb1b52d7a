import type * as Agent from "./type"
import { request } from "@/http/axios"

/** 获取助手列表 */
export function getAgentListApi(data: any) {
  return request({
    url: '/app/list',
    method: "post",
    data
  })
}

/** 获取历史对话,获取对话列表 */
export function getDialogueListApi(params: Record<string, any>) {
  return request<Agent.Dialogue.DialogueListResponseData>({
    url: '/chat/dialogue/list',
    method: "get",
    params: params
  })
}
// 查看单个会话历史记录
export function getMessageDtailList(params: Record<string, any>) {
  return request<Agent.Dialogue.DialogueListResponseData>({
    url: 'chat/dialogue/messages/history',
    method: "get",
    params: params
  })
}
// 问题引导
export function QAguidance(data: Agent.GetAgentListRequestData) {
  return request({
    url: '/chat/guide-question',
    method: "post",
    data
  })
}

// 问题优化
export function QAoptimize(data: Agent.GetAgentListRequestData) {
  return request({
    url: '/chat/optimize-question',
    method: "post",
    data
  })
}

/** 删 */
export function deleteTableDataApi(id: number) {
  return request({
    url: `/tables/${id}`,
    method: "delete"
  })
}

/** 改 */
export function updateTableDataApi(data: Agent.GetAgentListRequestData) {
  return request({
    url: `/tables`,
    method: "put",
    data
  })
}
// 获取sql示例
export function getSqlExampleApi<T extends Record<string, any>>(data: T) {
  return request({
    url: '/chat/retrieve-sql',
    method: "post",
    data
  })
}

// 意图解析
export function getIntentAnalysisApi<T extends Record<string, any>>(data: T) {
  return request({
    url: '/chat/parse-intent',
    method: "post",
    data
  })
}

// 生成sql
export function generateSqlApi<T extends Record<string, any>>(data: T) {
  return request({
    url: '/chat/generate-sql',
    method: "post",
    data
  })
}

// 修正sql
export function correctSqlApi<T extends Record<string, any>>(data: T) {
  return request({
    url: '/chat/amendment-sql',
    method: "post",
    data
  })
}

// 执行sql
export function executeSqlApi<T extends Record<string, any>>(data: T) {
  return request({
    url: '/chat/sql-chart',
    method: "post",
    data
  })
}

// 存储chat-data 助手下的历史对话
export function addChatDataHistoryApi(data: Agent.StoreChatDataHistoryRequest) {
  return request({
    url: '/chat/save-history',
    method: "post",
    data
  })
}

// 删除历史对话
export function deleteHistoryApi<T extends Agent.DeleteHistoryRequest>(data: T) {
  return request({
    url: '/chat/dialogue/delete',
    method: "post",
    data
  })
}

// 获取sql 转换的json
export function getSqlToJsonApi<T extends Record<string, any>>(data: T) {
  return request({
    url: '/chat/dialogue/parse_sql_or_json',
    method: "post",
    data
  })
}

// 波动归因
export function volatilityAttributionApi<T extends Record<string, any>>(data: T) {
  return request({
    url: "/chat/attribution-analysis",
    method: "post",
    data
  })
}

// 波动归因统计sql
export function volatilityAttributionSqlApi<T extends Record<string, any>>(data: T) {
  return request({
    url: "/chat/dialogue/fluctuation_reason_sql",
    method: "post",
    data
  })
}

// 问题推荐
export function getQuestionRecommendApi<T extends Record<string, any>>(data: T) {
  return request({
    url: "/chat/recommend-questions",
    method: "post",
    data
  })
}

// 联想输入
export function getSuggestionInputApi<T extends Record<string, any>>(data: T) {
  return request({
    url: "/chat/input-suggestions",
    method: "post",
    data
  })
}