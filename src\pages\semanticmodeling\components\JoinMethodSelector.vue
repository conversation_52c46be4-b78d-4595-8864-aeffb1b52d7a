<template>
  <div class="join-method-selector">
    <div class="selector-header" :class="{ 'disabled': props.disabled }" @click="!props.disabled && toggleDropdown()">
      <span class="current-selection"><em>关联方式:</em> {{ currentSelection.label }}</span>
      <el-icon class="arrow-icon" :class="{ 'is-expanded': !isExpanded }">
        <ArrowUpBold />
      </el-icon>
    </div>

    <div v-if="isExpanded" class="dropdown-content">
      <div
        v-for="option in options"
        :key="option.value"
        class="option-item"
        :class="{ 'selected': option.value === currentSelection.value }"

      >
        <div class="option-header">
          <el-radio
            v-model="currentValue"
            :label="option.value"
            :disabled="props.disabled"
          >
            {{ option.label }}
          </el-radio>
        </div>
        <div class="option-description">
          {{ option.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ArrowUpBold } from '@element-plus/icons-vue'

interface JoinMethodOption {
  value: string
  label: string
  description: string
}

const props = defineProps<{
  modelValue?: string
  disabled?: boolean
}>()

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const isExpanded = ref(false)

const options: JoinMethodOption[] = [
  {
    value: 'explicit',
    label: '显式关联',
    description: 'SQL查询中直接使用JOIN关键字(如 INNER JOIN、LEFT JOIN 等)将多个表连接起来的方式'
  },
  {
    value: 'view',
    label: '视图封装关联',
    description: '通过预先定义的视图(View)来封装多表关联逻辑,查询时只需访问视图,注意此方式会创建一个视图表'
  }
]

const currentSelection = computed(() => {
  const selected = options.find(option => option.value === props.modelValue) || options[0]
  return selected
})

const currentValue = computed({
  get: () => props.modelValue || 'explicit',
  set: (value: string) => {
    emit('update:modelValue', value)
    isExpanded.value = false
  }
})

function toggleDropdown() {
  isExpanded.value = !isExpanded.value
}



// 点击外部关闭下拉框
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  if (!target.closest('.join-method-selector')) {
    isExpanded.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped lang="scss">
.join-method-selector {
  position: relative;
  display: inline-block;
}

.selector-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #F2F2F2;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;

  &:hover {
    background: #E8E8E8;
  }

  &.disabled {
    cursor: not-allowed;
    // opacity: 0.6;
    .current-selection {
      color:#303133;
    }
    &:hover {
      background: #F2F2F2;
    }
  }

  .current-selection {
    em {
      color: #777;
      font-style: normal;
      margin-right: 4px;
    }
  }

  .arrow-icon {
    font-size: 12px;
    transition: transform 0.2s;

    &.is-expanded {
      transform: rotate(180deg);
    }
  }
}

.dropdown-content {
  position: absolute;
  top: 100%;
  right: -50%;
  background: white;
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  min-width: 320px;
}

.option-item {
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid #F5F5F5;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: #F8F9FA;
  }

  &.selected {
    // background: #EBF3FD;
  }

  .option-header {

    :deep(.el-radio) {
      margin-right: 0;

      .el-radio__label {
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .option-description {
    font-size: 12px;
    color: #8C8C8C;
    line-height: 2;
    margin-left: 20px;
  }
}
</style>
