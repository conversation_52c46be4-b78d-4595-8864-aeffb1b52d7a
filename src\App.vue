<script lang="ts" setup>
import { useGreyAndColorWeakness } from "@@/composables/useGreyAndColorWeakness"
import { useTheme } from "@@/composables/useTheme"
// import { StagewiseToolbar } from "@stagewise/toolbar-vue"
import zhCn from "element-plus/es/locale/lang/zh-cn" // Element Plus 中文包
import { computed } from "vue"

const { initTheme } = useTheme()
const { initGreyAndColorWeakness } = useGreyAndColorWeakness()

// 初始化主题
initTheme()
// 初始化灰色模式和色弱模式
initGreyAndColorWeakness()

// stagewise 配置
const stagewiseConfig = {
  plugins: []
}

// 判断是否为开发环境
const isDev = computed(() => process.env.NODE_ENV === "development")
</script>

<template>
  <el-config-provider :locale="zhCn">
    <router-view />
    <!-- <StagewiseToolbar v-if="isDev" :config="stagewiseConfig" /> -->
  </el-config-provider>
</template>
