<script lang="ts" setup>
import type { ChatHistoryResponse } from "@@/apis/chat/type"
import { inject, nextTick, onMounted, onUnmounted, ref, watch } from "vue"
import { ElButton, ElIcon } from "element-plus"
import { ArrowUp, ArrowDown } from "@element-plus/icons-vue"
import ChatContent from "./ChatContent.vue"

// 注入的数据
const history = inject<ChatHistoryResponse>("history", [])

// 响应式数据
const scrollRef = ref<HTMLDivElement>()
const isScrollToTop = ref(false)
const showScrollButtons = ref(false)
const isAtTop = ref(true)
const isAtBottom = ref(false)

// 滚动处理函数
function handleScroll() {
  if (!scrollRef.value) return

  const container = scrollRef.value
  const scrollTop = container.scrollTop
  const scrollHeight = container.scrollHeight
  const clientHeight = container.clientHeight
  const buffer = 20 // 缓冲区

  // 检查是否在顶部
  isAtTop.value = scrollTop <= buffer

  // 检查是否在底部
  isAtBottom.value = scrollTop + clientHeight >= scrollHeight - buffer

  // 头部可见性
  if (scrollTop >= 42 + 32) {
    isScrollToTop.value = true
  } else {
    isScrollToTop.value = false
  }

  // 当内容可滚动时显示滚动按钮
  const isScrollable = scrollHeight > clientHeight
  showScrollButtons.value = isScrollable
}

// 滚动到顶部
function scrollToTop() {
  if (scrollRef.value) {
    scrollRef.value.scrollTo({
      top: 0,
      behavior: "smooth"
    })
  }
}

// 滚动到底部
function scrollToBottom() {
  if (scrollRef.value) {
    scrollRef.value.scrollTo({
      top: scrollRef.value.scrollHeight,
      behavior: "smooth"
    })
  }
}

// 监听历史记录变化，自动滚动到底部
watch(
  () => history,
  () => {
    if (!scrollRef.value) return

    const container = scrollRef.value
    const { scrollTop, scrollHeight, clientHeight } = container
    // 动态计算滚动缓冲区
    const buffer = Math.max(50, container.clientHeight * 0.2)

    // 当新消息添加时自动滚动到底部
    const isBottomPos = scrollTop + clientHeight >= scrollHeight - buffer
    if (isBottomPos) {
      nextTick(() => {
        container.scrollTo({
          top: scrollHeight - clientHeight,
          behavior: "smooth"
        })
      })
    }
  },
  { deep: true }
)

// 监听最后一条消息的内容变化
watch(
  () => history[history.length - 1]?.context,
  () => {
    if (!scrollRef.value) return

    const container = scrollRef.value
    const { scrollTop, scrollHeight, clientHeight } = container
    const buffer = Math.max(50, container.clientHeight * 0.2)

    const isBottomPos = scrollTop + clientHeight >= scrollHeight - buffer
    if (isBottomPos) {
      nextTick(() => {
        container.scrollTo({
          top: scrollHeight - clientHeight,
          behavior: "smooth"
        })
      })
    }
  }
)

onMounted(() => {
  if (scrollRef.value) {
    scrollRef.value.addEventListener("scroll", handleScroll)

    // 初始检查内容是否可滚动
    const isScrollable = scrollRef.value.scrollHeight > scrollRef.value.clientHeight
    showScrollButtons.value = isScrollable
  }
})

onUnmounted(() => {
  if (scrollRef.value) {
    scrollRef.value.removeEventListener("scroll", handleScroll)
  }
})

// 暴露scrollRef给父组件
defineExpose({
  scrollRef
})
</script>

<template>
  <div class="chat-content-container">
    <div
      ref="scrollRef"
      class="scroll-container"
    >
      <!-- 聊天头部 -->
      <div
        class="chat-header"
        :class="{ 'header-visible': isScrollToTop }"
      >
        <h2>AI 助手</h2>
      </div>

      <!-- 聊天内容 -->
      <ChatContent />
    </div>

    <!-- 滚动按钮 -->
    <div
      v-if="showScrollButtons"
      class="scroll-buttons"
    >
      <ElButton
        v-if="!isAtTop"
        circle
        class="scroll-button"
        @click="scrollToTop"
      >
        <ElIcon>
          <ArrowUp />
        </ElIcon>
      </ElButton>
      <ElButton
        v-if="!isAtBottom"
        circle
        class="scroll-button"
        @click="scrollToBottom"
      >
        <ElIcon>
          <ArrowDown />
        </ElIcon>
      </ElButton>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chat-content-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
}

.scroll-container {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  overflow-y: auto;
}

.chat-header {
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e7eb;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;

  &.header-visible {
    opacity: 1;
    transform: translateY(0);
  }

  h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
  }

  .dark & {
    background: rgba(255, 255, 255, 0.1);
    border-bottom-color: rgba(255, 255, 255, 0.2);

    h2 {
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

.scroll-buttons {
  position: absolute;
  right: 1.5rem;
  bottom: 6rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.scroll-button {
  width: 2.5rem;
  height: 2.5rem;
  background: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;

  &:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .dark & {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.85);
  }
}
</style>