<script lang="ts" setup>
import type {
  ChatHistoryResponse,
} from "@@/apis/chat/type";
import { ChatLineRound, User } from "@element-plus/icons-vue";
import { ElAvatar, ElCard, ElSkeleton } from "element-plus";
import { inject, ref } from "vue";
import ChatDataNode from "./chatDataNode.vue";

// 注入的数据
const history = inject<ChatHistoryResponse>("history", []);
const selectedAgent = inject<any>("selectedAgent", {})

// 获取思考文本
function getThinkingText(message: any): string {
  return message.tipAlias || 'AI正在思考中...'
}

// 格式化时间
function formatTime(timestamp: number | string | null): string {
  if (!timestamp) return "";
  const date = new Date(
    typeof timestamp === "string" ? Number.parseInt(timestamp) : timestamp
  );
  return date.toLocaleTimeString();
}

// 判断是否为用户消息
function isUserMessage(role: string): boolean {
  return role === "human";
}

// 判断是否为AI消息
function isAIMessage(role: string): boolean {
  return role === "view" || role === "ai";
}

// 渲染消息内容
function renderMessageContent(content: string): string {
  if (!content) return "";

  // 预处理内容
  content = content
    // 处理各种换行符
    .replace(/\\n/g, '\n')
    .replace(/\r\n|\r/g, '\n')
    // 处理连续的换行
    .replace(/\n\s*\n/g, '\n\n')
    // 处理HTML实体
    .replace(/&nbsp;/g, ' ')
    // 最后统一转换为<br>
    .replace(/\n/g, '<br>')
    // 处理多余的<br>
    .replace(/<br>\s*<br>/g, '<br><br>');

  return renderGuideTag(content);
}
function renderGuideTag(content: string): string {
  if (!content) return "";

  // 处理引导返回的内容 替换 <ques> 标签内的内容
  return content.replace(
    /<ques>([\s\S]*?)<\/ques>/g,
    (match, questions: string) => {
      // 处理每个数字编号的问题
      return (
        "<ques>" +
        questions.replace(/\d+\.\s*(.*?)(?=\d+\.|$)/g, (_, question) => {
          // 去除首尾空格
          question = question.trim();
          if (!question) return "";
          // 返回包装后的 a 标签
          return `<a class="suggest-question" onclick="handleQuestionClick('${question}')" data-question="${question}">${question}</a><br>`;
        }) +
        "</ques>"
      );
    }
  );
}

function handleQuestionClick(e: MouseEvent): void {
  const target = e.target as HTMLElement;
  // 判断是否点击了引导问题链接
  if (target.classList.contains("suggest-question")) {
    const question = target.getAttribute("data-question")?.replace(/<br>/g, "");
    question && (window as any).setInputValue(question);
  }
}
function handleClickEvent(item: any) {
  const question = item.question;
  question && (window as any).setInputValue(question);
}
</script>

<template>
  <div class="chat-content" :class="!history.length && 'no-chat-hisotry'">
    <div v-if="history.length === 0" class="empty-state">
      <div class="empty-icon">
        <ChatLineRound />
      </div>
      <div v-if="selectedAgent?.team_context">
        <h3>{{selectedAgent?.team_context?.scene_name}}</h3>
        <p>{{selectedAgent?.team_context?.scene_describe}}</p>
      </div>
      <div v-else>
        <h3>欢迎使用AI助手</h3>
        <p>请输入您的问题，我将为您提供帮助</p>
      </div>
      <div v-if="selectedAgent?.recommend_questions?.length" style="width: 100%;" class="mt10">
        <el-row :gutter="20" class="recommend-questions-cards">
          <el-col style="padding: 10px;"
              v-for="(item,idex) in selectedAgent.recommend_questions"
              :key="idex"
              :xs="12"
              :sm="8"
              :md="8"
              :lg="8"
              :xl="8"
            >
            <el-card class="item-card" shadow="hover" @click="handleClickEvent(item)">
              <!-- <template #header> -->
                <div class="card-header">
                  <span class="item-name">{{ item.question }}</span>
                </div>
              <!-- </template> -->
              <!-- <div class="item-description">
                {{ item.description || '暂无描述' }}
              </div> -->
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <div v-else class="message-list">
      <div
        v-for="(message, index) in history"
        :key="index"
        class="message-item"
        :class="{
          'user-message': isUserMessage(message.role),
          'ai-message': isAIMessage(message.role),
        }"
      >
        <div class="message-wrapper">
          <!-- 头像 -->
          <div class="avatar-wrapper">
            <ElAvatar :size="40" class="message-avatar">
              <User v-if="isUserMessage(message.role)" />
              <ChatLineRound v-else />
            </ElAvatar>
          </div>

          <!-- 消息内容 -->
          <div class="message-content">
            <div class="message-header">
              <span class="sender-name">
                {{ isUserMessage(message.role) ? "您" : "AI助手" }}
              </span>
              <span class="message-time">
                {{ formatTime(message.time_stamp) }}
              </span>
            </div>

            <ElCard
              class="message-card"
              :class="{
                'user-card': isUserMessage(message.role),
                'ai-card': isAIMessage(message.role),
              }"
              shadow="never"
            >
              <!-- 思考中状态 -->
              <div v-if="message.thinking" class="thinking-state">
                <ElSkeleton :rows="2" animated />
                <p class="thinking-text">{{getThinkingText(message)}}</p>
              </div>

              <!-- 消息内容 -->
              <template v-else>
                <div
                  v-if="typeof message.context === 'string'"
                  class="message-text"
                  v-html="renderMessageContent(message.context)"
                  @click="handleQuestionClick"
                ></div>
                <div v-else>
                  <ChatDataNode :data="message" :index="index"></ChatDataNode>
                </div>
              </template>
            </ElCard>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.chat-content {
  padding: 1rem 10rem;
  min-height: calc(100% - 63px);
}
.no-chat-hisotry {
  padding: 1rem calc(10rem + 50px)
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 58vh;
  text-align: center;
  color: #6b7280;

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  h3 {
    margin: 0 0 1rem 0rem;
    font-size: 2.5rem;
    font-weight: 600;
    color:#222;
  }

  p {
    margin: 0;
    font-size: 1rem;
  }
}

.message-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.message-item {
  display: flex;
  width: 100%;

  &.user-message {
    justify-content: flex-end;

    .message-wrapper {
      flex-direction: row-reverse;
    }

    .message-content {
      align-items: flex-end;
    }

    .message-header {
      text-align: right;
    }
  }

  &.ai-message {
    justify-content: flex-start;
  }
}

.message-wrapper {
  display: flex;
  max-width: 80%;
  gap: 0.75rem;
}

.avatar-wrapper {
  flex-shrink: 0;
}

.message-avatar {
  background: #f3f4f6;
  color: #6b7280;

  .user-message & {
    background: #3b82f6;
    color: white;
  }

  .ai-message & {
    background: #10b981;
    color: white;
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.sender-name {
  font-weight: 500;
}

.message-time {
  font-size: 0.75rem;
}

.message-card {
  border: none;

  &.user-card {
    background: #3b82f6 !important;
    color: white;

    :deep(.el-card__body) {
      padding: 0.75rem 1rem;
    }
  }

  &.ai-card {

    border: 1px solid #F2F2F2;

    :deep(.el-card__body) {
      padding: 0.75rem 1rem;
      background: #FCFCFC;
    }

    .dark & {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: rgba(255, 255, 255, 0.85);
    }
  }
}

.thinking-state {
  .thinking-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
    font-style: italic;
  }
}

.message-text {
  line-height: 1.6;
  word-wrap: break-word;
  :deep(a) {
    color: blue;
    text-decoration: underline;
    cursor: pointer;
  }

  :deep(br) {
    line-height: 1.6;
  }
}
.recommend-questions-cards {
  :deep(.el-card) {
    height: 75px;
    font-size: 14px;
    cursor: pointer;
    text-align: left;
  }
}
</style>
