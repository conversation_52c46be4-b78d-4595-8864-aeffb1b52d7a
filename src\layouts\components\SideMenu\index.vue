<script lang="ts" setup>
  const route = useRoute()
  const router = useRouter()
  const isExpand = ref(true);
  const activeIndex = ref('')
  const refreshFlag = ref(true)
  const SideMenuList = computed(() => {
    const matchs = route.matched;
    let parentPath = matchs.length > 1 && matchs[1].path;
    let curMenu = matchs.filter((item) => {
      return item.path === parentPath && parentPath !== '/';
    });
    if(curMenu && curMenu.length) {
      return curMenu[0].children;
    }
    return []; // 默认返回空数组，避免 undefined
  })
  const defaultOpeneds = computed(() => {
    let defaultOpenedArray = []
      SideMenuList.value.forEach((item) => {
        defaultOpenedArray.push(item.path)
      });
    return defaultOpenedArray
  })
  watch(defaultOpeneds, (value, oldValue) => {
    if (value !== oldValue) {
      refreshFlag.value = false
      nextTick(() => {
        refreshFlag.value = true
      })

    }
  })
  function handleToggleSidebar() {
    isExpand.value = !isExpand.value
  }
  function selectMenu(item: any) {
    activeIndex.value = item.path
    router.push({ name: item.name, query: {
        spaceCode: route.query.spaceCode || ''
      }
    })
  }
</script>

<template>
  <el-aside :width="isExpand ? '260px' : '50px'" class="chat-sider">
    <div class="sider-content">
      <!-- 标题 -->
      <div class="sidebar-header">
        <h3 v-if="isExpand">{{ $route.meta.groupTitle || $route.meta.title }}</h3>
        <el-icon class="iconfont" @click="handleToggleSidebar"><Fold v-if="isExpand" /><Expand v-else /></el-icon>
      </div>
      <!-- 左侧菜单内容 -->
      <el-menu :default-active="activeIndex"  v-if="refreshFlag" :default-openeds="defaultOpeneds">
        <template v-for="(item, index) in SideMenuList" :key="index">
          <!-- 如果是分组（假设 children 字段标识分组） -->
          <el-sub-menu v-if="item.children && item.children.length" :index="item.path">
            <template #title>
              <i class="iconfont mr2" :class="item.meta?.icon"></i>
              <span>{{ item.meta?.title }}</span>
            </template>
            <el-menu-item-group>
              <el-menu-item
                v-for="(subItem, subIndex) in item.children"
                :key="subIndex"
                :index="subItem.path"
                :class="{
                  'active-menu': $route.path.indexOf(`${subItem.path}`) !== -1
                }"
                @click="selectMenu(subItem)"
                v-show="!(subItem.meta?.hide)"
              >
                <i class="iconfont mr2" :class="subItem.meta?.icon"></i>
                {{ subItem.meta?.title }}
              </el-menu-item>
            </el-menu-item-group>
          </el-sub-menu>
          <!-- 如果是普通菜单项 -->
          <el-menu-item
            v-else
            :index="item.path"
            :class="{
              'active-menu': $route.path.indexOf(`${item.path}`) !== -1
            }"
            @click="selectMenu(item)"
            v-show="!(item.meta?.hide)"
          >
            <i class="iconfont mr2" :class="item.meta?.icon"></i>
            {{ item.meta?.title }}
          </el-menu-item>
        </template>
      </el-menu>
    </div>
  </el-aside>
</template>

<style lang="scss" scoped>
.sider-content {
  padding: 0 10px;
  height: 100%;
  background: #FCFCFC;
  border-right: 1px solid #e5e7eb;
  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    border-bottom: 1px solid #e4e7ed;
    padding: 0 8px;
    .iconfont {
      font-size: 20px;
      cursor: pointer;
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;

      .dark & {
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }
  .el-menu {
    border-right:none;
    margin-top: 5px;
    :deep(.el-sub-menu__title) {
      padding-left: 10px !important;
      font-weight: bold;
      font-size: 14px;
      color: #00416f;
      height: auto;
      line-height: 30px;
      padding: 4px 0 0 15px;
      word-break: break-all;
      &:hover {
        background: none;
      }
    }
    :deep(.el-menu-item-group__title) {
      display: none;
    }
  }
  .el-menu-item {
    height: 36px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding:0 15px !important;
    margin: 0 !important;
    &.active-menu {
      background: #EBF3FD;
      color: #005EE0;
      font-weight: bold;
      i {
        font-weight: normal;
      }
      &:after {
        position: absolute;
        content: "";
        left: 0;
        top: 25%;
        width: 3px;
        height: 50%;
        background: #005EE0;
        border-radius: 0 5px 5px 0;
      }
    }
  }

}
</style>
