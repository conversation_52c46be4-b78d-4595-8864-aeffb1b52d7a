<script lang="ts" setup>
import { ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import ModelDrawer from "./components/ModelDrawer.vue";
import ModelTree from "./components/ModelTree.vue";
import ChartDiagram from "./chartDiagram/index.vue";
import FieldList from "./components/FieldList.vue";
import { ArrowUpBold, ArrowDownBold } from "@element-plus/icons-vue";
import { useRoute } from "vue-router";
import { get } from "lodash-es";
import {
  getSemanticModelTreeApi,
  createSemanticFolderApi,
  createSemanticModelApi,
  deleteSemanticModelApi,
  updateSemanticModelApi,
  getSemanticModelApi,
  updateSemanticModelAttributionApi,
  updateSemanticFolderApi,
  cloneSemanticModelApi,
} from "@/common/apis/semantic";
import ModelFolderDialog from "./components/ModelFolderDialog.vue";
import MoveModelDialog from "./components/MoveModelDialog.vue";
import { transformFieldData } from "./config";
import { useRouter } from "vue-router";
import dayjs from "dayjs";
import JoinMethodSelector from "./components/JoinMethodSelector.vue";
const router = useRouter();

// 字段区高度拖拽相关
const fieldListHeight = ref(300); // px
const dragging = ref(false);
const startY = ref(0);
const startHeight = ref(0);
const minHeight = 300;
const maxHeight = 1000;
const mainRef = ref();
const chartDiagramRef = ref();

function handleDragStart(e) {
  dragging.value = true;
  startY.value = e.clientY;
  startHeight.value = fieldListHeight.value;
  document.body.style.cursor = "ns-resize";
  window.addEventListener("mousemove", handleDragMove);
  window.addEventListener("mouseup", handleDragEnd);
}
function handleDragMove(e) {
  if (!dragging.value) return;
  let newHeight = startHeight.value + (startY.value - e.clientY);
  newHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
  fieldListHeight.value = newHeight;
}
function handleDragEnd() {
  dragging.value = false;
  document.body.style.cursor = "";
  window.removeEventListener("mousemove", handleDragMove);
  window.removeEventListener("mouseup", handleDragEnd);
}

function toggleFieldListHeight() {
  if (fieldListHeight.value < 900) {
    fieldListHeight.value = 1000;
  } else {
    fieldListHeight.value = 300;
  }
}
const flag = ref(true);
const isExpand = ref(true);
const addVisible = ref(false);
const Keyword = ref("");
const currentNode = ref<any>({});

const attributionRows = reactive([
  {
    measure: "",      // 指标（如销售额）
    dimensions: [],  // 维度（如地区,类别...），多选时为数组
  }
]);

function addAttributionRow() {
  attributionRows.push({
    measure: "",
    dimensions: [],
  });
}

function removeAttributionRow(index: number) {
  if (attributionRows.length > 1) {
    attributionRows.splice(index, 1);
  }
}

const handleToggleSidebar = () => {
  isExpand.value = !isExpand.value;
};
onMounted(() => {
  getTreeData();
});
function getTreeData() {
  getSemanticModelTreeApi({
    workspace_id: route.query.spaceCode || "",
    name_filter:Keyword.value,
  }).then((res) => {
    flag.value = false;
    nextTick(() => {
      treeData.value = get(res, "data", []);
      if (!route.query.id) {
        // 获取第一个子节点
        const firstWithChildren = treeData.value.find(
          (item) => Array.isArray(item.children) && item.children.length > 0
        );
        const firstChild = firstWithChildren?.children?.[0];
        currentNode.value = firstChild;
        handleNodeClick(firstChild);
      } else {
        // 在treeData的children中查找与route.query.id相同的id数据
        let foundNode = null;
        for (const folder of treeData.value) {
          if (Array.isArray(folder.children)) {
            foundNode = folder.children.find(
              (child) => String(child.id) === String(route.query.id)
            );
            if (foundNode) break;
          }
        }
        if(!foundNode) {
          const firstWithChildren = treeData.value.find(
          (item) => Array.isArray(item.children) && item.children.length > 0
        );
          foundNode = firstWithChildren?.children?.[0];
        }
        currentNode.value = foundNode;
        if(currentNode.value?.id) {
          handleNodeClick({ id: currentNode.value.id });
        }
      }
      flag.value = true;
    });
  });
}
const handleNodeClick = async (nodeData: any) => {
  const response = await getSemanticModelApi(Number(nodeData.id));
  const data = (response as any)?.data || {};
  currentNode.value = data;
  let { dimensions, measures, tables, graph, attribution_config } = data;
  dimensionData.value = [...transformFieldData(dimensions, tables)];
  measureData.value = [...transformFieldData(measures, tables)];
  rawDimensionData.value = dimensions;
  rawMeasureData.value = measures;

  // 回填 attribution_config
  if (Array.isArray(attribution_config) && attribution_config.length > 0) {
    // 先清空原有的
    attributionRows.splice(0, attributionRows.length, ...attribution_config.map(item => ({
      measure: item.measure,
      dimensions: Array.isArray(item.dimensions) ? [...item.dimensions] : [],
    })));
  } else {
    // 没有配置时，重置为初始一行
    attributionRows.splice(0, attributionRows.length, { measure: "", dimensions: [] });
  }

  nextTick(() => {
    let { nodes = [], edges = [] } = graph;
    // if (!!nodes && !!edges) {
      chartDiagramRef.value.resetFlowChart(nodes, edges);
    // }
  });

  setRouteQuery(nodeData.id);
};
function setRouteQuery(nodeId: any) {
  router.replace({
    query: {
      ...route.query,
      id: nodeId,
    },
  });
}

const folderDialogVisible = ref(false);
const folderDialogType = ref<"add" | "edit">("add");
const folderDialogData = ref<any>({});

// 移动对话框相关
const moveDialogVisible = ref(false);
const moveTargetNode = ref<any>(null);

function openAddFolderDialog() {
  folderDialogType.value = "add";
  folderDialogData.value = {};
  folderDialogVisible.value = true;
}
function openEditFolderDialog(data: any) {
  folderDialogType.value = "edit";
  folderDialogData.value = { ...data };
  folderDialogVisible.value = true;
}

async function handleFolderDialogSuccess(form: any) {
  if (folderDialogType.value === "add") {
    await createSemanticFolderApi({
      workspace_id: route.query.spaceCode || "",
      name: form.name,
      description: form.description,
    }).then((res: any) => {
      if (res.success) {
        ElMessage.success("创建成功");
      }
    });
  } else if (folderDialogType.value === "edit") {
    await updateSemanticFolderApi(folderDialogData.value.id, {
      parent_id: 0,
      name: form.name,
      description: form.description,
    }).then((res: any) => {
      if (res.success) {
        ElMessage.success("编辑成功");
      }
    });
  }
  getTreeData();
}

async function handleMoveDialogSuccess(data: any) {
  try {
    await updateSemanticModelApi(moveTargetNode.value.id, {
      parent_id: data.targetFolderId
    });
    ElMessage.success("移动成功");
    getTreeData();
  } catch (error) {
    ElMessage.error("移动失败");
  }
}

const handleNodeCommand = (command: string, data: any) => {
  if (command === "add") {
    openAddModelDrawer(data);
  } else if (command === "edit") {
    openEditFolderDialog(data);
  } else if (command === "delete") {
    openDeleteModelDrawer("folder", data);
  } else if (command === "cancelCollect") {
    openCancelCollectModelDrawer(data);
  } else if (command === "cloneNode") {
    openCloneModelDrawer(data);
  } else if (command === "moveNode") {
    openMoveModelDrawer(data);
  } else if (command === "editNode") {
    openEditModelDrawer(data);
  } else if (command === "deleteNode") {
    openDeleteModelDrawer("model", data);
  }
};
function openDeleteModelDrawer(type: String, data: any) {
  ElMessageBox.confirm(
    `确定删除该${type === "folder" ? "数据主题" : "数据模型"}吗？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }
  ).then(() => {
    deleteSemanticModelApi(data.id)
      .then((res: any) => {
        if (String(data.id) === String(route.query.id)) {
          // 删除 route.query.id 参数
          const { id, ...restQuery } = route.query;
          router.replace({ query: restQuery });
        }
        getTreeData();
        ElMessage.success("删除成功");
      })
      .catch((err: any) => {
      });
  });
}
async function openCancelCollectModelDrawer(data:any) {
  await updateSemanticModelApi(data.id, {popular: !data.popular}).then((res: any) => {
    if (res.success) {
      ElMessage.success(res.data.popular ? "收藏成功" : '取消收藏成功');
      getTreeData()
    }
  });
}
async function openCloneModelDrawer(data:any) {
  await cloneSemanticModelApi(data.id).then((res: any) => {
    if (res.success) {
      ElMessage.success('克隆成功');
      getTreeData();
    }
  });
}
function openMoveModelDrawer(data: any) {
  moveTargetNode.value = data;
  moveDialogVisible.value = true;
}
function openEditModelDrawer(data: any) {
  modelDrawerType.value = "edit";
  modelDrawerVisible.value = true;
  ModelData.value = data;
}

const modelDrawerVisible = ref(false);
const modelDrawerType = ref<"add" | "edit">("add");
const fromId = ref<any>('')
const ModelData = ref<any>({});
function openAddModelDrawer(data:any) {
  modelDrawerType.value = "add";
  fromId.value = data.id || ''
  modelDrawerVisible.value = true;
  ModelData.value = {};
}

async function handleDrawerSuccess(form: any) {
  if (modelDrawerType.value === "add") {
    await createSemanticModelApi({
      workspace_id: route.query.spaceCode || "",
      ...form,
    }).then((res: any) => {
      if (res.success) {
        ElMessage.success("创建成功");
      }
    });
  } else if (modelDrawerType.value === "edit") {
    await updateSemanticModelApi(ModelData.value.id, form).then((res: any) => {
      if (res.success) {
        ElMessage.success("编辑成功");
      }
    });
  }
  getTreeData();
}

const treeData = ref<any[]>([]);
const dimensionData = ref<any[]>([]);
const measureData = ref<any[]>([]);
const rawDimensionData = ref<any[]>([]);
const rawMeasureData = ref<any[]>([]);
const route = useRoute();
function goEdit() {
  // 新窗口打开
  router.push({
    name: "SemanticModelingEdit",
    query: route.query,
  });
  // window.open(
  //   `${window.location.origin}/#/semanticmodelingEdit?id=${currentNode.value.id}&spaceCode=${route.query.spaceCode}`,
  //   '_blank'
  // );
}
const formatDate = (dateStr: any) => {
  return dateStr ? dayjs(String(dateStr)).format("YYYY-MM-DD HH:mm:ss") : "-";
};
const attributionVisible = ref(false);
const handleSaveAttribution = () => {
  updateSemanticModelAttributionApi(currentNode.value.id, {
    attribution_config: attributionRows,
  }).then((res: any) => {
    if (res.success) {
      ElMessage.success("保存成功");
    }
  });
};
</script>

<template>
  <el-container class="semantic-modeling-layout">
    <!-- 侧边栏 -->
    <el-aside
      :width="isExpand ? '260px' : '50px'"
      class="semantic-modeling-sider"
    >
      <div class="sider-content">
        <div class="sidebar-header">
          <h3 v-if="isExpand">语义建模</h3>
          <el-icon class="iconfont" @click="handleToggleSidebar"
            ><Fold v-if="isExpand" /><Expand v-else
          /></el-icon>
        </div>
        <div v-if="isExpand" class="search-add">
          <el-input
            placeholder="输入关键字..."
            prefix-icon="Search"
            v-model="Keyword"
            @clear="getTreeData"
            @keyup.enter="getTreeData"
            clearable
          />
          <el-popover
            placement="bottom-start"
            :width="100"
            trigger="click"
            v-model:visible="addVisible"
          >
            <template #reference>
              <el-button v-if="GetPermission(['data_subject:create']) || GetPermission(['data_model:create'])" class="add-btn" type="primary" :icon="Plus" circle />
            </template>
            <div class="menu-popover">
              <div class="menu-item" @click="openAddFolderDialog" v-permission="['data_subject:create']">
                新增数据主题
              </div>
              <div class="menu-item" @click="openAddModelDrawer"  v-permission="['data_model:create']">
                新增数据模型
              </div>
            </div>
          </el-popover>
        </div>
        <div class="model-tree">
          <modelTree
            v-if="flag"
            :data="treeData"
            :currentNode="currentNode"
            @nodeClick="handleNodeClick"
            @nodeCommand="handleNodeCommand"
          />
        </div>
      </div>
    </el-aside>
    <el-main class="main-layout" ref="mainRef">
      <div class="semantic-content-layout" v-if="currentNode && currentNode.id">
        <!-- 头部 -->
        <div class="semantic-header">
          <div class="left-content">
            <div class="semantic-title mb5">{{ currentNode?.name }}</div>
            <div class="semantic-description">
              描述：{{ currentNode?.description }}
            </div>
          </div>
          <div class="semantic-meta">
            <el-button
              class="attribution-btn"
              type="text"
              style="margin-right: 10px;"
              @click="openCancelCollectModelDrawer(currentNode)"
            >
              <el-tooltip effect="dark" :content="currentNode.popular ? '取消收藏' : '收藏'" placement="bottom">
                <b class="iconfont" :class="currentNode.popular ?  'ChatData-shoucang-mian' : 'ChatData-shoucang'" style="color: #FFC701;"></b>
              </el-tooltip>
            </el-button>
            <el-button
              class="attribution-btn"
              type="text"
              style="margin-right: 10px;"
              @click="handleNodeClick({ id: currentNode.id })"
            >
              <el-tooltip effect="dark" content="刷新" placement="bottom">
                <b class="iconfont ChatData-shuaxin" style="color: #4C6F88;"></b>
              </el-tooltip>
            </el-button>
            <el-popover
              placement="bottom"
              width="600"
              v-model:visible="attributionVisible"
              trigger="click"
              :persistent="true"
              :teleported="false"
            >
              <template #reference>
                <el-button
                  class="attribution-btn"
                  type="text"
                  style="margin-right: 10px;"
                >
                <el-tooltip effect="dark" content="归因设置" placement="bottom">
                  <b class="iconfont ChatData-guiyin" style="color: #4C6F88;"></b>
                </el-tooltip>
                </el-button>
              </template>
              <div class="attribution-form" style="text-align: left; padding:0 10px">
                <div style="font-weight: bold; font-size: 18px; margin-bottom: 16px;">归因设置</div>
                <div
                  v-for="(row, idx) in attributionRows"
                  :key="idx"
                  style="display: flex; gap: 8px; margin-bottom: 8px; align-items: center;"
                >
                  <el-select v-model="row.measure" placeholder="请选择对应的度量" style="flex:1" :teleported="false">
                    <el-option v-for="item in rawMeasureData" :key="item.id" :label="item.alias || item.field" :value="item.id"></el-option>
                  </el-select>
                  <el-select v-model="row.dimensions" placeholder="请选择对应的维度" style="flex:2" :multiple="true" :teleported="false">
                    <el-option v-for="item in rawDimensionData" :key="item.id" :label="item.alias || item.field" :value="item.id"></el-option>
                  </el-select>
                  <el-button icon="Delete" circle @click="removeAttributionRow(idx)" />
                </div>
                <el-button
                  type="primary"
                  plain
                  icon="Plus"
                  size="small"
                  style="margin-bottom: 12px;"
                  @click="addAttributionRow"
                >新增一行</el-button>
                <div style="margin-top: 16px; display: flex; gap: 8px;">
                  <el-button type="primary" @click="handleSaveAttribution">保存</el-button>
                  <el-button @click="attributionVisible = false">取消</el-button>
                </div>
              </div>
            </el-popover>
            <JoinMethodSelector v-model="currentNode.association_method " class="mr5" :disabled="true" />
            <el-button v-if="GetPermission(['data_model_permissions:edit'],currentNode?.id)" type="primary" class="mb3" @click="goEdit"
              >去编辑</el-button
            >
            <div>
              <el-space>
                <span>新增时间：{{ formatDate(currentNode.create_time) }}</span>
                <span>更新时间：{{ formatDate(currentNode.modify_time) }}</span>
              </el-space>
            </div>
          </div>
        </div>
        <!-- 中间图表区 -->
        <div
          class="semantic-chart-area"
          :style="{ flex: '1 1 auto', minHeight: '80px' }"
        >
          <ChartDiagram ref="chartDiagramRef" modelType="view" />
        </div>
        <div class="drag-bar" @mousedown="handleDragStart">
          <span class="drag-arrow" @click.stop="toggleFieldListHeight">
            <el-icon v-if="fieldListHeight < 900"><ArrowUpBold /></el-icon>
            <el-icon v-else><ArrowDownBold /></el-icon>
          </span>
        </div>
        <div
          class="semantic-field-list"
          :style="{ height: fieldListHeight + 'px' }"
        >
          <FieldList
            :dimension="dimensionData"
            :measure="measureData"
            editType="view"
          />
        </div>
      </div>
      <el-empty v-else description="暂无数据" />
    </el-main>
    <ModelDrawer
      v-if="modelDrawerVisible"
      v-model:visible="modelDrawerVisible"
      :type="modelDrawerType"
      :fromId="fromId"
      :treeData="treeData"
      :ModelData="ModelData"
      @successModel="handleDrawerSuccess"
    />
    <ModelFolderDialog
      v-model:visible="folderDialogVisible"
      :type="folderDialogType"
      :folderDialogData="folderDialogData"
      @success="handleFolderDialogSuccess"
    />
    <MoveModelDialog
      v-model:visible="moveDialogVisible"
      :treeData="treeData"
      :currentNode="moveTargetNode"
      @success="handleMoveDialogSuccess"
    />
  </el-container>
</template>

<style lang="scss" scoped>
.semantic-modeling-layout {
  height: 100%;
  background: transparent;

  .semantic-modeling-sider {
    background: rgba(255, 255, 255, 0.95);
    border-right: 1px solid #e5e7eb;
    .sider-content {
      padding: 0 10px;
      height: 100%;
      background: #fcfcfc;

      .sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        border-bottom: 1px solid #e4e7ed;
        padding: 0 8px;
        .iconfont {
          font-size: 20px;
          cursor: pointer;
        }

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;
        }
      }
      .search-add {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        .add-btn {
          width: 20px;
          height: 20px;
          margin-left: 10px;
        }
      }
                      .model-tree {
          height: calc(100% - 110px);
          overflow: auto;

          /* 自定义滚动条样式 */
          &::-webkit-scrollbar {
            width: 0;
            transition: width 0.3s ease;
          }

          &:hover::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
          }

          &::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;

            &:hover {
              background: #a8a8a8;
            }
          }
        }
    }
  }
  .el-main.main-layout {
    padding: 0;
    overflow: hidden;
    background: transparent;
  }
}
.semantic-content-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
}
.semantic-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  background: #fff;
  border-bottom: 1px solid #ededed;
  .left-content {
  }
  .semantic-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  .semantic-description {
    color: #888;
    margin-bottom: 4px;
  }
  .semantic-meta {
    color: #888;
    font-size: 12px;
    text-align: right;
  }
}
.semantic-chart-area {
  flex: 1 1 auto;
  min-height: 80px;
  overflow: auto;

}
.drag-bar {
  height: 8px;
  background: #f0f0f0;
  cursor: ns-resize;
  z-index: 10;
  position: relative;
  .drag-arrow {
    position: absolute;
    right: 16px;
    top: 35px;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #4c6f88;
    font-size: 18px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    padding: 2px;
    transition: background 0.2s;
    &:hover {
      background: #e6f0ff;
    }
  }
}
.semantic-field-list {
  background: #fff;
  overflow: auto;
  transition: height 0.2s;
  padding: 16px 24px;

  .field-list {
    max-height: 100%;
  }
}
</style>
<style lang="scss">
.menu-popover {
  .menu-item {
    height: 26px;
    line-height: 26px;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}
/* 使 el-select 下拉选项内容左对齐 */
.el-select-dropdown__item {
  text-align: left !important;
}
</style>
