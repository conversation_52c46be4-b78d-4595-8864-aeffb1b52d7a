import dayjs from "dayjs"

const INVALID_DATE = "N/A"

/** 格式化日期时间 */
export function formatDateTime(datetime: string | number | Date = "", template: string = "YYYY-MM-DD HH:mm:ss") {
  const day = dayjs(datetime)
  return day.isValid() ? day.format(template) : INVALID_DATE
}

// src/utils/formatTimeAgo.ts

interface TimeAgoOptions {
  maxDays?: number;
  minUnit?: 'minute' | 'hour' | 'day';
  dateFormat?: 'yyyy-MM-dd' | 'MM-dd' | 'full';
}

/**
 * 将时间转换为"xx前"格式
 * @param time - 要转换的时间（时间戳、日期字符串或Date对象）
 * @param options - 配置选项
 * @returns 格式化后的字符串
 */
export function formatTimeAgo(
  time: string | number | Date,
  options: TimeAgoOptions = {}
): string {
  const {
    maxDays = 30,
    minUnit = 'hour',
    dateFormat = 'yyyy-MM-dd'
  } = options;

  // 转换为时间戳
  const timestamp = new Date(time).getTime();
  const now = Date.now();
  const diff = now - timestamp;

  // 转换时间
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  // 如果超过最大天数，返回日期格式
  // if (days > maxDays) {
  //   return formatDate(new Date(timestamp), dateFormat);
  // }

  // 根据时间差返回不同格式
  if (days > 0) {
    return `${days}天前`;
  }

  if (hours > 0) {
    return minUnit === 'day' ? '今天' : `${hours}小时前`;
  }

  if (minutes > 0) {
    return minUnit === 'hour' ? '1小时内' : `${minutes}分钟前`;
  }

  return minUnit === 'minute' ? '1分钟内' : '刚刚';
}
