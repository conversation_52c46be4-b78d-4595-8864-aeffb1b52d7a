<template>
  <div class="chat-container">
    <!-- 左侧边栏 -->
    <div class="sidebar">
      <!-- 问答对话标题 -->
      <div class="sidebar-header">
        <h3>问答对话</h3>
        <el-icon class="collapse-icon"><ArrowRight /></el-icon>
      </div>

      <!-- 我的助手部分 -->
      <div class="my-agents">
        <div class="agent-item active">
          <el-icon class="agent-icon"><ChatDotRound /></el-icon>
          <span>我的助手</span>
          <el-popover
            placement="bottom-start"
            :width="280"
            trigger="click"
            v-model:visible="searchVisible"
          >
            <template #reference>
              <el-icon class="search-icon" @click="searchVisible = !searchVisible">
                <Search />
              </el-icon>
            </template>
            <div class="search-popover">
              <el-input
                v-model="searchText"
                placeholder="输入关键字"
                class="search-input"
              />
              <div class="search-actions">
                <el-button type="primary" size="small" @click="handleSearch">搜索</el-button>
                <el-button size="small" @click="handleCancel">取消</el-button>
              </div>
            </div>
          </el-popover>
          <el-badge :value="1" class="agent-badge" />
        </div>

        <div class="agent-item">
          <el-icon class="agent-icon"><ChatDotRound /></el-icon>
          <span>XXXX助手</span>
        </div>

        <div class="agent-item">
          <el-icon class="agent-icon"><ChatDotRound /></el-icon>
          <span>商情与法政数据分析助手</span>
          <el-icon class="star-icon"><Star /></el-icon>
        </div>

        <div class="agent-item">
          <el-icon class="agent-icon"><ChatDotRound /></el-icon>
          <span>智能问诊分析</span>
        </div>

        <div class="agent-item">
          <el-icon class="agent-icon"><ChatDotRound /></el-icon>
          <span>XXX助手</span>
        </div>

        <div class="agent-item">
          <el-icon class="agent-icon"><QuestionFilled /></el-icon>
          <span>发现更多助手</span>
          <span class="more-text">有什么...</span>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
      </div>

      <!-- 历史对话 -->
      <div class="history-section">
        <div class="history-header">
          <span>历史对话</span>
          <el-icon class="filter-icon"><Filter /></el-icon>
        </div>
        
        <div class="search-history">
          <el-input
            placeholder="搜索对话"
            prefix-icon="Search"
            class="history-search"
          />
        </div>

        <div class="history-list">
          <div class="history-item">电商中怎么快速打出command...</div>
          <div class="history-item">需求是有数据</div>
          <div class="history-item">分享我学习编程经验</div>
          <div class="history-item">开会不能去开什么，该如何气质...</div>
          <div class="history-item">乐于助人之路，勇于挑战自己</div>
          <div class="history-item">改变别人之前，先改变自己</div>
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <div class="content-header">
        <h1>XXXX助手</h1>
      </div>

      <div class="content-cards">
        <div class="content-card">
          <h3>个人医疗建议</h3>
          <p>分析您的身体健康状况，提供工作生活长期健康管理建议</p>
        </div>

        <div class="content-card">
          <h3>个人医疗建议</h3>
          <p>高效处理健康管理相关问题，精准工具助您问题解答不走弯路的解决方案。</p>
        </div>
      </div>

      <div class="content-cards">
        <div class="content-card">
          <h3>个人医疗建议</h3>
          <p>高效处理健康管理相关问题，精准工具助您问题解答不走弯路的解决方案。</p>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-container">
          <el-input
            v-model="inputText"
            type="textarea"
            :rows="3"
            placeholder="请输入，输入@可选择助手/输入#可选择工具"
            class="message-input"
          />
          <div class="input-footer">
            <span class="input-hint">可同时向AI发送文本、图片和文档等</span>
            <el-button type="primary" class="send-button">
              <el-icon><Promotion /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ArrowRight,
  ChatDotRound,
  Search,
  Star,
  QuestionFilled,
  Filter,
  Promotion
} from '@element-plus/icons-vue'

const searchVisible = ref(false)
const searchText = ref('')
const inputText = ref('')

const handleSearch = () => {
  searchVisible.value = false
}

const handleCancel = () => {
  searchText.value = ''
  searchVisible.value = false
}
</script>

<style scoped>
.chat-container {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

.sidebar {
  width: 280px;
  background-color: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.collapse-icon {
  color: #909399;
  cursor: pointer;
}

.my-agents {
  padding: 8px 0;
  border-bottom: 1px solid #e4e7ed;
}

.agent-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  position: relative;
}

.agent-item:hover {
  background-color: #f5f7fa;
}

.agent-item.active {
  background-color: #ecf5ff;
  color: #005EE0;
}

.agent-icon {
  margin-right: 8px;
  color: #005EE0;
}

.search-icon {
  margin-left: auto;
  color: #909399;
  cursor: pointer;
}

.search-icon:hover {
  color: #005EE0;
}

.agent-badge {
  margin-left: auto;
}

.star-icon {
  margin-left: auto;
  color: #f56c6c;
}

.arrow-icon {
  margin-left: auto;
  color: #909399;
}

.more-text {
  margin-left: auto;
  color: #909399;
  font-size: 12px;
}

.history-section {
  flex: 1;
  padding: 16px 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px 8px;
  font-size: 14px;
  color: #303133;
}

.filter-icon {
  color: #909399;
  cursor: pointer;
}

.search-history {
  padding: 0 16px 16px;
}

.history-search {
  font-size: 14px;
}

.history-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 16px;
}

.history-item {
  padding: 8px 0;
  font-size: 14px;
  color: #606266;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:hover {
  color: #005EE0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.content-header {
  text-align: center;
  padding: 40px 20px 20px;
}

.content-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: #303133;
}

.content-cards {
  display: flex;
  gap: 20px;
  padding: 0 40px 20px;
  justify-content: center;
}

.content-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  width: 280px;
  border: 1px solid #e4e7ed;
}

.content-card h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.content-card p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.input-area {
  margin-top: auto;
  padding: 20px 40px 40px;
}

.input-container {
  max-width: 800px;
  margin: 0 auto;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background-color: white;
}

.message-input {
  border: none;
}

.message-input :deep(.el-textarea__inner) {
  border: none;
  box-shadow: none;
  resize: none;
  padding: 16px;
}

.input-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  border-top: 1px solid #e4e7ed;
}

.input-hint {
  font-size: 12px;
  color: #909399;
}

.send-button {
  border-radius: 50%;
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-popover {
  padding: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.search-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
</style>