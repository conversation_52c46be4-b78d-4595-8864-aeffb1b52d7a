/* eslint-disable perfectionist/sort-imports */

// core
import { pinia } from "@/pinia"
import { router } from "@/router"
import { installPlugins } from "@/plugins"
import App from "@/App.vue"
// css
import "normalize.css"
import "nprogress/nprogress.css"
// import "element-plus/theme-chalk/dark/css-vars.css"
import 'element-plus/dist/index.css';
import "vxe-table/lib/style.css"
import "@@/assets/styles/index.scss"
import "virtual:uno.css"
import '@/common/assets/iconfont/iconfont.css'
import '@/common/assets/iconfont/iconfont.js'
import '@@/assets/styles/element-plus-variables.scss'; // 引入自定义 SCSS 文件
// 权限
import { checkPermission } from '@/common/utils/permission'
// 创建应用实例
const app = createApp(App)
// 全局函数，获取权限；挂载到全局属性
// @ts-ignore 全局定义 GetPermission 以便于组件使用
app.config.globalProperties.GetPermission = function(permissionRoles:[],id?:string):boolean {
  return checkPermission(
    permissionRoles,
    id
  )
}
// 安装插件（全局组件、自定义指令等）
installPlugins(app)

// 安装 pinia 和 router
app.use(pinia).use(router)

// router 准备就绪后挂载应用
router.isReady().then(() => {
  app.mount("#app")
})
