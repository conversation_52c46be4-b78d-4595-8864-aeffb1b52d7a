<script setup lang="ts">
import { Close, Document, MoreFilled, Plus } from "@element-plus/icons-vue"
import { ElMessage, ElMessageBox } from "element-plus"
import { computed, onMounted, onUnmounted, ref, watch } from "vue"

const props = defineProps({
  index: {
    type: Number,
    default: 0
  }
})

const emits = defineEmits(["save"])
// 操作类型
const editType = ref<string>("add")
// 编辑数据
const editData = ref<any>({})

const visible = ref<boolean>(false)

const isFullscreen = ref(false)
const originData = ref(null)

// 计算字符数
const charCount = computed(() => {
  if (!editData.value) return 0
  if (typeof editData.value === "string") {
    return editData.value.length
  }
  return JSON.stringify(editData.value).length
})
// --------------

function handleClose() {
  visible.value = false
}
// 查看
function show(type: string = "add", data?: any, origin?: any) {
  editData.value = ""
  originData.value = null; // 存储携带过来的其他数据
  editType.value = type
  data && (editData.value = data)
  origin && (originData.value = origin)
  visible.value = true
}

// 切换全屏
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value
}

function handleSave() {
  if (editData.value) {
    emits("save", {
      type: editType.value,
      data: editData.value,
      originData: originData.value
    })
    visible.value = false;
    // ElMessage.success("保存成功")
  } else {
    ElMessage.warning("内容不能为空")
  }
}

// 全局键盘事件处理（可选）
function handleGlobalKeydown(event: KeyboardEvent) {
  // 只在抽屉可见时响应
  if (visible.value && (event.ctrlKey || event.metaKey) && event.key === "s") {
    event.preventDefault()
    handleSave()
  }
}
// 可以在组件挂载时添加全局监听
onMounted(() => {
  document.addEventListener("keydown", handleGlobalKeydown)
})

onUnmounted(() => {
  document.removeEventListener("keydown", handleGlobalKeydown)
})

defineExpose({
  show
})
</script>

<template>
  <el-drawer
    v-model="visible"
    :show-close="false"
    :size="isFullscreen ? '100%' : '30%'"
    class="chunks-manage-drawer"
  >
    <template #header="{ close }">
      <div class="drawer-header flex-row">
        <span class="title">
          {{ editType === "add" ? "新增分片" : "编辑分片" }}
        </span>
        <div class="save-btn" v-if="isFullscreen">
          <el-button type="primary" @click="handleSave">
            保存 <span class="shortcut"> ⌘/Ctrl+S</span>
          </el-button>
          <el-button @click="handleClose">
            取消
          </el-button>
        </div>
        <el-tooltip :content="isFullscreen ? '退出全屏' : '放大全屏'">
          <svg
            class="icon svg-icon"
            aria-hidden="true"
            @click="toggleFullscreen"
          >
            <use
              :xlink:href="
                isFullscreen ? '#ChatData-zuixiaohua' : '#ChatData-zuidahua'
              "
            />
          </svg>
        </el-tooltip>
        <el-icon class="el-icon--left" @click="close">
          <Close />
        </el-icon>
      </div>
      <br>
      <div class="chunks-desc">
        <span class="title">
          <svg class="icon svg-icon" aria-hidden="true">
            <use xlink:href="#ChatData-fenpian" />
          </svg>
          分片-{{originData?.index ?? 1}}
        </span>
        <span class="count">{{ charCount }}字符</span>
      </div>
    </template>
    <el-input
      type="textarea"
      v-model="editData"
      :autosize="{ minRows: 10 }"
      resize="none"
      placeholder="请输入分片内容..."
      class="edit-content"
    />
    <!-- <div :contenteditable="true" class="edit-content"></div> -->
    <div class="drawer-footer" v-if="!isFullscreen">
      <el-button type="primary" @click="handleSave">
        保存 <span class="shortcut"> ⌘/Ctrl+S</span>
      </el-button>
      <el-button @click="handleClose">
        取消
      </el-button>
    </div>
  </el-drawer>
</template>

<style lang="scss">
.drawer-header {
  gap: 15px;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: #262626;
    line-height: 20px;
    flex: 1;
  }
  .svg-icon {
    width: 14px;
    height: 14px;
    cursor: pointer;
    vertical-align: middle;
  }
  i {
    cursor: pointer;
  }
}
.chunks-desc {
  display: flex;
  align-items: center;
  gap: 50px;

  .svg-icon {
    width: 14px;
    height: 14px;
    cursor: pointer;
    vertical-align: middle;
  }
  //   }
  .count,
  .title {
    font-size: 12px;
    color: #595959;
    line-height: 14px;
  }
}
.chunks-manage-drawer {
  .el-drawer__header {
    display: grid !important;
    grid-template-columns: 1fr;
  }
  .el-drawer__body {
    display: flex;
    flex-direction: column;
    gap: 15px;
    .edit-content {
      flex: 1;
      overflow: auto;
      //   padding: 16px;
      //   border: 1px solid #dcdfe6;
      //   border-radius: 4px;
      //   min-height: 200px;
      outline: none;

      .el-textarea__inner {
        height: 100% !important;
        box-shadow: none;
        border-bottom: 1px solid #dcdfe6;
      }
    }
    .drawer-footer {
      text-align: right;
    }
  }
}
</style>
