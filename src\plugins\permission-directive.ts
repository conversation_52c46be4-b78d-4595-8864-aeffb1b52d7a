import type { App, Directive } from "vue"
import { useUserStore } from "@/pinia/stores/user"
import { isArray } from "@@/utils/validate"

/**
 * @name 权限指令
 * @description 和权限判断函数 checkPermission 功能类似
 */
const permission: Directive = {
  mounted(el, binding) {
    const { value: permissionRoles } = binding
    const { privileges } = useUserStore()
    const { workspace_permissions } = privileges as any
    if (isArray(permissionRoles) && permissionRoles.length > 0) {
      const scope = permissionRoles[0].split(':')[0]
      const permission = permissionRoles[0].split(':')[1]
      // 空间维度权限 判断 scope 是否在 spaceDimension 中
      const spaceDimension = ['workspace','member','agent','data_subject','data_model']
      if(spaceDimension.includes(scope)){
        // 空间维度权限（空间管理，成员管理、助手新增、模型主题新增、模型新增）
        const PrivilegesData = workspace_permissions.find(item => item.category === scope)?.permissions || []
        const hasPermission = PrivilegesData.includes(permission)
        hasPermission || el.parentNode?.removeChild(el)
      } else {
        // 数据维度  单个助手，数据主题，数据模型
      }
    } else {
      throw new Error(`参数必须是一个数组且长度大于 0，参考：v-permission="['workspace:create']"`)
    }
  }
}

export function installPermissionDirective(app: App) {
  app.directive("permission", permission)
}
