<template>
  <div class="data-authority el-row">
    <!-- 左侧模型列表 -->
    <div class="model-list el-col">
      <div class="small-title mb-3">数据权限管理</div>
      <el-input v-model="modelSearch" placeholder="关键词" size="small" clearable class="model-search" />
      <div style="overflow: scroll; height: calc(100% - 160px);">
        <div v-for="item in filteredModels" :key="item.id"
          :class="['model-item', { active: selectedModel.id === item.id }]" @click="changeModelEvent(item)">
          <span>{{ item.name }}</span>
          <span class="model-count">{{ item.count }}</span>
        </div>
      </div>
    </div>
    <div class="right-col">
      <div class="small-title mb-3">{{ selectedModel.name }}</div>
      <div class="flex">
        <!-- 中间权限列表 -->
        <div class="auth-list el-col">

          <div class="auth-list-header">
            <span class="auth-list-title">列表</span>
            <el-tooltip class="box-item" effect="dark" content="新增权限" placement="top">
              <el-button class="add-btn" type="primary" :icon="Plus" size="small" circle @click="onAddAuth()" />
            </el-tooltip>
          </div>
          <div class="search">
            <el-input v-model="authSearch" placeholder="关键词" size="small" clearable class="auth-search" />
          </div>
          <div class="auth-box">
            <div v-for="item in filteredAuths" :key="item.id"
              :class="['auth-item', { active: selectedAuth.id === item.id }]" @click="getPermissionDetail(item)">
              <span>{{ item.permission_name }}</span>
              <el-dropdown @command="(cmd) => handleDropdownCommand(cmd, item)">
                <el-icon>
                  <MoreFilled />
                </el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">重命名</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
        <!-- 右侧权限详情表单 -->
        <div class="auth-detail el-col" v-loading="loading">
          <template v-if="!selectedAuth.id">
            <el-empty description="请在左侧新建或选择数据权限"></el-empty>
          </template>
          <template v-else>
            <div class="top-flex">
              <div class="small-title">{{ selectedAuth.permission_name }}</div>
              <BusmMemberOrgBtn
                v-bind="$attrs"
                :isMultiple="true"
                :nodeKey="'username'"
                @hideEvent="onHideEvent()"
                v-model:value="usernamesList"
                @change="(list) => changeUser(list)"
              >
              <template #reference>
                <span class="userCount">
                  <i class="ChatData-tuandui iconfont"></i>
                  <i class="num">{{ selectedAuth.user_count }}</i>
                </span>
              </template>
            </BusmMemberOrgBtn>
            </div>
            <div class="top-operate">
              <el-radio-group v-model="selectedAuth.permission_type">
                <el-radio-button label="行权限" value="row" />
                <el-radio-button label="列权限" value="column" />
              </el-radio-group>
              <el-button type="primary" @click="SubmitEvent">保存</el-button>
            </div>
            <row-permission ref="rowpermission" v-if="selectedAuth.permission_type === 'row'" :selectedAuth="selectedAuth"></row-permission>
            <column-permission ref="columnpermission" v-else :selectedAuth="selectedAuth"></column-permission>
          </template>
        </div>
      </div>
    </div>
  </div>
  <PermissionDialog v-model:visible="permissionDialogVisible" :type="permissionDialogType"
    :permissionDialogData="permissionDialogData" @success="handlepermissionDialogSuccess" />
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { getSemanticModelListApi } from '@/common/apis/semantic';
import { getPermissionListApi, postPermissionsApi, postRenamePermissionsApi, deletePermissionsApi,getPermissionDetailApi,postPermissionsRuleApi,postPermissionsUsersApi} from '@/common/apis/llm';
import { Plus } from "@element-plus/icons-vue";
import PermissionDialog from './permissionDialog.vue'
import RowPermission from './rowPermission.vue'
import ColumnPermission from './columnPermission.vue'
import { useRoute } from 'vue-router'
import BusmMemberOrgBtn from '@/pages/llm/workspaceManage/component/BusmMemberOrgBtn.vue'

const route = useRoute()
const modelSearch = ref('');
const selectedModel = ref({ name: '', id: '' });
const models = ref([]);
const filteredModels = computed(() => {
  if (!modelSearch.value) return models.value;
  return models.value.filter(m => m.name.includes(modelSearch.value));
});

const authSearch = ref('');
const selectedAuth = ref<any>({});
const authsList = ref([]);
const usernamesList = ref([])
const filteredAuths = computed(() => {
  if (!authSearch.value) return authsList.value;
  return authsList.value.filter(a => a.permission_name.includes(authSearch.value));
});

const permissionDialogVisible = ref(false);
const permissionDialogType = ref<'add' | 'edit'>('add');
const permissionDialogData = ref<any>({});

const columnpermission = ref();
const rowpermission = ref();
const loading = ref(false)
function onAddAuth() {
  permissionDialogType.value = 'add';
  permissionDialogVisible.value = true;
  permissionDialogData.value = {};
}

onMounted(() => {
  getListData();
});

function getListData() {
  getSemanticModelListApi({workspace_id:route.query.spaceCode}).then((res) => {
    const response = res as { data: any };
    models.value = response.data || [];
    selectedModel.value = models.value[0];
    getPermissionData();
  });
}

function getPermissionData() {
  getPermissionListApi({ model_id: selectedModel.value.id }).then((res) => {
    const response = res as { data: any };
    authsList.value = response.data || [];
    if(!authsList.value.length) {
      selectedAuth.value = {id:'',name:''}
      return
    }
    if (!selectedAuth.value.id && authsList.value.length) {
      getPermissionDetail(authsList.value[0])
    }
  });
}
function changeModelEvent(item) {
  selectedModel.value = item
  selectedAuth.value = {}
  getPermissionData()
}
async function handlepermissionDialogSuccess(form: any) {
  if (permissionDialogType.value === 'add') {
    await postPermissionsApi({
      model_id: selectedModel.value.id,
      ...form,
    }).then((res: any) => {
      if (res.success) {
        ElMessage.success('创建成功');
      }
    });
  } else if (permissionDialogType.value === 'edit') {
    await postRenamePermissionsApi({id:selectedAuth.value.id,...form}).then((res: any) => {
      if (res.success) {
        ElMessage.success('编辑成功');
      }
    });
  }
  getPermissionData();
}

function handleDropdownCommand(command: string, item: any) {
  switch (command) {
    case 'edit':
      permissionDialogType.value = 'edit';
      permissionDialogVisible.value = true;
      permissionDialogData.value = item;
      break;
    case 'delete':
      handleDeleteAEvent(item);
      break;
  }
}

function handleDeleteAEvent(item: any) {
  ElMessageBox.confirm(
    `确定要删除数据吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    let parmas = {
      model_id: selectedModel.value.id,
      permission_name: item.permission_name
    };
    deletePermissionsApi(parmas).then((res) => {
      ElMessage.success('删除成功');
      if (item.id === selectedAuth.value.id) {
        selectedAuth.value = { id: '', permission_name: '' };
      }
      getPermissionData();
    });
  });
}
// 获取权限详情
const getPermissionDetail = async (item:any) => {
  loading.value = true
  const res = await getPermissionDetailApi({
    model_id: item.model_id,
    permission_name: item.permission_name,
  }).finally(() => {
    loading.value = false
  });
  const response = res as { data: any };
  selectedAuth.value = response.data || {};
  usernamesList.value = []
  if(selectedAuth.value.usernames && selectedAuth.value.usernames.length) {
    selectedAuth.value.usernames.forEach(val=>{
      usernamesList.value.push({
        username:val,
        name:val
      })
    })
  }
}
// 保存事件
function SubmitEvent(){
  let data = selectedAuth.value.permission_type === 'column' ? columnpermission.value?.saveEvent() : rowpermission.value?.saveEvent();
  data && postPermissionsRuleApi({model_id: selectedModel.value.id, permission_name:selectedAuth.value.permission_name,permission_type:selectedAuth.value.permission_type,...data}).then((res) => {
      const response = res as { data: any, success:Boolean};
      if (response.success) {
        ElMessage.success('编辑成功');
      }
    });
}
const changeUser =  (userList: any[]) => {
}
// 用户为空时添加用户
function onHideEvent(){
  let parmas = {
      model_id:selectedAuth.value.model_id,
      permission_name:selectedAuth.value.permission_name,
      usernames:[]
    }
    if(usernamesList.value.length) {
      usernamesList.value.forEach(val =>{
        parmas.usernames.push(val.username)
      })
    }
  if(JSON.stringify(parmas.usernames) === JSON.stringify(selectedAuth.value.usernames)) {
    return
  } else {
    postPermissionsUsersApi(parmas).then((res) => {
        const response = res as { data: any, success:Boolean};
        if (response.success) {
          ElMessage.success('成员更新成功');
          selectedAuth.value.user_count = usernamesList.value.length
        }
      });
    }
}
</script>

<style scoped lang="scss">
.data-authority {
  display: flex;
  height: 100%;
  background: #fff;
  height: calc(100vh - 180px);

  .small-title {
    font-weight: bold;
    font-size: 14px;
  }

  .model-list {
    width: 220px;
    min-width: 200px;
    border-right: 1px solid #f0f0f0;
    padding: 15px;

    .model-search {
      width: 90%;
      margin-bottom: 10px;
    }

    .model-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      cursor: pointer;
      border-radius: 4px;
      margin-bottom: 2px;
      transition: background 0.2s, color 0.2s;

      &.active {
        background: #F4F9FF;
        color: #005EE0;
        font-weight: bold;
      }

      &:hover {
        background: #F4F9FF;
      }

      .model-count {
        color: #999;
      }
    }
  }

  .right-col {
    flex: 1;
    padding: 20px;

    .flex {
      display: flex;
      height: calc(100% - 10px);
    }

  }

  .auth-list {
    width: 260px;
    min-width: 220px;
    border: 1px solid #F2F2F2;
    background: #FCFCFC;
    padding: 15px;

    .auth-list-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .auth-list-title {
        font-size: 14px;
        font-weight: bold;
      }

      .ml-2 {
        margin-left: 8px;
      }
    }

    .search {
      margin-bottom: 10px;
    }

    .auth-item {
      padding: 8px 16px;
      border-radius: 4px;
      margin-bottom: 2px;
      cursor: pointer;
      transition: background 0.2s, color 0.2s;
      display: flex;
      justify-content: space-between;
      .el-dropdown {
        display: none;
      }
      &:hover {
        .el-dropdown {
          display: block;
        }
      }

      &.active {
        background: #F4F9FF;
        color: #005EE0;
      }

      &:hover {
        background: #F4F9FF;
      }
    }

    .auth-dropdown {
      margin-top: 8px;
    }
  }

  .auth-detail {
    border: 1px solid #F2F2F2;
    border-left: none;
    flex: 1;
    padding: 15px;
    .top-operate {
      margin:15px 0 25px 0;
      display: flex;
      justify-content: space-between;
    }

    .detail-tabs {
      margin-bottom: 16px;
    }

    .detail-form-row {
      :deep(.el-form-item__content) {
        display: flex;
        align-items: center;
        flex-direction: row;
        flex-wrap: nowrap;
      }

      .mr-2 {
        margin-right: 8px;
      }

      .short-select {
        width: 100px;
      }

      .ml-2 {
        margin-left: 8px;
      }
    }
  }
}
.top-flex {
  display: flex;
  justify-content: space-between;
  .userCount {
    background: #EBF3FD;
    padding:3px 5px;
    border-radius: 3px;
    cursor: pointer;
    .iconfont {
      color:#4C6F88;
    }
    .num {
      color: #005EE0;
      margin-left: 5px;
      font-style: normal;
      font-size:14px;
    }
  }
}
</style>
