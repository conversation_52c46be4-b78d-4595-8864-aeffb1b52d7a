<template>
  <div class="model-form-container" v-loading="loading">
    <div class="model-form-header">
      <div class="header-left">
        <span class="back-btn" @click="handleBack">
          <i class="ChatData-fanhui iconfont"></i>
        </span>
        <h2>{{ isEdit ? "编辑模型" : "新增模型" }}</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="submitForm" size="small" >确定</el-button>
        <el-button @click="handleCancel" size="small" >取消</el-button>
      </div>
    </div>
    <div class="model-form-content">
      <el-form
        label-width="100px"
        :model="form.params"
        :rules="paramsValidate"
        ref="modelFormRef"
        label-position="top"
      >
        <el-form-item required label="模型类型">
          <el-row :gutter="20">
            <el-radio-group
              v-model="form.worker_type"
              class="worker-type-radio-group"
              :disabled="isEdit"
            >
              <el-radio
                v-for="item in WorkerType"
                :key="item.key"
                :value="item.key"
              >
                <i class="iconfont ChatData-duihua worker-type-icon"> </i>
                <!-- <svg class="iconfont" aria-hidden="true" size="6">
                  <use href="#ChatData-duihua"></use>
                </svg> -->
                <div class="desc">
                  <span class="title">{{ item.label }}</span>
                  <span>{{ item.description }}</span>
                </div>
              </el-radio>
            </el-radio-group>
          </el-row>
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="Provider" prop="provider">
              <el-select
                v-model="form.provider"
                placeholder="请选择Provider"
                :disabled="isEdit"
                style="width: 32%"
                @change="handleProviderChange"
              >
                <el-option
                  v-for="item in providerOptions"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col
            :span="
              specialParamsConfig?.row?.includes(field.param_name) ? 24 : 8
            "
            v-if="form.provider"
            v-for="field in params"
            :key="field.param_name"
          >
            <el-form-item :prop="field.param_name">
              <template #label>
                <span>{{
                  specialParamsConfig?.labelAlias?.[field.param_name] ||
                  field.label
                }}</span>
                <el-tooltip
                  :content="field.description"
                  placement="top"
                  v-if="!!field.description"
                >
                  <i
                    class="iconfont ChatData-zhushi tip-icon"
                    style="margin-top: 2px"
                  ></i>
                </el-tooltip>
              </template>
              <el-select
                v-if="field.param_name === 'name'"
                allow-create
                filterable
                :disabled="paramsIsDis(field.param_name)"
                clearable
                placeholder="请选择或输入模型名称"
                @change="handleModelNameChange"
                v-model="form.params[field.param_name]"
              >
                <el-option
                  v-for="opt in modelNameOptions"
                  :key="opt"
                  :value="opt"
                >
                  {{ opt }}
                </el-option>
              </el-select>
              <!-- 开关 -->
              <template
                v-else-if="
                  specialParamsConfig?.checkbox?.includes(field.param_name)
                "
              >
                <el-checkbox
                  v-model="form.params[field.param_name]"
                  :disabled="paramsIsDis(field.param_name)"
                />
              </template>
              <el-input
                v-else-if="!field.is_array"
                :type="field.param_type || 'text'"
                v-model="form.params[field.param_name]"
                :disabled="paramsIsDis(field.param_name)"
                :placeholder="`请输入 `+ field.param_name"
              />

              <el-select
                v-else
                v-model="form.params[field.param_name]"
                :disabled="paramsIsDis(field.param_name)"
              >
                <el-option
                  v-for="opt in field.valid_values"
                  :key="opt"
                  :value="opt"
                >
                  {{ opt }}
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  createLLMtApi,
  getLLMTypeListApi,
  getLLMDetailApi,
  updateLLMtApi,
} from "@@/apis/llm";
import {
  CreateModelRequestData,
  LLM,
  GetLLMDetailRequestData,
  LLMSpecialParamsConfig,
} from "@@/apis/llm/type";
import {
  Coin,
  Cpu,
  DataAnalysis,
  Delete,
  Edit,
  MoreFilled,
} from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

const route = useRoute();
const router = useRouter();
const isEdit = ref(false);
const form = ref({
  host: "",
  port: 0,
  model: "",
  worker_type: "",
  params: {},
  provider: "",
});
const modelTypeList = ref<LLM.ModelTypeResponse[]>([]);
let providerOptions = ref<string[]>([]);
const params = ref<LLM.ModelTypeParam[]>([]);
const WorkerType: Array<Record<string, any>> = [
  {
    key: "llm",
    label: "对话模型",
    description: "专门用于处理人机对话或多轮交互",
  },
  {
    key: "text2vec",
    label: "向量模型",
    description: "将文本、将文本、图像等数据转换为高维向量",
  },
  {
    key: "reranker",
    label: "重排模型",
    description: "对初步检索或生成的结果进行重新排序",
  },
];
const modelNameOptions = ref<string[]>([]);

// 根据当前选择的provider，获取当前provider的模型类型列表
const curSelProvider = computed(() => {
  return form?.value?.provider
    ? modelTypeList.value.find((item) => item.provider === form.value.provider)
    : ({} as any);
});
// 根据当前选的provider，生成当前params的校验规则
const paramsValidate = computed(() => {
  let rules = params.value.reduce((acc, item) => {
    if (item.required) {
      !acc[item.param_name] && (acc[item.param_name] = []);
      if (acc[item.param_name]) {
        acc[item.param_name].push({
          required: true,
          message: `${item.label}不能为空`,
        });
      }
    }
    return acc;
  }, {});
  rules["provider"] = [
    {
      required: true,
      message: "请输入问题",
      trigger: "blur",
    },
  ];
  return rules;
});
// 表单ref实例
const modelFormRef = ref();
const loading = ref<boolean>(false);
// 模型表单特殊配置
const specialParamsConfig = ref<LLMSpecialParamsConfig>({
  checkbox: ["verbose", "reasoning_model"], // 展示checkbox的params
  allDis: ["provider"], // 一直禁用的params
  editDis: ["provider", "name"], // 编辑时禁用的params
  row: ["api_base"], // 独占一行的params
  labelAlias: {
    name: "模型名称",
  }, // 特殊labelAlias
});

const handleBack = () => {
  handleCancel();
};

const handleCancel = () => {
  router.push({
    path: "modelManage",
    query: {
      spaceCode: route.query.spaceCode || "",
    },
  });
};

// 提交表单
async function submitForm() {
  // console.log(form.value, 1111);
  const valid = await modelFormRef.value?.validate();
  if (valid) {
    let API = isEdit.value ? updateLLMtApi : createLLMtApi;
    API(form.value as CreateModelRequestData).then((res) => {
      ElMessage.success("操作成功");
      handleBack();
    });
  }
}

// 获取模型类型列表
function getModeTypeList() {
  return new Promise((resolve, reject) => {
    loading.value = true;
    getLLMTypeListApi()
      .then((res) => {
        const response = res as {
          data: LLM.ModelTypeResponse | LLM.ModelTypeResponse[];
        };

        // 判断 data 是数组还是单个对象
        if (Array.isArray(response.data)) {
          modelTypeList.value = response.data;
        } else {
          modelTypeList.value = [response.data]; // 包装成数组
        }
        loading.value = false;
        resolve(true);
      })
      .catch(() => {
        loading.value = false;
        reject(false);
      });
  });
}
// 处理provider变化
function handleProviderChange(provider: string, isInitValue: boolean = true) {
  modelNameOptions.value.length = 0;
  modelTypeList.value.forEach((e) => {
    if (e.worker_type === form.value.worker_type && e.provider === provider) {
      if (modelNameOptions.value.indexOf(e.model) === -1) {
        modelNameOptions.value.push(e.model);
      }
      params.value = e.params;
      if (isInitValue) {
        e.params.forEach((item) => {
          form.value.params = {
            ...form.value.params,
            [item.param_name]: item.default_value,
          };
        });
      }
    }
  });
  // 新增设置当前选择的provider的模型名称和host 进行回填
  if (!isEdit.value) {
    const { model = "", host = "" } = curSelProvider.value;
    // form.value.model = model;
    form.value.host = host;
    modelFormRef.value.resetFields();
  }
}

function handleModelNameChange(modelName: string) {
  form.value.model = modelName;
  console.log(form.value, 1111);
}

// 获取模型详情
function getLLMDetail() {
  const query = route.query;
  const params: GetLLMDetailRequestData = {
    model: query.id as string,
    worker_type: query.worker_type as GetLLMDetailRequestData["worker_type"],
    healthy: query.healthy === "true",
  };
  // 编辑时 根据详情 回填
  if (params.model && params.worker_type) {
    getLLMDetailApi(params).then((res: any) => {
      const {
        params: resParams = {},
        host,
        port,
        model,
        worker_type,
      } = res.data;
      form.value = {
        host,
        port,
        model: resParams.name, // 回填模型名称
        worker_type,
        params: resParams,
        provider: resParams.provider,
      };
      nextTick(() => {
        // console.log(resParams, form.value, 777888);
        handleProviderChange(resParams.provider, false);
      });
    });
  }
}

// 根据params的param_name 判断是否禁用
function paramsIsDis(param_name: string) {
  const { allDis, editDis } = specialParamsConfig.value;
  // 新增全部不处理，编辑时 根据allDis 和 editDis 判断是否禁用
  return param_name && isEdit.value
    ? allDis.includes(param_name) || editDis.includes(param_name) || false
    : false;
}

watch(
  () => form.value.worker_type,
  (newVal: string, oldVal: string) => {
    if (newVal !== oldVal) {
      providerOptions.value.length = 0;
      modelTypeList.value.forEach((j) => {
        if (
          j.worker_type === newVal &&
          providerOptions.value.indexOf(j.provider) === -1
        ) {
          providerOptions.value.push(j.provider);
        }
      });
      form.value.params = {};
      form.value.provider = "";
    }
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  const { id, mode, worker_type } = route.query;
  // 编辑模式
  if (mode === "edit" && id) {
    isEdit.value = true;
    form.value.worker_type = worker_type as string;
  } else {
    isEdit.value = false;
    // 新增模式，设置默认值
    Object.assign(form.value, {
      host: "",
      port: 0,
      model: "",
      worker_type: "",
      params: {},
      provider: "",
    });
  }
  getModeTypeList().then(() => {
    getLLMDetail();
    if(mode !== "edit") {
       form.value.worker_type = 'llm'
    }
  });
});
</script>

<style lang="scss" scoped>
.model-form-container {
  background-color: #fff;
  // min-height: 100vh;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .tip-icon {
    font-size: 13px;
    color: #005ee0;
    cursor: pointer;
    margin-left: 10px;
  }
}

.model-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  padding: 0 20px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;

  .back-btn {
    margin-right: 10px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f1f7ff;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    color: #005ee0;

    &:hover {
      background-color: #005ee0;
      color: #fff;
    }
  }
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.header-right {
  display: flex;
}

.model-form-content {
  padding: 20px 40px;
  width: 100%;
  overflow: auto;
  flex: 1;
}

.model-type-card {
  border: 1px solid #eee;
  transition: border 0.2s;
}

.model-type-card.active {
  border: 1.5px solid #409eff;
  box-shadow: 0 0 6px #409eff22;
}
.worker-type-radio-group {
  width: 100%;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  .worker-type-icon {
    font-size: 18px;
    color: #005ee0;
    padding: 0px 9px;
    background: #ebf3fd;
    border-radius: 8px;
  }
  :deep(.el-radio) {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    height: 62px;
    background: #fcfcfc;
    border-radius: 4px;
    border: 1px solid #f2f2f2;
    padding: 12px 14px;
    box-sizing: border-box;
    margin: 0px;
    .el-radio__input {
      order: 1;
      .el-radio__inner {
        // background: #005EE0;
        &::after {
          width: 7px;
          height: 7px;
        }
      }
      &.is-checked {
        .el-radio__inner {
          background: #005ee0;
        }
      }
    }
    .el-radio__label {
      display: flex;
      align-items: center;
      gap: 15px;
      font-size: 12px;
      color: #262626;
      padding: 0px;
      .desc {
        display: flex;
        flex-direction: column;
        & > * {
          display: inline-block;
          line-height: 1;
        }
        .title {
          font-size: 13px;
          color: #262626;
          margin-bottom: 8px;
        }
      }
    }
    &.is-checked {
      background: #f6faff;
      border-color: #98bef5;
      .desc .title {
        color: #005ee0;
        font-weight: bold;
      }
    }
  }
}
</style>

