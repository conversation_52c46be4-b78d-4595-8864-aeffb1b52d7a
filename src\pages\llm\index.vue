<script lang="ts" setup></script>

<template>
  <div class="llm">
    <router-view></router-view>
  </div>
</template>

<style lang="scss" scoped>
.llm {
  height: 100%;
  background: #fff;

}

.llm-card-col {
  margin-bottom: 24px;
}

.llm-card {
  border-radius: 12px;
  min-height: 200px;
  position: relative;
  overflow: visible;
}

.llm-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.llm-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.llm-card-menu {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
}

.llm-card-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.llm-card-desc {
  color: #666;
  font-size: 14px;
  margin-bottom: 16px;
  min-height: 38px;
}

.llm-type-tag {
  position: absolute;
  top: 16px;
  right: 16px;
}

.model-type-card {
  border: 1px solid #eee;
  transition: border 0.2s;
}

.model-type-card.active {
  border: 1.5px solid #409EFF;
  box-shadow: 0 0 6px #409EFF22;
}
</style>
