<template>
  <el-drawer
    v-model="visible"
    :title="editType === 'add' ? '新增计算字段' : '编辑计算字段'"
    size="720"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    footer-class="custom-drawer-footer"
  >
    <el-form :model="form" :rules="rules" label-position="top" ref="formRef">
      <el-form-item label="名称" prop="label">
        <el-input v-model="form.label" placeholder="请输入名称" />
      </el-form-item>
      <el-form-item label="表达式" prop="expr">
        <div class="flex-row" style="width: 100%; margin-bottom: 10px">
          <div class="flex1">
            <el-tree-select
              :data="resolveDimension"
              multiple
              value-key="id"
              style="width: 200px"
              placeholder="请选择维度"
              filterable
              @change="handleDimensionSelect"
              :props="{ label: 'label', value: 'id' }"
            />
            <el-tree-select
              multiple
              style="width: 200px; margin-left: 10px"
              value-key="id"
              :data="resolveMeasure"
              placeholder="请选择度量"
              filterable
              @change="handleMeasureSelect"
              :props="{ label: 'label', value: 'id' }"
            />
          </div>
          <el-tooltip placement="top">
            <template #content>
              返回组中的项目总和。<br />示例: SUM([销售额])
            </template>
            <el-button @click="insertFunction('SUM')">
              求和聚合
              <i class="iconfont ChatData-zhushi" style="margin-left: 5px"></i>
            </el-button>
          </el-tooltip>
          <el-tooltip placement="top">
            <template #content>
              返回组中的项目数。<br />示例: COUNT([客户名称])
            </template>
            <el-button @click="insertFunction('COUNT')">
              计数聚合
              <i class="iconfont ChatData-zhushi" style="margin-left: 5px"></i>
            </el-button>
          </el-tooltip>
        </div>

        <div
          ref="editorRef"
          class="expression-editor"
          contenteditable="true"
          @blur="handleEditorBlur"
          @focus="handleEditorFocus"
          @input="handleEditorInput"
          :placeholder="'请输入/插入表达式内容'"
        ></div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="flex-row">
        <el-space style="flex: 1">
          <template v-if="editType === 'add'">
            <el-switch v-model="isKeepCreate" />
            继续新增下一个
          </template>
          <span v-else>
          </span>
        </el-space>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">{{
          editType === "add" ? "新增" : "保存"
        }}</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { cloneDeep } from "lodash-es";
// 类型定义
interface FieldNode {
  id: string;
  label: string;
  children?: FieldNode[];
}

// Props 类型更新
const props = defineProps<{
  dimensionList: FieldNode[];
  measureList: FieldNode[];
}>();
// 表单状态
const form = ref<any>({
  label: "",
  expr: "", // 保存带高亮的HTML
  field: "",
  expressionFields: [], // 保存字段信息
  // description: "",
});
const emit = defineEmits<{
  (e: "confirm", value: any);
}>();
const visible = ref<boolean>(false);
const rules = {
  label: [
    { required: true, message: "请输入名称", trigger: "blur" },
    // { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
  ],
  expr: [{ required: true, message: "请输入表达式", trigger: "blur" }],
};
const formRef = ref<any>();
const editType = ref<string>("add");
const isKeepCreate = ref<boolean>(false); // 是否继续创建

// 处理可能存在的负数情况
const resolveDimension = computed(() => {
  return props.dimensionList.reduce((acc, item) => {
    item.id && (item.id = String(item.id).replace(/-/g, ""));
    if (item.children) {
      item.children.forEach((child) => {
        child.id && (child.id = String(child.id).replace(/-/g, ""));
      });
    }
    acc.push(item);
    return acc;
  }, []);
});
const resolveMeasure = computed(() => {
  return props.measureList.reduce((acc, item) => {
    item.id && (item.id = String(item.id).replace(/-/g, ""));
    if (item.children) {
      item.children.forEach((child) => {
        child.id && (child.id = String(child.id).replace(/-/g, ""));
      });
    }
    acc.push(item);
    return acc;
  }, []);
});

// -=-------------------------------
function show(type: string = "add", data: any = {}) {
  editType.value = type;
  isKeepCreate.value = false;
  visible.value = true;
  initForm(data);
}

// 编辑器相关
const editorRef = ref<HTMLElement>();
let lastSelection: Selection | null = null;
let lastRange: Range | null = null;

// 保存光标位置
function saveSelection() {
  if (window.getSelection) {
    const sel = window.getSelection();
    if (sel?.getRangeAt && sel?.rangeCount) {
      lastRange = sel.getRangeAt(0);
      lastSelection = sel;
    }
  }
}

// 恢复光标位置
function restoreSelection() {
  if (lastRange && window.getSelection) {
    const sel = window.getSelection();
    sel?.removeAllRanges();
    sel?.addRange(lastRange);
  }
}

// 插入函数
function insertFunction(type: "SUM" | "COUNT" | string) {
  if (!editorRef.value) return;

  restoreSelection();

  // 创建函数名称高亮元素
  const funcSpan = document.createElement("span");
  funcSpan.className = "highlight-function";
  funcSpan.textContent = type;

  // 创建括号和关键字
  const leftBracket = document.createTextNode("(");

  // 创建关键字高亮元素
  const keywordSpan = document.createElement("span");
  keywordSpan.className = "highlight-keyword";
  keywordSpan.contentEditable = "true"; // 确保关键字可编辑
  keywordSpan.textContent = "[关键字]";

  const rightBracket = document.createTextNode(")");

  // 创建一个文档片段来包含所有元素
  const fragment = document.createDocumentFragment();
  fragment.appendChild(funcSpan);
  fragment.appendChild(leftBracket);
  fragment.appendChild(keywordSpan);
  fragment.appendChild(rightBracket);

  if (lastRange) {
    lastRange.deleteContents();
    lastRange.insertNode(fragment);

    // 设置光标位置，选中关键字
    const range = document.createRange();
    range.selectNodeContents(keywordSpan);

    const sel = window.getSelection();
    sel?.removeAllRanges();
    sel?.addRange(range);

    lastRange = range;
  } else {
    editorRef.value.appendChild(fragment);

    // 如果没有选区，也要选中关键字
    const range = document.createRange();
    range.selectNodeContents(keywordSpan);

    const sel = window.getSelection();
    sel?.removeAllRanges();
    sel?.addRange(range);

    lastRange = range;
  }

  // 触发 input 事件以更新表单数据
  handleEditorInput();
}

// 查找字段信息
function findFieldInfo(
  id: string,
  type: "dimension" | "measure"
): FieldNode | null {
  const list =
    type === "dimension" ? resolveDimension.value : resolveMeasure.value;

  function find(nodes: FieldNode[]): FieldNode | null {
    for (const node of nodes) {
      if (node.id === id) {
        return node;
      }
      if (node.children) {
        const found = find(node.children);
        if (found) return found;
      }
    }
    return null;
  }

  return find(list);
}

// 插入字段 (优化后的版本)
function insertField(id: string, type: "dimension" | "measure") {
  if (!editorRef.value) return;

  const fieldInfo = findFieldInfo(id, type);
  if (!fieldInfo) return;

  restoreSelection();

  // 如果当前选中的是关键字，替换它
  let shouldReplaceKeyword = false;
  if (lastRange) {
    const selectedNode = lastRange.commonAncestorContainer;
    const keywordElement =
      selectedNode.nodeType === 1
        ? (selectedNode as HTMLElement)
        : selectedNode.parentElement;

    shouldReplaceKeyword =
      keywordElement?.classList.contains("highlight-keyword") ?? false;
  }

  // 创建字段高亮元素
  const fieldSpan = document.createElement("span");
  fieldSpan.className = "highlight-field";
  fieldSpan.dataset.fieldId = id;
  fieldSpan.dataset.fieldType = type;
  fieldSpan.textContent = `[${fieldInfo.label}]`;

  if (shouldReplaceKeyword) {
    // 如果是替换关键字，保持在函数括号内
    const keywordElement =
      lastRange!.commonAncestorContainer.nodeType === 1
        ? (lastRange!.commonAncestorContainer as HTMLElement)
        : lastRange!.commonAncestorContainer.parentElement!;
    keywordElement.replaceWith(fieldSpan);
  } else if (lastRange) {
    // 普通插入
    lastRange.deleteContents();
    lastRange.insertNode(fieldSpan);
  } else {
    editorRef.value.appendChild(fieldSpan);
  }

  // 更新光标位置到字段后
  const range = document.createRange();
  range.setStartAfter(fieldSpan);
  range.collapse(true);

  const sel = window.getSelection();
  sel?.removeAllRanges();
  sel?.addRange(range);

  lastRange = range;

  // 保存字段信息
  form.value.expressionFields.push({
    id,
    label: fieldInfo.label,
    type,
  });

  // 触发 input 事件以更新表单数据
  handleEditorInput();
}

// 处理维度选择
function handleDimensionSelect(value: string[]) {
  if (value.length) {
    const fieldId = value[value.length - 1];
    insertField(fieldId, "dimension");
  }
}

// 处理度量选择
function handleMeasureSelect(value: string[]) {
  if (value.length) {
    const fieldId = value[value.length - 1];
    insertField(fieldId, "measure");
  }
}

// 获取实际SQL表达式
function getActualExpression(): string {
  if (!editorRef.value) return "";

  // 克隆编辑器内容以供处理
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = editorRef.value.innerHTML;

  // 处理所有字段节点
  const fieldSpans = tempDiv.querySelectorAll(".highlight-field");
  fieldSpans.forEach((span: any) => {
    const fieldId = span.dataset.fieldId;
    if (fieldId) {
      // 将字段span替换为 [id]
      span.replaceWith(`[${fieldId}]`);
    }
  });

  // 处理所有未替换的关键字节点（如果有）
  const keywordSpans = tempDiv.querySelectorAll(".highlight-keyword");
  keywordSpans.forEach((span) => {
    // 将关键字span替换为其文本内容
    span.replaceWith(span.textContent || "");
  });

  // 处理所有函数名节点
  const functionSpans = tempDiv.querySelectorAll(".highlight-function");
  functionSpans.forEach((span) => {
    // 将函数名span替换为其文本内容
    span.replaceWith(span.textContent || "");
  });

  // 获取处理后的纯文本内容
  return tempDiv.textContent || "";
}

// 提交表单时
function handleSubmit() {
  formRef.value.validate().then((valid) => {
    if (!valid) return;
    const actualExpression = getActualExpression();
    emit("confirm", {
      ...cloneDeep(toRaw(form.value)),
      field: actualExpression,
    });
    if (isKeepCreate.value) {
      initForm();
      visible.value = true;
    } else {
      visible.value = false;
    }
  });
}

// 事件处理
function handleEditorBlur() {
  saveSelection();
}

function handleEditorFocus() {
  restoreSelection();
}

// 处理编辑器输入
function handleEditorInput() {
  saveSelection();

  // 更新表达式内容
  form.value.expr = editorRef.value?.innerHTML || "";

  // 更新字段列表
  updateExpressionFields();
}

// 更新表达式中的字段列表
function updateExpressionFields() {
  if (!editorRef.value) return;

  // 清空现有字段列表
  form.value.expressionFields = [];

  // 获取所有字段元素
  const fieldSpans = editorRef.value.querySelectorAll(".highlight-field");
  fieldSpans.forEach((span: any) => {
    const fieldId = span.dataset.fieldId;
    const fieldType = span.dataset.fieldType as "dimension" | "measure";
    if (fieldId && fieldType) {
      const fieldInfo = findFieldInfo(fieldId, fieldType);
      if (fieldInfo) {
        form.value.expressionFields.push({
          id: fieldId,
          label: fieldInfo.label,
          type: fieldType,
        });
      }
    }
  });
}

function initForm(obj: any = {}) {
  const { label = "", expr = "", field = "", expressionFields = [] } = obj;
  form.value = {
    label,
    expr,
    field,
    expressionFields,
    ...obj,
  };
  // form.value.label = label;
  // form.value.expr = expr;
  // form.value.field = field;
  // form.value.expressionFields = expressionFields;
  if (editorRef.value) {
    editorRef.value.innerHTML = expr;

    updateExpressionFields();
  }
}

// 初始化
onMounted(() => {
  // if (editorRef.value && form.value.expr) {
  //   editorRef.value.innerHTML = form.value.expr;
  //   updateExpressionFields();
  // }
});

defineExpose({
  show,
});
</script>

<style lang="scss" scoped>
.expression-editor {
  min-height: 200px;
  width: 100%;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
  outline: none;

  &:empty:before {
    content: attr(placeholder);
    color: #999;
  }

  &:focus {
    border-color: #409eff;
  }

  // 函数名称高亮样式
  :deep(.highlight-function) {
    color: #409eff;
    font-weight: bold;
    user-select: none; // 防止函数名被选中编辑
  }

  // 关键字高亮样式
  :deep(.highlight-keyword) {
    color: #e6a23c;
    background-color: #fdf6ec;
    padding: 0 2px;
    border-radius: 2px;
    cursor: text;
  }

  // 字段高亮样式
  :deep(.highlight-field) {
    padding: 0 2px;
    border-radius: 2px;
    user-select: none; // 防止字段被选中编辑
    &[data-field-type="dimension"] {
      color: #67c23a;
      background-color: #f0f9eb;
    }

    &[data-field-type="measure"] {
      color: #409eff;
      background-color: #ecf5ff;
    }
  }
}

// 确保编辑器内的内容可以正常换行
.expression-editor {
  white-space: pre-wrap;
  word-break: break-all;
}

// 可以为不同类型的字段添加不同的样式
.expression-editor {
  :deep(.highlight-field[data-field-type="dimension"]) {
    color: #67c23a;
    background-color: #f0f9eb;
  }

  :deep(.highlight-field[data-field-type="measure"]) {
    color: #409eff;
    background-color: #ecf5ff;
  }
}
</style>
