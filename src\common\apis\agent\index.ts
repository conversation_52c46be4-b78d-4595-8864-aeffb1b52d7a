import { request } from "@/http/axios"
import type * as Agent from "./type"

/** 获取模型列表 */
export function getModelListApi<T extends object>(data?: T) {
    return request({
        url: '/serve/model/models',
        version: "/api/v2",
        method: "get",
        data
    })
}

/** 获取应用类型 */
export function getApplicationTypeApi<T extends object>(data?: T) {
    return request({
        url: '/native_scenes',
        method: "get",
        data
    })
}

/** 获取知识库列表 */
export function getKnowledgeBaseListApi<T extends object>(data?: T) {
    return request({
        url: '/space/list',
        method: "post",
        data,
        version: "/knowledge"
    })
}

/** 新增助手 */
export function addAssistantApi<T extends object>(data?: T) {
    return request({
        url: '/app/create',
        method: "post",
        data
    })
}

/** 更新助手 */
export function updateAssistantApi<T extends Agent.createAppData>(data?: T) {
    return request({
        url: '/app/edit',
        method: "post",
        data
    })
}

/** 获取助手详情 */
export function getAssistantDetailApi<T extends Agent.delAssistantData>(data?: T) {
    return request({
        url: '/app/detail',
        method: "post",
        data
    })
}

// 删除助手
export function deleteAssistantApi<T extends Agent.delAssistantData>(data?: T) {
    return request({
        url: '/app/remove',
        method: "post",
        data
    })
}

// 取消发布助手
export function unPublishAssistantApi<T extends Agent.delAssistantData>(data?: T) {
    return request({
        url: '/app/unpublish',
        method: "post",
        data
    })
}

// 发布助手
export function publishAssistantApi<T extends Agent.delAssistantData>(data?: T) {
    return request({
        url: '/app/publish',
        method: "post",
        data
    })
}

/** 获取数据集列表 */
export function getDatasetListApi<T extends object>(params?: T) {
    return request({
        url: `/serve/data-models/tree`,
        method: "get",
        params,
        version: "/api/v2"
    })
}

// 查询记忆列表
export function memoryList<T>(data?: T) {
    return request({
        url: '/memory/list',
        method: "post",
        data
    })
}

// 查询单条记忆
export function getMemoryDetailById<T>(id: string | number, data?: T) {
    return request({
        url: `/memory/${id}`,
        method: "get",
        data
    })
}

// 更新记忆
export function updateMemoryDetailById<T>(id: string | number, data?: T) {
    return request({
        url: `/memory/${id}`,
        method: "put",
        data
    })
}

// 删除记忆
export function delMemoryDetailById<T>(id: string | number, data?: T) {
    return request({
        url: `/memory/${id}`,
        method: "delete",
        data
    })
}

// 保存记忆
export function saveMemoryDetail<T>(data?: T) {
    return request({
        url: `/memory/create-memory-record`,
        method: "post",
        data
    })
}

// 模型评估
export function modelEvaluate<T>(data?: T) {
    return request({
        url: `/memory/evaluation/evaluate`,
        method: "post",
        data
    })
}
