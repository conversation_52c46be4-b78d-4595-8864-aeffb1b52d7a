<script lang="ts" setup>
import type { questListItem } from "./type";
import { ArrowRightBold, Search, Select } from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";
import { useRouter, useRoute } from "vue-router";
import {
  getMemoryDetailById,
  memoryList,
  modelEvaluate,
  updateMemoryDetailById,
} from "@/common/apis/agent/index";
import customPage from "./components/customPage.vue";
import dayjs from "dayjs";
import utc from 'dayjs/plugin/utc';
// 扩展 dayjs 以支持 utc 方法
dayjs.extend(utc);
const props = defineProps({
  canEdit: {
    type: Boolean,
    default: true,
  },
});
const route = useRoute();
// 当前选中助手
const curEditAgent = inject<any>("curEditAgent");
// 全局loading
const loading = ref<boolean>(false);
// detail loading
const detailLoading = ref<boolean>(false);
// 问题搜索
const searchKeyword = ref<string>("");
// 问题列表
const questList = ref<questListItem[]>([]);
// 当前选中的问题
const curSelectQues = ref<questListItem>({} as questListItem);
// 状态tag 映射
const statusTagMap: Record<questListItem["status"], any> = {
  pending: {
    class: "tag-pending",
    type: "primary",
    text: "待定",
  },
  enabled: {
    class: "tag-enabled",
    type: "success",
    text: "启用",
  },
  disabled: {
    class: "tag-disabled",
    type: "danger",
    text: "禁用",
  },
};
// 折叠面板值
const detailCollapseVals = ref<string[]>(["sqlConfig", "chartConfig"]);
// 详情表单实例
const quesDetailForm = ref<any>(null);
// 详情表单校验
const quesDetailRules = ref({
  admin_feedback_sql: [
    {
      required: true,
      message: "请输入管理员评估意见",
      trigger: "blur",
    },
  ],
  admin_feedback_chart: [
    {
      required: true,
      message: "请输入管理员评估意见",
      trigger: "blur",
    },
  ],
});
// 问题搜索表单
const quesFilterData = ref({
  timeRange: [],
  status: "",
  resultType: "",
  resultOpera: "",
  resultValue: "",
});
// 问题搜索pop 显示
const filterPopVisible = ref<boolean>(false);
// 问题页数
const quesPage = ref<number>(1);
// 问题总数
const quesTotal = ref<number>(0);

// ---------------------------------------

function formatTime(time: string | Date, format?: string) {
  return time
    ? dayjs.utc(time).local().format(format || "YYYY-MM-DD HH:mm:ss")
    : "-";
}

// 问题过滤表单change 触发搜索
function handleSearchQues() {
  quesPage.value = 1;
  let { timeRange, status, resultType, resultValue } = quesFilterData.value;
  let searchData: any = {
    key_word: searchKeyword.value,
    status: status === "all" ? "" : status,
    page: quesPage.value,
    page_size: 10,
  };
  // 处理时间选择，必须有两个时间才触发
  if (timeRange && timeRange.length === 2) {
    let [start_date, end_date] = timeRange;
    searchData.start_date = start_date;
    searchData.end_date = end_date;
  }
  // 处理评估结果查询
  if (resultType && resultValue) {
    if (resultType === "sql") {
      searchData.model_result_sql = resultValue;
    } else {
      searchData.model_result_chart = resultValue;
    }
  }
  getQuestionList(true, searchData);
}

// 获取问题详情
async function getDetialInfo(id: string) {
  detailLoading.value = true;
  nextTick(async () => {
    if (id) {
      await getMemoryDetailById(id)
        .then((res: any) => {
          curSelectQues.value = res.data;
          // quesDetailForm.value.setFieldsValue(res.data);
          detailLoading.value = false;
        })
        .catch(() => {
          detailLoading.value = false;
        });
    }
  });
}

//  获取问题列表
async function getQuestionList(isInitSelect: boolean = true, data?: object) {
  loading.value = true;
  questList.value = [];
  // eslint-disable-next-line prefer-const
  let { timeRange, status, resultType, resultValue } = quesFilterData.value;
  let [start_date = "", end_date = ""] = timeRange || [];

  let reqData: any = {
    app_code: curEditAgent.value,
    page: quesPage.value,
    page_size: 10,
    start_date,
    end_date,
    key_word: searchKeyword.value,
    status,
  };
  // 处理评估结果查询
  if (resultType && resultValue) {
    if (resultType === "sql") {
      reqData.model_result_sql = resultValue;
    } else {
      reqData.model_result_chart = resultValue;
    }
  }
  await memoryList({
    ...reqData,
    ...data,
  })
    .then(async (res: any) => {
      loading.value = false;
      questList.value = res?.data?.items || [];
      quesTotal.value = res?.data?.total || 0;
      // 默认初始化选中第一个
      if (isInitSelect && questList.value.length) {
        // handleSelectQuestion(questList.value[0]);
        await getDetialInfo(questList?.value[0]?.id);
      }
      // 数据为空且上次有数据时，清空
      if (!questList.value.length && !!curSelectQues.value) {
        curSelectQues.value = {} as questListItem;
      }
    })
    .catch(() => (loading.value = false));
}

// 评估结果调整更新状态
function handleResultChange() {
  nextTick(() => {
    // 确保已经选中了问题
    if (curSelectQues.value.id) {
      const { admin_result_sql, admin_result_chart }: any = curSelectQues.value;
      if (admin_result_sql === "correct" && admin_result_chart === "correct") {
        curSelectQues.value.status = "enabled";
      } else if (
        admin_result_sql === "wrong" &&
        admin_result_chart === "wrong"
      ) {
        curSelectQues.value.status = "disabled";
      }
    }
  });
}

// 保存
async function handleSave() {
  nextTick(() => {
    quesDetailForm.value.validate((valid) => {
      console.log(valid, curSelectQues.value);
      if (valid) {
        let {
          id,
          status,
          admin_feedback_chart,
          admin_feedback_sql,
          admin_result_chart,
          admin_result_sql,
        } = curSelectQues.value;
        // sql 和 图形、status之间有一个有结果
        if (admin_result_chart || admin_result_sql || status) {
          let reqData:any = {
            status,
            workspace_id: route?.query?.spaceCode
          };
          // 拼接 管理员评估参数
          if (admin_result_sql) {
            reqData = {
              ...reqData,
              admin_result_sql,
              admin_feedback_sql,
            };
          }
          if (admin_result_chart) {
            reqData = {
              ...reqData,
              admin_result_chart,
              admin_feedback_chart,
            };
          }
          // 更新记忆
          detailLoading.value = true
          updateMemoryDetailById(id, reqData).then(async (res:any) => {
            detailLoading.value = false
            // 重新获取详情、列表
            await getQuestionList(false)
            await getMemoryDetailById(id)
          }).catch(() => detailLoading.value = false);
        }
      }
    });
  });
}

// 选择问题
function handleSelectQuestion(item: questListItem) {
  getDetialInfo(item.id);
  // curSelectQues.value = cloneDeep(item);
}

// 手动触发大模型评估
function handleAssessment(type: string) {
  detailLoading.value = true;
  let { s2sql, question, id } = curSelectQues.value;
  modelEvaluate({
    type,
    original_question: question,
    generated_sql: s2sql,
    auto_save: true,
    record_id: id,
    ...curSelectQues.value,
  })
    .then((res: any) => {
      detailLoading.value = false;
      let {
        model_feedback_chart,
        model_feedback_sql,
        model_result_chart,
        model_result_sql,
      } = res.data;
      // 根据返回的结果重置显示内的数据
      if (type === "sql") {
        curSelectQues.value = {
          ...curSelectQues.value,
          model_feedback_sql,
          model_result_sql,
        };
      } else {
        curSelectQues.value = {
          ...curSelectQues.value,

          model_feedback_chart,
          model_result_chart,
        };
      }
      console.log(res, 321312);
    })
    .catch(() => {
      detailLoading.value = false;
    });
}

onUnmounted(() => {
  // 销毁时重置状态
  filterPopVisible.value = false;
});

onMounted(async () => {
  await getQuestionList();
});

defineExpose({
  getQuestionList,
});
</script>

<template>
  <div class="memory-manage" v-loading="loading">
    <!-- 左侧问题列表 -->
    <div class="ques-content">
      <p class="title">问题列表</p>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          placeholder="关键字..."
          v-model="searchKeyword"
          @keydown.enter="handleSearchQues"
          :prefix-icon="Search"
        />
        <el-popover
          class="box-item"
          :visible="filterPopVisible"
          placement="right-end"
          width="480"
        >
          <template #reference>
            <i
              class="iconfont ChatData-shaixuan filter-icon"
              @click.stop="filterPopVisible = !filterPopVisible"
            />
          </template>
          <div class="ques-filter-header">
            <p class="title">筛选</p>
            <el-icon @click="filterPopVisible = false" style="cursor: pointer">
              <Close />
            </el-icon>
          </div>
          <el-form
            :model="quesFilterData"
            class="ques-filter-data"
            label-width="120"
            label-position="left"
          >
            <el-form-item label="日期区间" class="time-range">
              <el-date-picker
                v-model="quesFilterData.timeRange"
                type="daterange"
                range-separator="~"
                @change="handleSearchQues"
                @clear="handleSearchQues"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
            <el-form-item label="状态" class="status-filter">
              <el-select
                v-model="quesFilterData.status"
                placeholder="请选择"
                @change="handleSearchQues"
                clearable
              >
                <el-option value="all" label="全部" />
                <el-option
                  v-for="(item, key) in statusTagMap"
                  :key="key"
                  :label="item.text"
                  :value="key"
                />
              </el-select>
            </el-form-item>
            <el-form-item
              label="大模型评估结果"
              label-position="top"
              class="result"
            >
              <!-- <el-space wrap> -->
              <el-select
                v-model="quesFilterData.resultType"
                placeholder="请选择"
                @change="handleSearchQues"
                clearable
              >
                <el-option value="sql" label="SQL配置参数" />
                <el-option value="chart" label="图表配置参数" />
              </el-select>
              <!-- <el-select
                v-model="quesFilterData.resultOpera"
                placeholder="请选择"
                clearable
              >
                <el-option value="sql" label="包含" />
                <el-option value="chart" label="等于" />
              </el-select> -->
              <el-select
                v-model="quesFilterData.resultValue"
                placeholder="请选择"
                @change="handleSearchQues"
                clearable
              >
                <el-option value="correct" label="正确" />
                <el-option value="wrong" label="错误" />
              </el-select>
              <!-- </el-space> -->
            </el-form-item>
          </el-form>
        </el-popover>
      </div>
      <!-- 问题列表区域 -->
      <div class="ques-list">
        <div
          class="ques-item"
          :class="[curSelectQues?.id === item.id && 'active-ques-item']"
          v-for="item in questList"
          :key="item.id"
          @click="handleSelectQuestion(item)"
        >
          <el-tooltip class="question" :content="item.question">
            <div class="question">
              {{ item.question }}
            </div>
          </el-tooltip>
          <div class="ques-item-bottom">
            <span class="ques-status" v-if="item.status">
              <el-tag
                :type="statusTagMap[item.status].type"
                plain
                :class="statusTagMap[item.status].class"
                >{{ statusTagMap[item.status].text }}</el-tag
              >
            </span>
            <span class="create-time"
              >新增时间：{{ formatTime(item.created_at) }}</span
            >
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <customPage
        v-model:current-page="quesPage"
        :total="quesTotal"
        @current-change="getQuestionList(false)"
      />
    </div>
    <!-- 右侧详情区域 -->
    <div
      class="ques-detail"
      v-if="!!curSelectQues?.id"
      v-loading="detailLoading"
    >
      <!-- 头部 -->
      <div class="header">
        <p class="title">
          {{ curSelectQues.question }}
        </p>
        <div class="options">
          <span class="create-time"
            >新增时间：{{ formatTime(curSelectQues.created_at) }}</span
          >
          <div class="status">
            状态：
            <template v-if="curSelectQues.status">
              <el-tag
                :type="statusTagMap[curSelectQues.status].type"
                plain
                :class="statusTagMap[curSelectQues.status].class"
              >
                {{ statusTagMap[curSelectQues.status].text }}
              </el-tag>
            </template>
            <el-dropdown
              @command="(key) => (curSelectQues.status = key)"
              v-if="canEdit"
            >
              <span class="el-dropdown-link">
                <el-icon
                  class="el-icon--right"
                  style="vertical-align: middle; cursor: pointer"
                >
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    :command="key"
                    v-for="(item, key) in statusTagMap"
                    :key="key"
                  >
                    <el-space wrap :size="40">
                      <el-tag :type="item.type" plain :class="item.class">
                        {{ item.text }}
                      </el-tag>
                      <el-icon
                        color="#005ee0"
                        v-if="curSelectQues.status === key"
                      >
                        <Select />
                      </el-icon>
                    </el-space>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <el-button
            size="small"
            type="primary"
            v-if="canEdit"
            @click="handleSave"
          >
            保存
          </el-button>
        </div>
      </div>
      <!-- 详情内容 -->
      <el-form
        :model="curSelectQues"
        label-width="180"
        :rules="quesDetailRules"
        ref="quesDetailForm"
        class="quesDetailForm"
        label-position="left"
      >
        <el-collapse class="ques-detail-collapse" v-model="detailCollapseVals">
          <!-- sql配置 -->
          <el-collapse-item name="sqlConfig" :icon="ArrowRightBold">
            <template #title>
              <span class="pre-title"> SQL配置参数 </span>
              <el-button
                @click.stop="handleAssessment('sql')"
                style="order: 1"
                size="small"
                type="primary"
                v-if="canEdit"
                plain
              >
                大模型评估
              </el-button>
            </template>
            <el-descriptions :column="1" size="large" border>
              <el-descriptions-item label-width="180" label="Schema映射">
                {{ curSelectQues?.schema_mapping }}
              </el-descriptions-item>
              <el-descriptions-item label="语义S2SQL">
                {{ curSelectQues?.s2sql }}
              </el-descriptions-item>
              <el-descriptions-item label="大模型评估意见">
                {{ curSelectQues?.model_feedback_sql }}
              </el-descriptions-item>
              <el-descriptions-item label="大模型评估结果">
                <template v-if="curSelectQues?.model_result_sql">
                  {{
                    curSelectQues?.model_result_sql === "correct"
                      ? "正确"
                      : "错误"
                  }}
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(curSelectQues?.updated_at) }}
              </el-descriptions-item>
            </el-descriptions>
            <template v-if="canEdit">
              <el-form-item label="管理员评估结果" style="margin-top: 15px">
                <el-radio-group
                  v-model="curSelectQues.admin_result_sql"
                  @change="handleResultChange"
                >
                  <el-radio value="correct" size="large"> 正确 </el-radio>
                  <el-radio value="wrong" size="large"> 错误 </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="管理员评估意见"
                :key="`${curSelectQues.id + curSelectQues.admin_result_sql}sql`"
                :prop="
                  curSelectQues.admin_result_sql === 'wrong'
                    ? 'admin_feedback_sql'
                    : ''
                "
              >
                <el-input
                  :rows="5"
                  v-model="curSelectQues.admin_feedback_sql"
                  type="textarea"
                />
              </el-form-item>
            </template>
          </el-collapse-item>
          <!-- 图表配置 -->
          <el-collapse-item name="chartConfig" :icon="ArrowRightBold">
            <template #title>
              <span class="pre-title"> 图表配置参数 </span>
              <el-button
                @click.stop="handleAssessment('chart')"
                style="order: 1"
                size="small"
                type="primary"
                v-if="canEdit"
                plain
              >
                大模型评估
              </el-button>
            </template>
            <el-descriptions :column="1" size="large" border>
              <el-descriptions-item label-width="180" label="语义S2SQL">
                {{ curSelectQues?.s2sql }}
              </el-descriptions-item>
              <el-descriptions-item label="图表配置参数">
                {{ curSelectQues?.chart_config }}
              </el-descriptions-item>
              <el-descriptions-item label="大模型评估意见">
                {{ curSelectQues?.model_feedback_chart }}
              </el-descriptions-item>
              <el-descriptions-item label="大模型评估结果">
                <template v-if="curSelectQues?.model_result_chart">
                  {{
                    curSelectQues?.model_result_chart === "correct"
                      ? "正确"
                      : "错误"
                  }}
                </template>
              </el-descriptions-item>
              <el-descriptions-item label="更新时间">
                {{ formatTime(curSelectQues?.updated_at) }}
              </el-descriptions-item>
            </el-descriptions>
            <template v-if="canEdit">
              <el-form-item label="管理员评估结果" style="margin-top: 15px">
                <el-radio-group
                  v-model="curSelectQues.admin_result_chart"
                  @change="handleResultChange"
                >
                  <el-radio value="correct" size="large"> 正确 </el-radio>
                  <el-radio value="wrong" size="large"> 错误 </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="管理员评估意见"
                :key="`${
                  curSelectQues.id + curSelectQues.admin_result_chart
                }chart`"
                :prop="
                  curSelectQues.admin_result_chart === 'wrong'
                    ? 'admin_feedback_chart'
                    : ''
                "
              >
                <el-input
                  :rows="5"
                  v-model="curSelectQues.admin_feedback_chart"
                  type="textarea"
                />
              </el-form-item>
            </template>
          </el-collapse-item>
        </el-collapse>
      </el-form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
@mixin ques-item-highlight {
  background: #ebf3fd;
  border-radius: 4px;
  .question {
    color: #005ee0 !important;
  }
}
.memory-manage {
  height: 100%;
  overflow: hidden;
  display: flex;
  .ques-content {
    width: 380px;
    box-shadow: 1px 0px 0px 0px #ededed;
    padding: 20px 10px 10px 0px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 15px;
    .title {
      font-weight: 600;
      font-size: 14px;
      color: #262626;
      line-height: 18px;
      margin: 0px;
    }
    .search-area {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-right: 5px;
      .filter-icon {
        font-size: 14px;
        color: #4c6f88;
        cursor: pointer;
      }
    }
    .ques-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      flex: 1;
      overflow: auto;
      .active-ques-item {
        @include ques-item-highlight;
        .question {
          font-weight: bold;
        }
      }
      .ques-item {
        height: 70px;
        box-sizing: border-box;
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;

        cursor: pointer;
        &:hover {
          @include ques-item-highlight;
        }
        .question {
          font-size: 13px;
          color: #262626;
          line-height: 18px;
          overflow: hidden;
          text-overflow: ellipsis;
          text-wrap: nowrap;
        }
        .ques-item-bottom {
          display: flex;
          align-items: center;
          gap: 25px;
          .create-time {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 14px;
          }
        }
      }
    }
  }
  .ques-detail {
    flex: 1;
    overflow-y: auto;
    padding: 20px 20px 15px 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    gap: 15px;
    .header {
      display: flex;
      align-items: center;
      gap: 10px;
      .title {
        margin: 0px;
        font-weight: bold;
        font-size: 14px;
        color: #262626;
        line-height: 18px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;
      }
      .options {
        display: flex;
        align-items: center;
        gap: 15px;
        .status {
          display: flex;
          align-items: center;
          gap: 5px;
        }
        .create-time {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 14px;
        }
      }
    }
    :deep(.ques-detail-collapse) {
      border: none;
      .el-collapse-item__header {
        display: flex;
        align-items: center;
        gap: 20px;
        border: none;
      }
      .el-collapse-item__arrow {
        margin: 0px;
        margin-right: auto;
        color: #4c6f88;
      }
      .el-collapse-item__wrap {
        border: none;
      }
      .el-descriptions__label {
        background: #fcfcfc;
        color: #595959;
      }
    }
    .pre-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 13px;
      &::before {
        content: "";
        height: 12px;
        width: 3px;
        display: inline-block;
        background: #005ee0;
      }
    }
    :deep(.quesDetailForm) {
      overflow: auto;
      .el-form-item__label {
        padding-left: 15px;
      }
    }
  }
}
.tag-pending {
  background: #ebf3fd;
  font-weight: bold;
  font-size: 12px;
  color: #005ee0;
}
.tag-enabled {
  background: #dff6eb;
  font-weight: bold;
  font-size: 12px;
  color: #2bbf79;
}
.tag-disabled {
  background: #ffeced;
  font-weight: bold;
  font-size: 12px;
  color: #fd033b;
}

.ques-filter-data {
  .time-range,
  .status-filter {
    :deep(.el-form-item__label) {
      background: #fafafa;
      border-radius: 4px;
      border: 1px solid #ededed;
      margin-right: 6px;
      padding-left: 12px;
      font-size: 13px;
      color: #595959;
    }
  }
  .result {
    :deep(.el-form-item__content) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 5px;
      box-sizing: border-box;
    }
  }
}
.ques-filter-header {
  display: flex;
  align-items: center;
  .title {
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    line-height: 20px;
    flex: 1;
  }
}
</style>
