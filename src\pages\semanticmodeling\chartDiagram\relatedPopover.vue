<script setup>
import { getDatasourceTableFieldListApi } from "@/common/apis/llm";
import { onMounted, ref } from "vue";
import { useVueFlow } from '@vue-flow/core';
import { cloneDeep, get } from 'lodash';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  modelType: {
    type: String,
    default: 'edit'
  }
})

const iconStyle = {
  display: 'inline-block',
  width: '20px',
  height: '20px',
  fontSize: '13px',
  textAlign: 'center',
  lineHeight: '18px',
  border: 'solid 1px #98BEF5',
  borderRadius: '50%',
  color: '#005EE0',
  cursor: 'pointer'
}

const iconMap = {
  inner: 'ChatData-guanlian-neibu',
  left: 'ChatData-guanlian-zuoce',
  right: 'ChatData-guanlian-youce',
  out: 'ChatData-guanlian-wanquanwaibu'
}

const operations = [
  { label: '等于',value: '=' },
  { label: '不等于',value: '!=' },
  { label: '小于',value: '<' },
  { label: '小于等于',value: '<=' },
  { label: '大于',value: '>' },
  { label: '大于等于',value: '>=' },
]
const { findEdge,updateNode } = useVueFlow()
let visible = ref(false);
let visible1 = ref(false);
const conditions = ref([
  {
    left_data_type: '',
    left_name:'',
    left_field_id:'',
    left_table_id:'',
    operator: '=',
    right_data_type: '',
    right_name:'',
    right_field_id:'',
    right_table_id:'',
  }
])
const letfConditionList = ref([]);
const rightConditionList = ref([]);
let EdgeInfo = ref(); // 边和节点信息
const buttonRef = ref();
const conectionRef = ref();

let conectionType = ref('inner');
const getMethodLabel = computed(() => {
  let label = '';
  switch(conectionType.value) {
    case 'inner':
      label = '内部';
      break;
    case 'left':
      label = '左侧';
      break;
    case 'right':
      label = '右侧';
      break;
    case 'out':
      label = '完全外部';
      break;
  }
  return label;
})


function onShow(){
  // 节点更新的时候需要重新获取数据
  EdgeInfo.value = findEdge(props.id);
  console.log(EdgeInfo.value);
  getConditionList();
  // 有配置条件时 显示初始条件
  if (EdgeInfo?.value?.targetNode?.join) {
    conditions.value = [...cloneDeep(get(EdgeInfo.value, 'targetNode.join.conditions', []))];
    conectionType.value = get(EdgeInfo.value, 'targetNode.join.method', 'inner');
  }
}

function onSelect(type) {
  conectionType.value = type;
  updateNode(EdgeInfo.value.targetNode.id, {
    join: {
      method: conectionType.value,
      conditions:conditions.value
    }
  })
  visible1.value = false;
}
// 获取字段列表
function getConditionList() {
  letfConditionList.value = [];
  rightConditionList.value = [];
  getDatasourceTableFieldListApi(EdgeInfo.value.sourceNode.data.id).then((res) => {
    letfConditionList.value = res.data;
  });
  getDatasourceTableFieldListApi(EdgeInfo.value.targetNode.data.id).then((res) => {
    rightConditionList.value = res.data;
  });
}
function addConditionRow() {
  const last = conditions.value[conditions.value.length - 1]
  // 检查所有字段是否填写
  if (!last.left_field_id || !last.right_field_id || !last.operator) {
    ElMessage.warning('请先填写完整上一条条件')
    return
  }
  // 新增一行
  conditions.value.push({
    left_data_type: '',
    left_name:'',
    left_field_id:'',
    left_table_id:'',
    operator: '=',
    right_data_type: '',
    right_name:'',
    right_field_id:'',
    right_table_id:'',
  })
}
function clearAll(){
  conditions.value = [{
    left_data_type: '',
    left_name:'',
    left_field_id:'',
    left_table_id:'',
    operator: '=',
    right_data_type: '',
    right_name:'',
    right_field_id:'',
    right_table_id:'',
  }]
  conectionType.value = 'inner';
  updateNode(EdgeInfo.value.targetNode.id, {
    join: null
  })
}
function changeGlobalNodesTarget(item,type) {
  if(type === 'left') {
    // 根据id查到对象
    let leftItem = letfConditionList.value.find((val) => val.id === item.left_field_id)
    item.left_name = leftItem.field;
    item.left_data_type = leftItem.data_type;
    item.left_table_id = EdgeInfo.value.sourceNode.data.id;
  } else if(type === 'right') {
    let rightItem = rightConditionList.value.find((val) => val.id === item.right_field_id)
    item.right_name = rightItem.field;
    item.right_data_type = rightItem.data_type;
    item.right_table_id = EdgeInfo.value.targetNode.data.id;
  }
  if(item.left_field_id && item.right_field_id) {
    // 左右条件都选择后，重新组装数据
    updateNode(EdgeInfo.value.targetNode.id, {
      join: {
        method: conectionType.value,
        conditions:conditions.value
      }
    })
  }
}
// 行删除
function removeConditionRow(item,idx){
  if(conditions.value.length>1) {
    conditions.value.splice(idx, 1)
    updateNode(EdgeInfo.value.targetNode.id, {
      join: {
        method: conectionType.value,
        conditions:conditions.value
      }
    })
  }
}

</script>

<template>
  <el-popover
    :visible="visible"
    width="480"
    placement="bottom"
    popper-class="chart-related-popover"
    @show="onShow"
  >
    <!-- 下拉框的apend to 容器 -->
    <!-- <div id="relatedPopoverAppend"></div> -->
    <div class="title flex-row">
      <span class="flex1">关联关系</span>
      <i class="iconfont ChatData-guanbi" @click="visible = false"></i>
    </div>
    <div class="flex-row related" v-if="EdgeInfo">
      <div class="left flex1">
        左表
        <div class="sheet">{{ EdgeInfo.sourceNode.data.table_name }}</div>
      </div>
      <div class="inline flex-column">
        {{getMethodLabel}}
        <i :class="['iconfont', iconMap[conectionType]]" style="font-size: 24px; color: #005EE0;"></i>
        <el-popover
          :visible="visible1"
          width="300"
          placement="bottom"
          v-if="modelType === 'edit'"
          popper-class="chart-related-in-popover"
        >
          <div class="flex-row conections">
            <div class="flex-column flex1" :class="{ 'selected': conectionType === 'inner' }" @click="onSelect('inner')">内部<i class="iconfont ChatData-guanlian-neibu"></i></div>
            <div class="flex-column flex1" :class="{ 'selected': conectionType === 'left' }" @click="onSelect('left')">左侧<i class="iconfont ChatData-guanlian-zuoce"></i></div>
            <div class="flex-column flex1" :class="{ 'selected': conectionType === 'right' }" @click="onSelect('right')">右侧<i class="iconfont ChatData-guanlian-youce"></i></div>
            <div class="flex-column" :class="{ 'selected': conectionType === 'out' }" @click="onSelect('out')">完全外部<i class="iconfont ChatData-guanlian-wanquanwaibu"></i></div>
          </div>
          <template #reference><i class="iconfont ChatData-danjiantouxia" @click="visible1 = !visible1"></i></template>
        </el-popover>
      </div>
      <div class="right flex1">
        右表
        <div class="sheet">{{ EdgeInfo.targetNode.data.table_name }}</div>
      </div>
    </div>
    <div class="condition">条件 <span style="color: #FD033B;">*</span></div>
    <div class="condition-list">
      <div class="condition-item flex-row" v-for="(item,idx) in conditions">
        <el-select class="flex1" v-model="item.left_field_id" @change="changeGlobalNodesTarget(item,'left')" :disabled="modelType === 'view'">
          <el-option v-for="field in letfConditionList" :key="field.id" :label="field.alias" :value="field.id"/>
        </el-select>
        <el-select style="width: 90px;" v-model="item.operator" :disabled="modelType === 'view'">
          <el-option v-for="item in operations" :key="item.label" :label="item.label" :value="item.value"/>
        </el-select>
        <el-select class="flex1" v-model="item.right_field_id" @change="changeGlobalNodesTarget(item,'right')" :disabled="modelType === 'view'">
          <el-option v-for="item in rightConditionList" :key="item.id" :label="item.alias" :value="item.id"/>
        </el-select>
        <!-- 添加行删除 -->
         <div class="right-operate" style="width:40px" v-if="modelType === 'edit'">
            <el-button v-if="conditions.length > 1"
            type="primary"
            plain
            link
            @click="removeConditionRow(item,idx)"
            style="margin-left: 8px;"
          > <el-icon><Delete /></el-icon></el-button>
         </div>
      </div>
    </div>
    <div class="tools" v-if="modelType === 'edit'">
      <el-button type="primary" size="small" @click="addConditionRow">添加</el-button>
      <el-button type="info" plain size="small" @click="clearAll">全部清除</el-button>
    </div>
    <template #reference><i class="iconfont ChatData-guanlian-zuoce" :style="iconStyle" @click="visible = !visible"></i></template>
  </el-popover>
</template>

<style lang="scss">
 .chart-related-popover{
  .title{
    margin-bottom: 15px;
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    .ChatData-guanbi{
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      font-size: 14px;
      color: #8c8c8c;
      cursor: pointer;
    }
  }
  .ChatData-danjiantouxia{
    cursor: pointer;
  }
  .related{
    text-align: center;
    gap: 10px;
    .left, .inline, .right{
      padding: 10px;
      border-radius: 4px;
      background: #FCFCFC;
      border: 1px solid #EFF0F2;
    }
    .inline{
      width: 90px;
      padding: 5px 10px;
    }
    .sheet{
      margin-top: 10px;
      height: 36px;
      line-height: 36px;
      padding-left: 10px;
      padding-right: 5px;
      border-radius: 4px;
      text-align: left;
      background: #FFFFFF;
      border: 1px solid #EDEDED;
      border-left: solid 3px #5167FF;
    }
  }
  .condition{
    margin-top: 20px;
    margin-bottom: 15px;
  }
  .condition-list{
    max-height: 300px;
    overflow: auto;
    .condition-item{
      margin-bottom: 5px;
      gap: 10px;
    }
  }
  .tools{
    margin: 20px 0;
  }
 }
 .chart-related-in-popover{
  .conections{
    gap: 10px;
    >div{
      gap: 8px;
      align-items: center;
      padding: 5px 8px;
      background: #FCFCFC;
      border-radius: 4px;
      border: 1px solid #EFF0F2;
      cursor: pointer;
      &.selected{
        color: #005EE0;
        background: #EBF3FD;
        border-radius: 4px;
        border: 1px solid #98BEF5;
        .iconfont{
          color: #005EE0;
        }
      }
    }
    .iconfont{
      width: 20px;
      height: 14px;
      line-height: 14px;
      font-size: 20px;
      color: #4C6F88;
    }
  }
 }

</style>