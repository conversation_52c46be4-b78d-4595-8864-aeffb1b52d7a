/**
 * TypeScript 配置文件
 * @link https://www.typescriptlang.org/tsconfig
 * @link https://cn.vuejs.org/guide/typescript/overview#configuring-tsconfig-json
 * @link https://cn.vite.dev/guide/features#typescript-compiler-options
 * @link https://www.npmjs.com/package/@types/vite-client 用于解决 vite/client 类型定义问题
 * @link https://element-plus.org/zh-CN/guide/typescript.html 用于解决 element-plus/global 类型定义问题
 */

{
  "compilerOptions": {
    "outDir": "./dist", // 输出目录
    "rootDir": "./src", // 输入目录
    "target": "esnext",
    "jsx": "preserve",
    "jsxImportSource": "vue",
    "lib": ["esnext", "dom"],
    "useDefineForClassFields": true,
    "experimentalDecorators": true,
    // baseUrl 用来告诉编译器到哪里去查找模块，使用非相对模块时必须配置此项
    "baseUrl": ".",
    "module": "esnext",
    "moduleResolution": "node",
    // 非相对模块导入的路径映射配置，根据 baseUrl 配置进行路径计算，与 vite.config 中 alias 配置同步
    "paths": {
      "@/*": ["src/*"],
      "@@/*": ["src/common/*"]
    },
    "resolveJsonModule": true,
    "types": ["vite/client", "element-plus/global", "node"],
    // 允许导入 .ts .mts .tsx 拓展名的文件
    "allowImportingTsExtensions": false,
    // 允许 JS
    "allowJs": true,
    // TS 严格模式
    "strict": false,
    "importHelpers": true,
    // 不输出任何编译后的文件，只进行类型检查
    "noEmit": false,
    "sourceMap": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  // 需要被编译的文件列表
  "include": ["src", "types"],
  // 从编译中排除的文件列表
  "exclude": ["node_modules", "dist"]
}
