export interface Database {
  id: number;
  type: string;
  description: string;
  gmt_created:String;
  gmt_modified:String;
  params:{
    database:String,
    driver:String,
    host:String,
    max_overflow:number,
    password:String,
    pool_pre_ping:boolean
    pool_recycle:number,
    pool_size:number,
    pool_timeout:number,
    port:number,
    user:String
  }
}

export type DatabaseType = 'mysql' | 'postgresql' | 'oracle' | 'sqlserver' | 'mongodb';

interface ApiResponse {
  data: Database[]
}