<script setup lang="ts">
import { Search } from "@element-plus/icons-vue"
import { ElMessage, ElMessageBox } from "element-plus"
import { computed, ref } from "vue"

interface Metric {
  id: string
  name: string
  category: string
  type: "基础指标" | "复合指标" | "自定义指标"
  description: string
  updateTime: string
}

const searchQuery = ref("")
const metricsList = ref<Metric[]>([
  {
    id: "1",
    name: "示例指标",
    category: "业务指标",
    type: "基础指标",
    description: "这是一个示例指标",
    updateTime: "2024-03-20 10:00:00"
  }
])

const filteredMetrics = computed(() => {
  return metricsList.value.filter(metric =>
    metric.name.toLowerCase().includes(searchQuery.value.toLowerCase())
    || metric.description.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})
function getTypeTag(type: string): "success" | "warning" | "info" {
  const typeMap: Record<string, "success" | "warning" | "info"> = {
    基础指标: "success",
    复合指标: "warning",
    自定义指标: "info"
  }
  return typeMap[type] || "info"
}

function handleCreate() {
  ElMessage.info("创建指标功能开发中")
}

function handleEdit(row: Metric) {
  ElMessage.info("编辑指标功能开发中")
}

function handleView(row: Metric) {
  ElMessage.info("查看指标详情功能开发中")
}

function handleDelete(row: Metric) {
  ElMessageBox.confirm("确定要删除该指标吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    const index = metricsList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      metricsList.value.splice(index, 1)
      ElMessage.success("删除成功")
    }
  })
}
</script>

<template>
  <div class="metrics-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>指标市场</span>
          <div class="header-operations">
            <el-input
              v-model="searchQuery"
              placeholder="搜索指标"
              class="search-input"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="handleCreate">
              创建指标
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="filteredMetrics" style="width: 100%">
        <el-table-column prop="name" label="指标名称" />
        <el-table-column prop="category" label="分类" width="120" />
        <el-table-column prop="type" label="类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getTypeTag(row.type)">
              {{ row.type }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button-group>
              <el-button type="primary" link @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="primary" link @click="handleView(row)">
                查看
              </el-button>
              <el-button type="danger" link @click="handleDelete(row)">
                删除
              </el-button>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<style scoped>
.metrics-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-operations {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.box-card {
  width: 100%;
}
</style>
