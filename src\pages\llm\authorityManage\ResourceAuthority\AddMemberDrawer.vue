<template>
  <el-drawer v-model="visible" title="添加成员" size="60%" direction="rtl" @close="onCancel">
    <div class="select-user">
      <div class="drawer-content">
        <div class="section-title">选择成员</div>
        <div class="selected-members">
          <ListShow
            labelField="name"
            :max="5"
            :dataList="userListIds"
          >
            <template #item="{ content }">
              <div class="user-tag">
                <span>{{ content.name }}</span>
                <el-icon  @click=" () => { onUserDel(content.user_id)} "><Close /></el-icon>
              </div>
            </template>
          </ListShow>
          <BusmMemberOrgBtn
            v-bind="$attrs"
            :isMultiple="true"
            :nodeKey="'user_id'"
            v-model:value="userListIds"
            @change="(list) => changeUser(list)"
            fromType="authorityManage"
          ></BusmMemberOrgBtn>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="perms-box">
        <div class="section-title perms-title">权限</div>
        <el-checkbox-group v-model="drawerPerms" class="perms-group">
          <el-checkbox @change="changeGroup" :label="item.prop" v-for="item in permissionColumns">{{ item.label }}</el-checkbox>
        </el-checkbox-group>
      </div>
      <div class="drawer-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="handleAddMembers">新增</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, computed,inject, watch } from 'vue';
import { ElMessage } from 'element-plus';
import ListShow from '@/pages/llm/workspaceManage/component/ListShow.vue'
import BusmMemberOrgBtn from '@/pages/llm/workspaceManage/component/BusmMemberOrgBtn.vue'

const permissionColumns = inject('permissionColumns', ref<any>([]))
const props = defineProps({
  modelValue: {
    type: Boolean,
    default:false
  }
})
const userListIds = ref([])
const drawerPerms = ref<string[]>(['view']);
const emits = defineEmits(['update:modelValue', 'add']);

const visible = computed({
  get: () => props.modelValue,
  set: v => emits('update:modelValue', v)
});

watch(visible, (val) => {
  if (val) {
    userListIds.value = [];
    drawerPerms.value = ['view'];
  }
});

function changeGroup(){
  // 自动勾选查看权限
  if (!drawerPerms.value.includes('view') && drawerPerms.value.length > 0) {
    drawerPerms.value.unshift('view');
  }
}
function handleAddMembers() {
  if(!userListIds.value.length) {
    ElMessage.warning('请添加成员！');
    return
  }
  if (!drawerPerms.value.includes('view') && drawerPerms.value.length > 0) {
    drawerPerms.value.unshift('view');
    return;
  }
  const uniqueNames = [...new Set(userListIds.value.map(item => item.username))]
  emits('add', { permission_type: drawerPerms.value,usernames:uniqueNames });
  emits('update:modelValue', false);
}
function onCancel() {
  emits('update:modelValue', false);
}
const onUserDel = (userId: number) => {
  userListIds.value = userListIds.value.filter(member => member.user_id !== userId)
  ElMessage.success('删除成功')
}
const changeUser =  (userList: any[]) => {
  userListIds.value = userList
}
</script>

<style scoped lang="scss">
.section-title {
  font-weight: bold;
  margin-bottom: 10px;
}
.perms-box {
  border-top: 1px solid #EDEDED;
  border-bottom: 1px solid #EDEDED;
  padding: 20px 10px;
  .perms-title {
    text-align: left;
  }
  .perms-group {
    display: flex;
    gap: 24px;
  }
}
.drawer-footer {
  margin-top: 32px;
  text-align: right;
}
.selected-members {
  display: flex;
  align-items: center;
}
.user-tag{
  display: flex;
  align-items: center;
  padding: 4px 6px;
  border-radius: 3px;
  color: #555;
  background: #F3F3F3;
  .el-icon-close{
    width: 20px;
    height: 20px;
    line-height: 20px;
    color: #ccc;
    text-align: center;
    &:hover{
      color: #005EE0;
      font-weight: bold;
      cursor: pointer;
    }
  }
}
</style>
