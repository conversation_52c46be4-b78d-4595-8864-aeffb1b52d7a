<template>
  <el-drawer :model-value="visible" direction="btt" size="70%" :with-header="false"
    @close="emit('update:visible', false)" body-class="field-meta-body" destroy-on-close>
    <div class="meta-header">
      <span class="meta-title">字段元信息</span>
      <el-button type="primary" size="small" v-if="!isView" @click="handleSave">保存</el-button>
      <el-button type="info" plain size="small" @click="emit('update:visible', false)">取消</el-button>
    </div>
    <div class="meta-toolbar">
      <span></span>
      <el-input class="meta-search" placeholder="请输入原名、别名" :prefix-icon="Search" @keydown.enter="handleSearch"
        v-model="search" />
    </div>
    <!-- 表头部 -->
    <div class="custom-header">
      <span>
        <el-space>
          <el-checkbox v-model="checkAll" @change="handleCheckAllChange" v-if="!isView"></el-checkbox>
          原名
        </el-space>
      </span>
      <span>别名</span>

      <span>字段类型</span>
      <span>字段属性</span>
      <span>字段说明</span>
      <span>注释</span>
      <span>是否可见</span>
    </div>
    <!-- 表内容 -->
    <el-tree-v2 v-if="!!visible && editData.length" :class="['field-meta-tree', isView ? 'field-meta-tree-view' : '']" :data="editData"
      :show-checkbox="!isView" node-key="id" ref="treeRef" :item-size="40" :indent="0"
      :defaultExpandedKeys="defaultExpandedKeys" :check-on-click-node="false" @check-change="handleCheckChange"
      :filter-method="filterNode" :height="calcTreeHeight">
      <template #default="{ node, data }">
        <!-- <span>{{ data.field || data.label }}</span> -->
        <div class="field-meta-tree-item" @click.stop="">
          <div class="field-name">{{ data.field || data.label }}</div>
          <!-- <el-input
            :disabled="data.hasOwnProperty('children')"
            v-model="data.label"
            placeholder="请输入原名"
            style="width: 180px"
          /> -->
          <template v-if="!data.hasOwnProperty('children')">
            <el-input v-model="data.alias" :key="data.id" placeholder="请输入别名" @change="data.label = data.alias" />
            <el-select v-model="data.data_type" placeholder="请选择" @change="batchOperate.data_type = ''">
              <el-option v-for="item in dataTypeMap" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="data.role" placeholder="请选择" @change="batchOperate.role = ''">
              <el-option label="维度" value="Dimension"></el-option>
              <el-option label="度量" value="Measure"></el-option>
            </el-select>
            <el-input v-model="data.description" placeholder="请输入字段说明" />
            <el-input v-model="data.annotation" placeholder="请输入注释" />
            <el-select v-model="data.ignore" placeholder="请选择" @change="batchOperate.ignore = ''">
              <el-option label="是" :value="false"></el-option>
              <el-option label="否" :value="true"></el-option>
            </el-select>
          </template>
        </div>
      </template>
    </el-tree-v2>
    <div class="meta-footer-operator" v-if="!isView">
      <span class="selection">
        批量操作：已选择
        <span class="selection-count">{{
          Object.keys(selectionCount).length
        }}</span>
        <!-- {{ selectIds }} -->
      </span>
      <template v-if="Object.keys(selectionCount).length">
        <el-button type="primary" plain @click="handleAliasToAnnotation">将别名修改为注释内容</el-button>
        <el-select placeholder="请选择字段类型" v-model="batchOperate.data_type"
          @change="(val) => handleBatchOperateValue(val, 'data_type')">
          <el-option v-for="item in dataTypeMap" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        <el-select placeholder="请选择字段属性" v-model="batchOperate.role"
          @change="(val) => handleBatchOperateValue(val, 'role')">
          <el-option label="维度" value="Dimension"></el-option>
          <el-option label="度量" value="Measure"></el-option>
        </el-select>
        <el-button type="primary" plain @click="handleDescriptionToAnnotation">将字段说明修改为注释内容</el-button>
        <span class="place"></span>
        <el-select placeholder="请选择是否可见" v-model="batchOperate.ignore"
          @change="(val) => handleBatchOperateValue(val, 'ignore')">
          <el-option label="是" :value="false"></el-option>
          <el-option label="否" :value="true"></el-option>
        </el-select>
      </template>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits } from "vue";
import { Search } from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";

const props = defineProps({
  visible: Boolean,
  data: {
    type: Array,
    default: () => [],
  },
  type: {
    type: String,
    default: "view",
  },
});
const batchOperate = ref({
  data_type: "",
  role: "",
  ignore: "",
});
const emit = defineEmits(["update:visible", "save"]);
const search = ref("");
// 存储选择的选项
const selectionCount = ref<Record<string, any>>({});
const editData = ref([]);
const isView = computed(() => props.type === "view");
const calcTreeHeight = ref<number>(0);
const dataTypeMap = ref([
  {
    label: "转换为整数",
    value: "Integer",
  },
  {
    label: "字符串",
    value: "String",
  },
  {
    label: "日期",
    value: "Date",
  },
  {
    label: "日期时间类型",
    value: "Datetime",
  },
  {
    label: "整数",
    value: "Whole",
  },
  {
    label: "浮点行",
    value: "Decimal",
  },
]);
// 获取存储选项的id
const selectIds = computed(() => {
  const selectIds = Object.values(selectionCount.value).map((item) => item.id);
  return selectIds;
});
// 默认展开的节点
let defaultExpandedKeys = ref<(string | number)[]>([]);
const checkAll = ref(false); // 全选
const treeRef = ref();
// --------------------------------------------------

// 搜索
function handleSearch() {
  treeRef.value.filter(search.value);
}
// 过滤节点
function filterNode(value, data) {
  if (!value) return true;
  let str = data.label || data.field;
  return str.indexOf(value) !== -1;
}

// 全选
function handleCheckAllChange() {
  editData.value.forEach((item) => {
    // item.checked = checkAll.value;
    treeRef.value.setChecked(item.id, checkAll.value, true);
    if (item.hasOwnProperty("children")) {

      item.children.forEach((child) => {
        treeRef.value.setChecked(child.id, checkAll.value);
        child.checked = checkAll.value;
        // 更新选项
        if (checkAll.value) {
          selectionCount.value[child.id] = child;
          // treeRef.value.setCheckedKeys([child.id]);
        } else {
          delete selectionCount.value[child.id];
          // treeRef.value.setCheckedKeys([]);
        }
      });
    }
  });
}

// 处理选中
function handleCheckChange(data, status) {
  // console.log(data, status, "data, status, child");
  if (data.hasOwnProperty("children")) {
    data.children.forEach((child) => {
      // child.checked = status;
      if (status) {
        selectionCount.value[child.id] = child;
      } else {
        delete selectionCount.value[child.id];
      }
    });
  } else {
    // data.checked = status;
    if (status) {
      selectionCount.value[data.id] = data;
    } else {
      delete selectionCount.value[data.id];
    }
  }
}

// 处理单元格合并
function handleSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (row.hasOwnProperty("children")) {
    // 如果是选择列，正常显示
    if (column.type === "selection") {
      return {
        rowspan: 1,
        colspan: 1,
      };
    }
    // 如果是原名列，占满除选择列外的所有列
    else if (column.property === "field") {
      return {
        rowspan: 1,
        colspan: 999, // 使用一个足够大的数字确保占满剩余列
      };
    }
    // 其他列不显示
    else {
      return {
        rowspan: 0,
        colspan: 0,
      };
    }
  }
  // 普通行不做处理
  return {
    rowspan: 1,
    colspan: 1,
  };
}

// 批量操作 数据
function handleBatchOperateValue(value, key) {
  editData.value.forEach((item) => {
    if (!!item.children && item.children.length) {
      item.children.forEach((child) => {
        if (selectIds.value.includes(child.id)) {
          child[key] = value;
        }
      });
    }
  });
}

// 将别名修改为注释内容
function handleAliasToAnnotation() {
  editData.value.forEach((item) => {
    if (!!item.children && item.children.length) {
      item.children.forEach((child) => {
        if (selectIds.value.includes(child.id)) {
          child.label = child.annotation;
          child.alias = child.annotation;
        }
      });
    }
  });
}

// 将字段说明修改为注释内容
function handleDescriptionToAnnotation() {
  editData.value.forEach((item) => {
    if (!!item.children && item.children.length) {
      item.children.forEach((child) => {
        if (selectIds.value.includes(child.id)) {
          child.description = child.annotation;
        }
      });
    }
  });
}

// 保存
function handleSave() {
  // console.log(editData.value, "editData");
  let dimensionData = [];
  let measureData = [];
  cloneDeep(editData.value).forEach((item: any, index: number) => {
    if (item.display_type === "table") {
      dimensionData.push({
        ...item,
        children: [],
      });
      measureData.push({
        ...item,
        children: [],
      });
      if (item.children.length) {
        item.children.forEach((child) => {
          if (child.role === "Dimension") {
            dimensionData[index].children.push(child);
          } else if (child.role === "Measure") {
            measureData[index].children.push(child);
          }
        });
      }
    }
  });
  emit("save", {
    dimensionData,
    measureData,
  });
  emit("update:visible", false);
  // console.log(dimensionData, measureData, "dimensionData, measureData");
}

watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 初始化数据
      defaultExpandedKeys.value = props.data.map((item: any) => item.id);
      editData.value = cloneDeep(props.data || [])

      // 初始化底部配置
      batchOperate.value = {
        data_type: "",
        role: "",
        ignore: "",
      };
      // 初始化选择
      selectionCount.value = {};
      // 初始化高度
      nextTick(() => {
        let height = document.querySelector(".field-meta-tree")?.clientHeight;
        calcTreeHeight.value = height;
      });
    } else {
      calcTreeHeight.value = 0;
      defaultExpandedKeys.value = [];
    }
  },
  {
    deep: true,
    flush: "post",
  }
);
</script>

<style scoped lang="scss">
.field-meta-tree {
  overflow: hidden;
  margin-bottom: 48px;
  flex: 1;

  // height: 500px;
  :deep(.el-tree-node__content) {
    height: 48px;
    // margin: 5px 0px;
    padding: 10px 0px;
    box-sizing: border-box;
    box-shadow: 0px 1px 0px 0px #EDEDED;
  }

  .field-name {
    width: 100%;
    line-height: 32px;
    padding: 0px 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &.field-meta-tree-view {
    :deep(.el-input) {
      --el-input-placeholder-color: transparent !important;
    }

    :deep(.el-input__wrapper),
    :deep(.el-select__wrapper) {
      box-shadow: none;
      pointer-events: none;
      background-color: transparent;

      .el-input__wrapper {
        &::placeholder {
          color: red;
        }
      }

      .el-select__suffix {
        display: none;
      }
    }
  }
}

.field-meta-tree-item,
.custom-header,
.meta-footer-operator {
  display: grid;
  grid-template-columns: 180px 180px 140px 140px 300px auto 80px;
  width: 100%;
  gap: 10px;
}

.custom-header {
  // margin-bottom: 10px;
  padding-left: 24px;
  font-weight: 600;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 14px;
  height: 32px;
  background: #fafafa;
  box-shadow: 0px 1px 0px 0px #ededed;

  &>span {
    height: 32px;
    line-height: 32px;
  }
}

.field-meta-body {
  .field-meta-table {
    flex: 1;
    overflow: auto;
  }

  .meta-footer-operator {
    // display: grid;
    // grid-template-columns: 255px 320px 140px 140px 300px auto 80px;
    // gap: 15px;
    padding-left: 46px;
    align-items: center;
    height: 48px;
    box-sizing: border-box;
    width: 100%;
    z-index: 200;
    position: absolute;
    bottom: 0px;
    background: #ffffff;
    box-shadow: 0px 0px 10px 0px #f2f2f2, 0px -1px 0px 0px #f2f2f2;
    padding-right: 50px;

    .el-button {
      width: 100%;
    }

    .selection-count {
      color: #005ee0;
    }
  }
}

.meta-header {
  display: flex;
  // justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;

  .meta-title {
    flex: 1;
    font-size: 16px;
    font-weight: bold;
    // margin-right: 10px;
  }
}

.meta-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.meta-search {
  width: 300px;
}

:deep(.alias-content) {
  .cell {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    // .el-input {
    //   width: 180px;
    // }
  }
}
</style>
<style lang="scss">
.field-meta-body {
  overflow: hidden !important;
  display: flex;
  flex-direction: column;

  .field-meta-table {
    flex: 1;
    overflow: auto;
  }
}
</style>
