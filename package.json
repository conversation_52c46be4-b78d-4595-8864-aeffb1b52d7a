{"name": "chatdata-ui", "type": "module", "version": "1.0.0", "description": "chatdata-ui-vue3", "scripts": {"dev": "vite", "build:staging": "vue-tsc && vite build --mode staging", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --fix", "test": "vitest"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@vue-flow/background": "1.3.2", "@vue-flow/core": "1.45.0", "axios": "1.8.4", "dayjs": "1.11.13", "echarts": "5.6.0", "element-plus": "2.9.7", "highlight.js": "11.11.1", "js-cookie": "3.0.5", "lodash-es": "4.17.21", "markdown-it": "14.1.0", "mitt": "3.0.1", "monaco-editor": "0.52.2", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "8.2.0", "pinia": "3.0.2", "screenfull": "6.0.2", "uuid": "11.1.0", "vue": "3.5.13", "vue-router": "4.5.0", "vxe-table": "4.6.25"}, "devDependencies": {"@antfu/eslint-config": "4.12.0", "@stagewise/toolbar-vue": "0.1.2", "@types/js-cookie": "3.0.6", "@types/lodash-es": "4.17.12", "@types/node": "22.14.1", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.3", "@vitejs/plugin-vue": "5.2.3", "@vitejs/plugin-vue-jsx": "4.1.2", "@vue/test-utils": "2.4.6", "eslint": "9.24.0", "eslint-plugin-format": "1.0.1", "happy-dom": "17.4.4", "lint-staged": "15.5.1", "sass": "1.78.0", "typescript": "5.8.3", "unocss": "66.1.0-beta.12", "unplugin-auto-import": "19.1.2", "unplugin-svg-component": "0.12.1", "unplugin-vue-components": "28.5.0", "vite": "6.3.2", "vite-svg-loader": "5.1.0", "vitest": "3.1.1", "vue-tsc": "2.2.8"}, "lint-staged": {"*": "eslint --fix"}}