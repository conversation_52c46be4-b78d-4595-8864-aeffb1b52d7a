import { defineStore } from 'pinia'
import { getSapceListApi } from '@/common/apis/workspace/index'

export const useWorkspaceStore = defineStore('workspace', {
  state: () => ({
    spaces: JSON.parse(localStorage.getItem('spaceList')) || []
  }),
  actions: {
    async fetchSpaces() {
      const res: any = await getSapceListApi()
      this.spaces = res?.data || []
      localStorage.setItem('spaceList', JSON.stringify(this.spaces));
      return this.spaces
    }
  }
})
