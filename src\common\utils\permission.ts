import { useUserStore } from "@/pinia/stores/user"
import { isArray } from "@@/utils/validate"
import { tr } from "element-plus/es/locale"

/** 全局权限判断函数，和权限指令 v-permission 功能类似 */
export function checkPermission(permissionRoles: any,id:string): boolean {
  const { privileges } = useUserStore()
    const { workspace_permissions, role_name } = privileges as any
    const scope = permissionRoles[0].split(':')[0]
    const permission = permissionRoles[0].split(':')[1]
    if(isArray(permissionRoles) && permissionRoles.length > 0) {
      // 空间维度权限 判断 scope 是否在 spaceDimension 中
      const spaceDimension = ['workspace','member','agent','data_subject','data_model']
      if(spaceDimension.includes(scope)){
        // 空间维度权限（空间管理，成员管理、助手新增、模型主题新增、模型新增）
        const PrivilegesData = workspace_permissions.find(item => item.category === scope)?.permissions || []
        const hasPermission = PrivilegesData.includes(permission)
        return hasPermission
      } else {
        // 数据维度  单个助手，数据主题，数据模型
        const privilegesAllData = privileges[scope]
        const curPrivilegesData = privilegesAllData.find(item=> item.data_model_id
          == id || item.agent_id == id)?.permissions || []
        if(role_name === 'space_admin' || role_name === 'super_admin') {  // 当前用户为空间管理员
           if(!privilegesAllData.length || !curPrivilegesData.length) {
            return true   // 无制定数据，则拥有所有权限
           }
        } else {
          // 非管理员无返回数据则 默认只能查看
          if(!privilegesAllData.length || !curPrivilegesData.length) {
            return false   // 无制定数据，则只有查看权限
           }
        }
        const hasPermission = curPrivilegesData.includes(permission)
        return hasPermission
      }
    } else {
      throw new Error(`参数必须是一个数组且长度大于 0，参考：v-permission="['workspace:create']"`)
    }
}
