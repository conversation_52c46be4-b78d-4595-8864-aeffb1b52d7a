<template>
  <div class="authority-container">
    <div class="authority-header">
      <h2>权限管理</h2>
    </div>
    <el-tabs v-model="activeTab" class="authority-tabs">
      <el-tab-pane label="资源权限管理" name="resource">
        <ResourceAuthority v-if="activeTab === 'resource'" />
      </el-tab-pane>
      <el-tab-pane label="数据权限管理" name="data" v-if="GetPermission(['member:set_role'])">
        <DataAuthority v-if="activeTab === 'data'" />
      </el-tab-pane>
      <el-tab-pane label="用户管理" name="user">
        <UserManage v-if="activeTab === 'user'" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import ResourceAuthority from './ResourceAuthority/index.vue';
import DataAuthority from './DataAuthority/index.vue';
import UserManage from './UserManage/index.vue';

const activeTab = ref('resource');
</script>

<style scoped lang="scss">
.authority-container {
  background-color: #fff;
  height: calc(100vh - 80px);
  .authority-header {
    padding:0 20px;
    border-bottom: 1px solid #e5e7eb;
    h2 {
        height: 48px;
        line-height: 48px;
        font-size: 1.125rem;
        margin: 0;
      }
  }
}
.authority-tabs {
  margin-top: 5px;
  :deep(.el-tabs__header) {
    margin-bottom: 0;
  }
  :deep(.el-tabs__nav-scroll) {
    padding:0 20px;
  }
}
</style>
