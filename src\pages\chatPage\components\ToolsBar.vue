<script setup lang="ts">
import { ref, computed, inject } from 'vue'
import { ElButton, ElSlider, ElSelect, ElOption, ElTooltip, ElUpload } from 'element-plus'
import { VideoPause, Refresh, Delete, Upload, Document } from '@element-plus/icons-vue'
import type { UploadRawFile } from 'element-plus'

// 注入聊天上下文数据
const history = inject('history', ref([]))
const canAbort = inject('canAbort', ref(false))
const replyLoading = inject('replyLoading', ref(false))
const appInfo = inject('appInfo', ref<any>({}))
const temperatureValue = inject('temperatureValue', ref(0.6))
const maxNewTokensValue = inject('maxNewTokensValue', ref(4000))
const resourceValue = inject('resourceValue', ref<any>(null))
const setResourceValue = inject('setResourceValue', () => { })
const handleChat = inject('handleChat', () => { }) as any
const currentDialogue = inject('currentDialogue', ref<any>({}))

// 本地状态
const fileList = ref<UploadRawFile[]>([])
const loading = ref(false)
const clsLoading = ref(false)
const modelOptions = ref([
  { label: 'GPT-4', value: 'gpt-4' },
  { label: 'GPT-3.5', value: 'gpt-3.5-turbo' },
  { label: 'Qwen2.5-72B', value: 'qwen2.5-72b-instruct' }
])
const selectedModel = ref('qwen2.5-72b-instruct')
const isProbe = ref(true)

defineExpose({
  isProbe,
  selectedModel
})

// 计算属性：参数配置
const paramKeys = computed(() => {
  return appInfo.value?.param_need?.map((i: any) => i.type) || []
})

// 右侧工具配置
const rightToolsConfig = computed(() => [
  {
    tip: '停止回复',
    icon: VideoPause,
    canUse: canAbort.value,
    key: 'abort',
    type: 'danger' as const,
    onClick: handleAbort
  },
  {
    tip: '重新回答',
    icon: Refresh,
    canUse: !replyLoading.value && (history.value as any[]).length > 0,
    key: 'redo',
    type: 'primary' as const,
    onClick: handleRedo
  },
  {
    tip: '清除历史',
    icon: Delete,
    canUse: (history.value as any[]).length > 0,
    key: 'clear',
    type: 'warning' as const,
    onClick: handleClear
  }
])

// 工具栏操作函数
function handleAbort() {
  if (!canAbort.value) return
  console.log('停止回复')
}

async function handleRedo() {
  const historyArray = history.value as any[]
  const lastHuman = historyArray.filter((i: any) => i.role === 'human')?.slice(-1)?.[0]
  if (!lastHuman) return

  const chatData = {
    app_code: appInfo.value?.app_code,
    ...(paramKeys.value.includes('temperature') && { temperature: temperatureValue.value }),
    ...(paramKeys.value.includes('max_new_tokens') && { max_new_tokens: maxNewTokensValue.value }),
    ...(paramKeys.value.includes('resource') && {
      select_param: typeof resourceValue.value === 'string'
        ? resourceValue.value
        : JSON.stringify(resourceValue.value) || currentDialogue.value?.select_param
    })
  }

  if (typeof handleChat === 'function') {
    await handleChat(lastHuman.context || '', chatData)
  }
}

async function handleClear() {
  if (clsLoading.value) return
  clsLoading.value = true
  try {
    console.log('清除历史记录')
    await new Promise(resolve => setTimeout(resolve, 1000))
  } finally {
    clsLoading.value = false
  }
}

// 文件上传处理
function handleFileChange(file: UploadRawFile) {
  console.log('文件上传:', file)
  return false
}

// 解析资源值
function parseResourceValue(value: any) {
  try {
    if (typeof value === 'string') {
      return JSON.parse(value)
    }
    return value || []
  } catch {
    return []
  }
}

// 获取文件名
const fileName = computed(() => {
  try {
    if (resourceValue.value) {
      if (typeof resourceValue.value === 'string') {
        return JSON.parse(resourceValue.value).file_name || ''
      }
      return resourceValue.value?.file_name || ''
    }
    return JSON.parse(currentDialogue.value?.select_param || '{}').file_name || ''
  } catch {
    return ''
  }
})

// 资源项展示
const resources = computed(() => {
  return parseResourceValue(resourceValue.value) || parseResourceValue(currentDialogue.value?.select_param) || []
})
</script>

<template>
  <div class="tools-bar">
    <!-- 主工具栏 -->
    <div class="flex items-center justify-between h-12 w-full">
      <!-- 左侧工具 -->
      <div class="flex items-center gap-4">
        <!-- 模型选择器 -->
        <!-- <div class="flex items-center gap-2">
          <span class="text-sm text-gray-600">模型:</span>
          <el-select v-model="selectedModel" placeholder="Select" style="width: 240px">
            <el-option v-for="item in modelOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>-->
        <!-- 是否开启追问 -->
        <div class="flex items-center mr5"> 
          <span class="text-sm text-gray-600" style="margin-right: 5px;">开启追问:</span>
          <el-switch v-model="isProbe" />
        </div>
        <!-- 资源上传 -->
        <!-- <div class="flex items-center gap-2">
          <el-upload :file-list="fileList" :before-upload="handleFileChange" :show-file-list="false">
            <el-tooltip content="上传文件" placement="top">
              <el-button :icon="Upload" size="small" circle />
            </el-tooltip>
          </el-upload>
        </div> -->

        <!-- 温度设置 -->
        <div v-if="paramKeys.includes('temperature')" class="flex items-center gap-2">
          <span class="text-sm text-gray-600">温度:</span>
          <el-slider v-model="temperatureValue" :min="0" :max="1" :step="0.1" :show-tooltip="true"
            style="width: 100px" />
        </div>

        <!-- 最大Token数 -->
        <div v-if="paramKeys.includes('max_new_tokens')" class="flex items-center gap-2">
          <span class="text-sm text-gray-600">Token:</span>
          <el-slider v-model="maxNewTokensValue" :min="1000" :max="8000" :step="500" :show-tooltip="true"
            style="width: 100px" />
        </div>
      </div>

      <!-- 右侧工具 -->
      <!-- <div class="flex items-center gap-2">
        <el-tooltip v-for="tool in rightToolsConfig" :key="tool.key" :content="tool.tip" placement="top">
          <el-button :icon="tool.icon" :type="tool.type" :disabled="!tool.canUse"
            :loading="tool.key === 'clear' && clsLoading" size="small" circle @click="tool.onClick" />
        </el-tooltip>
      </div> -->
    </div>

    <!-- 资源展示区域 -->
    <div v-if="resources.length > 0" class="resources-display mt-3">
      <div class="flex flex-wrap gap-2">
        <div v-for="(item, index) in resources" :key="index" class="resource-item">
          <!-- 图片资源 -->
          <div v-if="item.type === 'image_url' && item.image_url?.url" class="image-resource">
            <div class="image-preview">
              <img :src="item.image_url.url" :alt="item.image_url.fileName || 'Preview'" class="preview-img" />
            </div>
            <div class="resource-name">
              <span>{{ item.image_url.fileName }}</span>
            </div>
          </div>

          <!-- 文件资源 -->
          <div v-else-if="item.type === 'file_url' && item.file_url?.url" class="file-resource">
            <el-icon class="file-icon">
              <Document />
            </el-icon>
            <span class="file-name">{{ item.file_url.file_name }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载指示器 -->
    <div v-if="loading" class="loading-indicator">
      <!-- <el-loading-directive /> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
.tools-bar {
  border-bottom: 1px solid #eee;
  margin-bottom: 5px;
  // background: #fafafa;

  .resources-display {
    .resource-item {
      border: 1px solid #e3e4e6;
      border-radius: 8px;
      padding: 8px;
      background: white;

      &.image-resource {
        display: flex;
        flex-direction: column;

        .image-preview {
          width: 120px;
          height: 120px;
          margin-bottom: 8px;
          overflow: hidden;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f5f5;
          border-radius: 4px;

          .preview-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }

        .resource-name {
          font-size: 12px;
          color: #606266;
          text-align: center;
          word-break: break-all;
        }
      }

      &.file-resource {
        display: flex;
        align-items: center;
        gap: 8px;

        .file-icon {
          font-size: 20px;
          color: #67c23a;
        }

        .file-name {
          font-size: 14px;
          color: #303133;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
        }
      }
    }
  }

  .loading-indicator {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}

.dark {
  .tools-bar {
    background: rgba(0, 0, 0, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.1);

    .resource-item {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
    }
  }
}
</style>