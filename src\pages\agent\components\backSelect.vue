<template>
  <div class="back-select-container">
    <div class="back-icon-wrapper" v-if="showBack" @click="handleBack">
      <i class="ChatData-fanhui iconfont"></i>
    </div>
    <el-select v-model="value" v-bind="$attrs" class="select-box">
      <el-option
        v-for="item in data"
        :key="item[props.value]"
        :label="item[props.label]"
        :value="String(item[props.value])"
      ></el-option>
    </el-select>
  </div>
</template>

<script lang="ts" setup>
import { Back } from "@element-plus/icons-vue";
// 带回退按钮的 选择框
const props = defineProps({
  data: {
    type: Array as PropType<any[]>,
    required: true,
  },
  label: {
    type: String,
    default: "label",
  },
  value: {
    type: String,
    default: "value",
  },
  showBack: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits(["back"]);
const value = defineModel<string>();

function handleBack() {
  emit("back", value.value);
}
</script>
<style lang="scss" scoped>
.back-icon-wrapper {
  padding: 5px;
  border-radius: 8px;
  /* font-weight: bold; */
  background: #ebf3fd;
  display: flex;
  align-items: center;

  cursor: pointer;
  .iconfont {
    color: #005ee0;
  }
}
.back-select-container {
  display: flex;
  align-items: center;
  gap: 10px;
}
.select-box {
  width: 180px;
  :deep(.el-select__wrapper) {
    box-shadow: none;
    .el-select__suffix {
      padding: 1px;
      border-radius: 4px;
      /* font-weight: bold; */
      background: #ebf3fd;
      .el-icon {
        filter: drop-shadow(0 0 0.5px #005ee0);
      }
      svg {
        font-size: 12px;
        path {
          fill: #005ee0;
        }
      }
    }
    .el-select__selection {
      .el-select__selected-item.el-select__placeholder {
        font-weight: bold;
        font-size: 16px;
        color: #262626;
        line-height: 22px;
      }
    }
  }
}
</style>
