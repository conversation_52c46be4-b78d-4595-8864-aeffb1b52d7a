<template>
  <el-collapse v-model="activeName" class="chat-data-node-collapse">
    <!-- 意图分析 -->
    <el-collapse-item title="意图分析" name="intentAnalysis">
      <template #title>
        <el-icon
          :size="16"
          class="el-icon--right"
          v-if="!!Object.keys(intentAnalysisData).length"
        >
          <SuccessFilled />
        </el-icon>
        <el-icon class="loading-icon" v-else><Loading /></el-icon>
        <span class="collapse-title">意图分析</span>
      </template>
      <!-- <p>按照类别、子类别分组、计算销售额求和、数量求和</p> -->
      <p>
        <span>查询模式：</span>
        <span>{{ intentAnalysisData.query_mode }}</span
        ><br />
        <span>指标:</span>
        <span v-if="intentAnalysisData.metrics">{{
          intentAnalysisData?.metrics[0]
        }}</span>
      </p>
      <p>
        <span>筛选条件(数据时间)：</span>
        <span v-if="intentAnalysisData.time_range">{{
          intentAnalysisData?.time_range.join(" - ")
        }}</span>
      </p>
    </el-collapse-item>
    <!-- SQL生成 -->
    <el-collapse-item name="sqlRender">
      <template #title>
        <el-icon
          :size="16"
          class="el-icon--right"
          v-if="!lastExecutionSqlStatus"
        >
          <SuccessFilled />
        </el-icon>
        <el-icon class="loading-icon" v-else><Loading /></el-icon>
        <span class="collapse-title">SQL生成</span>
      </template>
      <div v-if="props.data.context">
        <!-- sql 编辑 -->
        <div>
          <!-- {{isNewAnswer}} -->
          <p v-if="sqlToJsonStatus" class="loading-text">
            加载中<span class="dots">...</span>
          </p>
          <sqlEdit
            v-if="!sqlToJsonStatus && !!sqlToJsonData"
            :data="props.data"
            :showEdit="isNewAnswer"
            :index="props.index"
          />
        </div>

        <el-radio-group v-model="renderSqlRadio" class="render-sql-radio">
          <el-radio value="schema" size="large">Schema映射</el-radio>
          <el-radio value="few_shot" size="large">Few-shot示例</el-radio>
          <el-radio
            value="renderSQL"
            :disabled="renderSQLStatus"
            size="large"
            :class="{ 'is-loading': renderSQLStatus }"
          >
            <el-icon class="loading-icon" v-if="renderSQLStatus"
              ><Loading
            /></el-icon>
            <span>生成SQL</span>
          </el-radio>
          <el-radio
            value="lastExecutionSql"
            :class="{ 'is-loading': lastExecutionSqlStatus }"
            :disabled="lastExecutionSqlStatus"
            size="large"
          >
            <el-icon class="loading-icon" v-if="lastExecutionSqlStatus"
              ><Loading
            /></el-icon>
            SQL修正
          </el-radio>
        </el-radio-group>
        <!-- few_shot 内容 -->
        <template v-if="renderSqlRadio === 'few_shot'">
          <MdHighlight :content="fewShotData" />
        </template>
        <!-- schema 内容 -->
        <template v-else-if="renderSqlRadio === 'schema'">
          <SqlHighlight :code="intentAnalysisData?.metrics[0]?.split(',').slice(1).join(',')" />
        </template>
        <!-- 其他内容 -->
        <template v-else>
          <SqlHighlight
            v-if="typeof data?.context === 'object'"
            :code="data?.context?.sqlData?.[renderSqlRadio].data"
          />
        </template>
      </div>
    </el-collapse-item>
    <!-- 数据查询 -->
    <el-collapse-item name="dataQuery">
      <template #title>
        <el-icon :size="16" class="el-icon--right" v-if="!flowChartStatus">
          <SuccessFilled />
        </el-icon>
        <el-icon class="loading-icon" v-else><Loading /></el-icon>
        <span class="collapse-title">数据查询</span>
      </template>
      <div v-loading="flowChartStatus" style="min-height: 100px">
        <ChartDiagram
          v-if="flowChartData && !flowChartStatus"
          :config="flowChartData"
        ></ChartDiagram>
      </div>
      <!-- 数据查询分析原因切换查看 -->
      <div
        class="sql-execute-analysis-content"
        v-if="!!dataExplainData || !!attributionAnalysisData"
      >
        <el-radio-group
          v-model="sqlExecuteAnalysisRadio"
          text-color="#005EE0"
          fill="#EBF3FD"
        >
          <el-radio-button label="数据解读" value="dataInterpretation" />
          <el-radio-button label="波动归因" value="fluctuationCausation" />
        </el-radio-group>
        <!-- 数据解读 -->
        <div
          class="data-interpretation"
          v-if="sqlExecuteAnalysisRadio === 'dataInterpretation'"
          v-loading="dataExplainStatus"
        >
          <MdHighlight :content="dataExplainData" />
        </div>
        <!-- 波动归因 -->
        <div
          class="fluctuation-causation"
          v-if="
            sqlExecuteAnalysisRadio === 'fluctuationCausation'
          "
          v-loading="attributionAnalysisStatus"
        >
          <ChatWaveCause :flowChartData="flowChartData" :data="attributionAnalysisData" :label="attributionSplitAnalysisLabel" />
        </div>
      </div>
    </el-collapse-item>
    <!-- 相关相似问题推荐 -->
    <el-collapse-item name="relatedSimilarQuestions" v-show="!flowChartStatus">
      <template #title>
        <el-icon :size="16" class="el-icon--right">
          <SuccessFilled />
        </el-icon>
        <span class="collapse-title">推荐相似问题</span>
      </template>
      <div class="related-similar-questions">
        <div
          class="related-questions"
          v-if="!!reqRec?.relatedQuestions?.length"
        >
          <p class="title">相关指标推荐</p>
          <div class="question-list">
            <span
              v-for="item in reqRec.relatedQuestions"
              :key="item"
              @click="handleQuestionClick(item)"
            >
              {{ item }}
            </span>
          </div>
        </div>
        <div
          class="similar-questions"
          v-if="!!reqRec?.similarQuestions?.length"
        >
          <p class="title">相似指标推荐</p>
          <div class="question-list">
            <span
              v-for="item in reqRec.similarQuestions"
              :key="item"
              @click="handleQuestionClick(item)"
            >
              {{ item }}
            </span>
          </div>
        </div>
      </div>
    </el-collapse-item>
  </el-collapse>
</template>
<script setup lang="ts">
import { ref, defineProps, watchEffect, computed, watch, inject } from "vue";
import ChartDiagram from "./chartDiagram.vue";
import { SuccessFilled, Loading } from "@element-plus/icons-vue";
import { get } from "lodash-es";
import SqlHighlight from "./SqlHighlight.vue";
import MdHighlight from "./mdHightlight.vue";
import sqlEdit from "./sqlEdit.vue";
import { ChatHistoryResponse } from "@@/apis/chat/type";
import SqlEdit from "./sqlEdit.vue";
import ChatWaveCause from "./chatWaveCause.vue";

interface ChatDataNodeTyps {
  context: Record<string, any> | string;
  thinking?: boolean;
}

const history = inject<any>("history", []);
const renderSqlRadio = ref<string>("schema"); // 生成sql 渲染调整
const activeName = ref<string[]>([
  "sqlRender",
  "dataQuery",
  "intentAnalysis",
  "relatedSimilarQuestions",
]); // 折叠面板打开的item
const sqlExecuteAnalysisRadio = ref<string>("dataInterpretation"); // 数据查询分析原因切换查看
const intentAnalysisData = ref<Record<string, any>>({}); // 意图分析数据


// 生成sql 状态
const renderSQLStatus = computed(() => {
  return get(props.data, "context.sqlData.renderSQL.status") === "pending";
});
// sql修正状态
const lastExecutionSqlStatus = computed(
  () => get(props.data, "context.sqlData.lastExecutionSql.status") === "pending"
);
// sql过滤 编辑状态
const sqlToJsonStatus = computed(
  () => get(props.data, "context.sqlData.sql_edit.status") === "pending"
);
// 获取sql过滤
const sqlToJsonData = computed(() =>
  get(props.data, "context.sqlData.sql_edit.data", null)
);
// 数据查询状态
const flowChartStatus = computed(
  () => get(props.data, "context.sqlData.sqlFlowChart.status") === "pending"
);
// 获取数据流程图
const flowChartData = computed(() =>{
  return get(props.data, "context.sqlData.sqlFlowChart.data", null)
});

// 获取波动归因数据
const attributionAnalysisData = computed(() =>{
  return get(props.data, "context.sqlData.attribution_analysis.data", null)
});
// 获取波动归因状态
const attributionAnalysisStatus = computed(
  () =>
    get(props.data, "context.sqlData.attribution_analysis.status") === "pending"
);

// 拼接波动归因label
const attributionSplitAnalysisLabel = computed(() =>{
  // let label =
  return get(intentAnalysisData.value, "metrics[0]", "") + '波动归因'
});

// 获取数据解读数据
const dataExplainData = computed(() =>
  get(props.data, "context.sqlData.data_explain.data", "")
);
// 获取数据解读状态
const dataExplainStatus = computed(
  () => get(props.data, "context.sqlData.data_explain.status") === "pending"
);

// 获取sql编辑数据
const sqlEditData = computed(() =>
  get(props.data, "context.sqlData.sql_edit.data", null)
);

// 推荐相似问题
const reqRec = computed(() => {
  const recStr = get(props.data, "context.req_rec", "");

  // 使用 [\s\S]*? 来匹配包含换行的内容
  const similarQuestions: string[] =
    recStr
      .match(/<similar>([\s\S]*?)<\/similar>/)?.[1]
      .match(/<ques>([\s\S]*?)<\/ques>/g)
      ?.map((q) => q.replace(/<\/?ques>/g, "").trim()) || [];

  const relatedQuestions: string[] =
    recStr
      .match(/<relate>([\s\S]*?)<\/relate>/)?.[1]
      .match(/<ques>([\s\S]*?)<\/ques>/g)
      ?.map((q) => q.replace(/<\/?ques>/g, "").trim()) || [];

  // console.log("原始字符串：", recStr);
  // console.log("相似问题：", similarQuestions);
  // console.log("相关问题：", relatedQuestions);

  return {
    similarQuestions,
    relatedQuestions,
  };
});
// ====================================================================

// 处理few_shot数据
const fewShotData = computed(() => {
  let source: object[] = toRaw(get(props.data, "context.sqlData.few_shot", []));
  return source
    .map((item) => {
      const shot = (item as Record<string, string>).shot;
      const cleanShot = shot.replace(/^"|"$/g, "");
      return cleanShot.replace(/\\n/g, "\n");
    })
    .join("\n");
});

// 是否新回答且已生成完sql
const isNewAnswer = computed(() => {
  // console.log(history, props.index, props.data, "is new answer");
  // sql 执行已生成完 且是最新一条已回答 时，可编辑sql
  return (
    !lastExecutionSqlStatus.value &&
    history.value.length === props.index + 1 &&
    get(props.data, "is_new_answer", false)
  );
});

const props = defineProps({
  data: {
    type: Object as PropType<ChatDataNodeTyps>,
    default: () => ({}),
  },
  index: {
    type: Number,
    default: 0,
  },
});

const handleQuestionClick = (question: string) => {
  (window as any).setInputValue(question);
  // console.log("点击问题：", question);
};

watchEffect(() => {
  intentAnalysisData.value = get(props.data, "context.intentAnalysisData", {});
  // sqlData.value = get(props.data, "context.sqlData", {});
  //   renderSQLStatus.value =
  //     get(props.data, "context.sqlData.renderSQL.status") === "pending";
  console.log("当前状态：", {
    props: get(props.data, "context.sqlData.renderSQL.status"),
    sqlData: get(props.data, "context.sqlData", {}),
    intentAnalysisData: intentAnalysisData.value
  });
});
</script>
<style scoped lang="scss">
@keyframes loading-remote {
  100% {
    transform: rotate(360deg);
  }
}
.chat-data-node-collapse {
  min-width: 700px;
  // max-width: 300px;
  border: none;
  :deep(.el-collapse-item__header) {
    background: transparent;
    height: 30px;
    padding: 5px 0px;
    border-bottom: none;
    border: none;
    margin-bottom: 5px;
    gap: 10px;
    .el-icon--right {
      color: #005ee0;
    }
    .el-collapse-item__arrow {
      color: #4C6F88;
      margin: 0px;
      font-weight: bold;
      margin-left: 20px;
    }
  }
  :deep(.el-collapse-item__wrap) {
    border-left: 1px solid #ededed;
    padding-left: 16px;
    margin-left: 14px;
    border-bottom: none;
    text-align: left;
    background: transparent;
  }
  .collapse-title {
    font-weight: bold;
    font-size: 14px;
    color: #262626;
  }
}
:deep(.render-sql-radio) {
  display: flex;
  align-items: center;
  //   gap: 15px;

  .el-radio {
    background: #ededed;
    color: #262626;
    height: 24px;
    margin: 0;
    padding: 0 20px; // 增加内边距，为箭头留空间
    position: relative;
    display: flex;
    align-items: center;
    clip-path: polygon(95% 0, 100% 50%, 95% 100%, 0% 100%, 5% 50%, 0% 0%);
    // 使用 clip-path 创建箭头形状
    // 添加过渡动画
    transition: all 0.3s ease;
    // 基础样式
    .el-radio__label {
      font-size: 12px;
      padding: 0;
      z-index: 1; // 确保文字在最上层
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .el-radio__input {
      display: none;
    }

    // 第一个元素样式
    &:first-child {
      clip-path: polygon(95% 0, 100% 50%, 95% 100%, 0% 100%, 0% 0%);
      padding-left: 15px;
    }

    // 最后一个元素样式
    &:last-child {
      clip-path: polygon(100% 0, 100% 100%, 0% 100%, 5% 50%, 0% 0%);
      padding-right: 15px;
    }

    // hover 状态
    &:hover {
      background: #e0e0e0;
    }

    // 选中状态
    &.is-checked {
      background: #005ee0;
      color: #ffffff;

      .el-radio__label {
        color: #ffffff;
      }
    }
    // loading 状态
    &.is-loading {
      background: #f7f7f7;
      color: #8c8c8c;
    }
  }
}
.pre-dom {
  white-space: break-spaces;
  // max-width: 700px;
  background: #1e1e1e;
  border-radius: 4px;
  border: 1px solid #f2f2f2;
  padding: 20px;
  line-height: 24px;
  max-height: 500px;
  overflow-y: auto;
  color: #f5f8fb;
}
.few-shot-data {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 15px;

  .few-shot-data-item {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    .label,
    .title {
      font-weight: bold;
    }
    .title {
      font-size: 14px;
    }
  }
}
:deep(.related-similar-questions) {
  padding-left: 0px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  .title {
    font-weight: 600;
    font-size: 13px;
    color: #262626;
    line-height: 16px;
    &::before {
      content: "";
      display: inline-block;
      border-radius: 50%;
      width: 8px;
      height: 8px;
      background: #4c6f88;
      margin-right: 10px;
    }
  }
  .question-list {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 0px 20px;
    span {
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #ededed;
      padding: 6px 12px;
      cursor: pointer;
      &:hover {
        background: #f0f7ff;
        border-color: #98bef5;
      }
    }
  }
}
.sql-execute-analysis-content {
  margin-top: 15px;
  :deep(.el-radio-group) {
    .el-radio-button__inner {
      font-size: 13px;
      font-weight: 600;
    }
  }
  .data-interpretation,
  .fluctuation-causation {
    border-radius: 4px;
    border: 1px solid #f2f2f2;
    padding: 12px 14px;
    margin-top: 10px;
  }
}
.loading-text {
  display: flex;
  .dots {
    display: inline-block;
    overflow: hidden;
    animation: dotFade 1.4s infinite;
    width: 18px;
  }
}

@keyframes dotFade {
  0% {
    width: 0;
  }
  50% {
    width: 18px;
  }
  100% {
    width: 0;
  }
}
.loading-icon {
  animation: loading-remote 2s linear infinite;
}
</style>

