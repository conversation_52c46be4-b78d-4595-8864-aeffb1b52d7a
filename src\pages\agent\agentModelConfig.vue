<template>
  <div class="agent-model-config">
    <div class="title-symbol">模型配置</div>
    <el-form
      :model="modelConfig"
      label-position="top"
      class="model-config-form"
    >
      <div class="switch-wrapper">
        <span
          :class="[
            'switch-item',
            { 'active-switch': curSceneType === item.value },
          ]"
          v-for="item in switchOptions"
          :key="item.label"
          @click="handleSwitchClick(item.value)"
        >
          {{ item.label }}
          <el-tooltip :content="item.tip" placement="top" v-if="!!item.tip">
            <i
              class="iconfont ChatData-zhushi tip-icon"
              style="margin-top: 2px"
            ></i>
          </el-tooltip>
          <el-switch v-model="modelConfig[item.value].enable" />
        </span>
      </div>
      <el-form-item label="应用模型" v-show="curSceneType">
        <template #label>
          <div class="flex-center">
            <span>应用模型</span>
            <el-tooltip
              content="通过大模型对结果数据做提炼总结"
              placement="top"
            >
              <i class="iconfont ChatData-zhushi tip-icon"></i>
            </el-tooltip>
          </div>
        </template>
        <el-select
          v-model="modelConfig[curSceneType].applicationModel"
          placeholder="请选择应用模型"
        >
          <el-option
            v-for="item in appList"
            :key="item.model_name"
            :label="item.model_name"
            :value="item.model_name"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="提示词模板"
        class="template-wrapper"
        v-show="curSceneType"
      >
        <template #label>
          <div class="flex-center">
            <span>提示词模板</span>
            <el-tooltip
              :content="tooltipContent"
              placement="top"
              width="200px"
              class="tooltip-content"
            >
              <i class="iconfont ChatData-zhushi tip-icon"></i>
            </el-tooltip>
          </div>
        </template>
        <el-input
          v-model="modelConfig[curSceneType].promptTemplate"
          type="textarea"
          :rows="4"
          placeholder="请输入提示词模板"
          class="template-input"
        />
      </el-form-item>
    </el-form>
  </div>
</template>
  <script setup lang="ts">
import { ref, defineExpose, defineProps, watchEffect } from "vue";
import { getModelListApi } from "@/common/apis/agent";
import type { ModelList } from "./type";
import { ElMessage } from "element-plus";
// 模型类型
interface ModelItem {
  enable: boolean;
  applicationModel: string;
  promptTemplate: string;
}
interface ModelConfig {
  [key: string]: ModelItem;
}
// 初始化模型配置
const initModelConfig = ref<ModelConfig>({
  sqlAnalysis: {
    enable: false,
    applicationModel: "",
    promptTemplate: "",
  },
  sqlRepair: {
    enable: false,
    applicationModel: "",
    promptTemplate: "",
  },
  resultDataParsing: {
    enable: false,
    applicationModel: "",
    promptTemplate: "",
  },
});
const modelConfig = ref<ModelConfig>({ ...initModelConfig.value });

// 开关选项
interface SwitchOptionType {
  label: string;
  value: string;
  tip?: string;
}
// 开关配置
const switchOptions = ref<SwitchOptionType[]>([
  {
    label: "语义SQL解析",
    value: "sqlAnalysis",
    tip: "通过大模型做语义解析生成S2SQL",
  },
  {
    label: "语义SQL修订",
    value: "sqlRepair",
    tip: "通过大模型对解析S2SQL做二次修正",
  },
  {
    label: "结果数据解读",
    value: "resultDataParsing",
    tip: "通过大模型对结果数据做提炼总结",
  },
]);
// 当前选选中的场景
const curSceneType = ref<string>("sqlAnalysis");
// 提示词模板内容
const tooltipContent = ref(
  "自定义提示词模板可嵌入以下变量，将由系统自动进行替换：\n-{{exemplar}} :替换成few-shot示例，示例个数由系统配置\n-{{question}} :替换成用户问题，拼接了一定的补充信息\n-{{schema}} :替换成数据语义信息，根据用户问题映射而来"
);

// 应用list
const appList = ref<ModelList[]>([]);

// 获取模型列表
const getModelList = () => {
  appList.value = [];
  getModelListApi().then((res: any) => {
    // llm类型的 为应用模型
    appList.value = res.data.filter(
      (item: ModelList) => item.worker_type === "llm"
    );
  });
};
getModelList();

// 获取模型配置数据
const getData = (): ModelConfig => {
  return {
    ...modelConfig.value,
  };
};

// 模型校验
const validateForm = async (): Promise<boolean> => {
  let valid = true;
  // 只要有开启场景，则需要校验应用模型和提示词模板
  for (const key in modelConfig.value) {
    if (modelConfig.value[key].enable) {
      if (!modelConfig.value[key].applicationModel || !modelConfig.value[key].promptTemplate) {
        valid = false;
        ElMessage.error("请选择应用模型和提示词模板");
        break;
      }
    }
  }
  return valid;
};

// 重置表单数据
const resetForm = (params: any = {}): void => {
  // 重置表单值，根据传入的数据获取或初始默认值
  for (let key in initModelConfig.value) {
    modelConfig.value[key] = {
      ...initModelConfig.value[key],
      ...params[key]
    };
  }
};

function handleSwitchClick(value: string) {
  curSceneType.value = value;
}
defineExpose({
  modelConfig,
  initModelConfig,
  getData,
  resetForm,
  validateForm
});
</script>
  <style lang="scss" scoped>
.agent-model-config {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
}
.tip-icon {
  font-size: 13px;
  color: #005ee0;

  cursor: pointer;
}
.title-symbol {
  border-left: 3px solid #005ee0;
  font-weight: bold;
  font-size: 14px;
  color: #262626;
  padding: 0px 10px;
}

.model-config-form {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
  // 让表单项占满剩余空间
  :deep(.el-form-item) {
    margin-bottom: 0px;

    // 提示词模板表单项占满剩余空间
    &.template-wrapper {
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;

      // 让表单项内容区域占满剩余空间
      .el-form-item__content {
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;

        // textarea 输入框占满剩余空间
        .template-input {
          flex: 1 1 auto;
          display: flex;
          flex-direction: column;

          .el-textarea__inner {
            height: 100% !important;
            resize: none;
          }
        }
      }
    }
  }
}
.switch-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  .switch-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
    padding: 0px 10px;
    color: #595959;
    background: #fafafa;
    border-radius: 4px;
    cursor: pointer;
    :deep(.el-switch__core) {
      min-width: 30px;
    }
  }
  .active-switch {
    background: #ebf3fd;
    color: #005ee0;
  }
}
.flex-center {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-start !important;
}
</style>
