<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { isExternal } from "@@/utils/validate"
import path from "path-browserify"
import Link from "./Link.vue"

const route = useRoute()
interface Props {
  item: any
  basePath?: string
}

const props = withDefaults(defineProps<Props>(), {
  basePath: ""
})

/** 是否是根菜单 */
const isRootMenu = computed(() => !props.item.name)

/** 是否始终显示根菜单 */
const alwaysShowRootMenu = computed(() => props.item.meta?.alwaysShow)

/** 显示的子菜单 */
const showingChildren = computed(() => props.item.children?.filter(child => !child.meta?.hidden) ?? [])

/** 显示的子菜单数量 */
const showingChildNumber = computed(() => showingChildren.value.length)

/** 唯一的子菜单项 */
const theOnlyOneChild = computed(() => {
  const number = showingChildNumber.value
  switch (true) {
    case number > 1:
      return null
    case number === 1:
      return showingChildren.value[0]
    default:
      return { ...props.item, path: "" }
  }
})

/** 解析路径 */
function resolvePath(routePath: string) {
  switch (true) {
    case isExternal(routePath):
      return routePath
    case isExternal(props.basePath):
      return props.basePath
    default:
      return path.resolve(props.basePath, routePath)
  }
}
</script>

<template>
  <template v-if="!alwaysShowRootMenu && theOnlyOneChild && !theOnlyOneChild.children">
    <Link v-if="theOnlyOneChild.meta" :to="resolvePath(theOnlyOneChild.path)">
      <el-menu-item :index="resolvePath(theOnlyOneChild.path)">
        <!-- <SvgIcon v-if="theOnlyOneChild.meta.svgIcon" :name="theOnlyOneChild.meta.svgIcon" class="svg-icon" /> -->
        <!-- <component v-else-if="theOnlyOneChild.meta.elIcon" :is="theOnlyOneChild.meta.elIcon" class="el-icon" /> -->
        <template v-if="theOnlyOneChild.meta.title" #title>
          <span class="title">{{ theOnlyOneChild.meta.title }}</span>
        </template>
      </el-menu-item>
    </Link>
  </template>
  <template v-else-if="isRootMenu && props.item.children">
    <template v-for="child in props.item.children" :key="child.path">
      <Link :to="resolvePath(child.path) + '?spaceCode=' + route.query.spaceCode || ''" class="sidebar-menu-item">
        <el-menu-item :index="resolvePath(child.path)">
          <template v-if="child.meta?.title" #title>
            <i class="iconfont" :class="child.meta?.icon"></i>
            <span class="title">{{ child.meta?.title }}</span>
          </template>
        </el-menu-item>
      </Link>
    </template>
  </template>
  <el-sub-menu v-else :index="resolvePath(props.item.path)" teleported>
    <template #title>
      <!-- <SvgIcon v-if="props.item.meta?.svgIcon" :name="props.item.meta.svgIcon" class="svg-icon" /> -->
      <!-- <component v-else-if="props.item.meta?.elIcon" :is="props.item.meta.elIcon" class="el-icon" /> -->
      <span v-if="props.item.meta?.title" class="title">{{ props.item.meta.title }}</span>
    </template>
    <template v-if="props.item.children">
      <Item
        v-for="child in showingChildren"
        :key="child.path"
        :item="child"
        :base-path="resolvePath(child.path)"
      />
    </template>
  </el-sub-menu>
</template>

<style lang="scss" scoped>
@import "@@/assets/styles/mixins.scss";

.svg-icon {
  min-width: 1em;
  margin-right: 12px;
  font-size: 18px;
}

.el-icon {
  width: 1em !important;
  margin-right: 12px !important;
  font-size: 18px;
}

.title {
  @extend %ellipsis;
  font-weight: 500;
  transition: all 0.3s;
}
.sidebar-menu-item {
  display: flex;
  justify-content: center;
  align-items: center;
}
:deep(.el-menu-item) {
  position: relative;
  font-weight: 500;
  transition: all 0.3s;
  color: #fff;
  height: 30px !important;
  line-height: 30px !important;
  border-radius: 5px;
  margin: 0 5px !important;
  .iconfont {
    margin-right: 5px;
  }

  &:hover {
    background: #fff !important;
    color: #005EE0 !important;
    .iconfont {
      color: #005EE0 !important;
    }
  }

  &.is-active {
    font-weight: 600;
    background: #fff !important;
    color: #005EE0 !important;
    .iconfont {
      color: #005EE0 !important;
    }
  }
}
</style>
