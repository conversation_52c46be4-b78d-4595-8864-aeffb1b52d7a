<script lang="ts" setup>
import { useRouter } from 'vue-router'
const router = useRouter()
const goToWorkspace = () => {
  router.push({ name: 'workspaceManage' })
}
</script>
<template>
  <div class="empty-container">
    <div class="empty-img"></div>
    <h3>对不起，无访问的工作空间</h3>
    <div class="empty-text">请联系管理员，在 <el-button type="text" @click="goToWorkspace">系统管理 / 工作空间</el-button>中添加权限</div>
    <el-button type="primary" @click="goToWorkspace">+ 新增工作空间</el-button>
  </div>
</template>

<style lang="scss" scoped>
.empty-container {
  text-align: center;
  margin-top: 300px;
  .empty-text {
    margin-bottom: 15px;
  }
}
</style>
