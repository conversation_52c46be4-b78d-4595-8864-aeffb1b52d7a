import type * as LLM from "./type"
import { request } from "@/http/axios"

/** 获取模型类型列表 */
export function getLLMTypeListApi() {
  return request({
    url: '/serve/model/model-types',
    method: "get",
    version:'/api/v2'
  })
}


/** 获取模型列表 */
export function getLLMListApi() {
  return request({
    url: '/serve/model/models',
    method: "get",
    version:'/api/v2'
  })
}

/** 获取模型详情 */
export function getLLMDetailApi(data: LLM.GetLLMDetailRequestData) {
  return request({
    url: `/serve/model/models/detail`,
    method: "get",
    version:'/api/v2',
    params:data
  })
}

/** 创建模型 **/
export function createLLMtApi(data: LLM.CreateModelRequestData) {
  return request({
    url: '/serve/model/models',
    method: "post",
    version:'/api/v2',
    data
  })
}

/** 更新模型 **/
export function updateLLMtApi(data: LLM.CreateModelRequestData) {
  return request({
    url: '/serve/model/models',
    method: "put",
    version:'/api/v2',
    data
  })
}
// 删除模型
export function deleteLLMtApi(data: LLM.DeleteModelRequestData) {
  return request({
    url: `/serve/model/models/stop`,
    method: "post",
    version: "/api/v2",
    data
  });
}

/** 获取数据源列表 */
export function getDatasourcesListApi() {
  return request({
    url: '/serve/datasources',
    method: "get",
    params: {},
    version:'/api/v2'
  })
}
// 获取数据源类型列表
export async function getDatasourcesTypeApi() {
  return request({
    url: '/serve/datasource-types',
    method: "get",
    version:'/api/v2'
  })
    // 模拟API请求延迟
    // await new Promise(resolve => setTimeout(resolve, 300))
    // return databaseTypesConfig
}
// 新增数据源
export function postDatasourceApi(data: any) {
  return request({
    url: '/serve/datasources',
    method: "post",
    data,
    version:'/api/v2'
  })
}
// 编辑数据源
export function putDatasourceApi(id: number, updateData: any) {
  return request({
    url: `/serve/datasources/${id}`,
    method: "put",
    data: updateData,
    version:'/api/v2'
  })
}

/** 查看单个数据源 */
export function getDatasourceByIdApi(id: number) {
  return request({
    url: `/serve/datasources/${id}`,
    method: "get",
    version:'/api/v2'
  })
}
// 删除数据源
export function deleteDatasourceApi(id: number) {
  return request({
    url: `/serve/datasources/${id}`,
    method: "delete",
    version:'/api/v2'
  })
}

// 数据源表列表
export function getDatasourceTableListApi(id: number) {
  return request({
    url: `/serve/datasources/${id}/tables`,
    method: "get",
    version:'/api/v2'
  })
}
// 数据源表字段列表
export function getDatasourceTableFieldListApi(tableId: number) {
  return request({
    url: `/serve/tables/${tableId}/fields`,
    method: "get",
    version:'/api/v2'
  })
}
// 修改数据源表
export function putDatasourceTableApi(tableId: number, data: any) {
  return request({
    url: `/serve/tables/${tableId}`,
    method: "put",
    version:'/api/v2',
    data
  })
}
//修改数据源表字段
export function putDatasourceTableFieldApi(tableId: number, data: any) {
  return request({
    url: `/serve/tables/${tableId}/fields`,
    method: "put",
    version:'/api/v2',
    data:{fields:data}
  })
}
// 字段下的枚举值
export function getDatasourceTableFieldEnumApi( fieldId: number) {
  return request({
    url: `/serve/fields/${fieldId}/enums`,
    method: "get",
    version:'/api/v2'
  })
}
// 测试数据源连接
export function testDatasourceConnectionApi(data: any) {
  return request({
    url: `/serve/datasources/test-connection`,
    method: "post",
    version:'/api/v2',
    data
  })
}

// 权限管理
// 查询助手用户及权限
export function getAngenMemberApi(agent_id: any) {
  return request({
    url: `/agent/${agent_id}/members_with_permissions`,
    method: "get",
    version:'/api'
  })
}
// 助手授权
export function getAngenPermissionsApi(agent_id: any,data: any) {
  return request({
    url: `/agent/${agent_id}/permissions/batch`,
    method: "post",
    version:'/api',
    data
  })
}
// 助手取消授权
export function deleteAngenPermissionsApi(agent_id: any,data: any) {
  return request({
    url: `/agent/${agent_id}/permissions/batch`,
    method: "delete",
    version:'/api',
    data
  })
}
// 查询数据模型用户及权限
export function getDatamodelMemberApi(data_model_id: any) {
  return request({
    url: `/datamodel/${data_model_id}/members_with_permissions`,
    method: "get",
    version:'/api'
  })
}
// 数据模型授权
export function getDatamodelPermissionsApi(data_model_id: any,data: any) {
  return request({
    url: `/datamodel/${data_model_id}/permissions/batch`,
    method: "post",
    version:'/api',
    data
  })
}

// 数据模型取消授权
export function deleteDatamodelPermissionsApi(data_model_id: any,data: any) {
  return request({
    url: `/datamodel/${data_model_id}/permissions/batch`,
    method: "delete",
    version:'/api',
    data
  })
}

// 查询权限列表
export function getPermissionListApi(data: any) {
  return request({
    url: `/data-permission/permission_list`,
    method: "get",
    version:'/api',
    params:data
  })
}
// 查询单个权限详情
export function getPermissionDetailApi(data: any) {
  return request({
    url: `/data-permission/get_permission_detail`,
    method: "get",
    version:'/api',
    params:data
  })
}
// 查询字段
export function getPermissionFieldsApi() {
  return request({
    url: `/data-permission/fields`,
    method: "get",
    version:'/api'
  })
}

// 新建权限
export function postPermissionsApi(data: any) {
  return request({
    url: `/data-permission/create_permission`,
    method: "post",
    version:'/api',
    data
  })
}
// 重命名权限
export function postRenamePermissionsApi(data: any) {
  return request({
    url: `/data-permission/rename_permission_name`,
    method: "post",
    version:'/api',
    data
  })
}
// 存储权限名称
export function postPermissionsRuleApi(data: any) {
  return request({
    url: `/data-permission/assign_data_permission`,
    method: "post",
    version:'/api',
    data
  })
}
//删除权限
export function deletePermissionsApi(data: any) {
  return request({
    url: `/data-permission/delete_permission`,
    method: "delete",
    version:'/api',
    data
  })
}
// 空间当前用户
export function getSpaceCurrentUserApi(data: any) {
  return request({
    url: `/user-manage/workspace_members_detail`,
    method: "get",
    version:'/api',
    params:data
  })
}
// 资源名称和数量
export function getResourceNameAndCountApi(data: any) {
  return request({
    url: `/user-manage/workspace_member_resources`,
    method: "get",
    version:'/api',
    params:data
  })
}
// 行列权限名称和数量
export function getDataSourceAndCountApi(data: any) {
  return request({
    url: `/user-manage/workspace_member_permission_summary`,
    method: "get",
    version:'/api',
    params:data
  })
}
// 数据权限添加用户
export function postPermissionsUsersApi(data: any) {
  return request({
    url: `/data-permission/add_permission_members`,
    method: "post",
    version:'/api',
    data
  })
}