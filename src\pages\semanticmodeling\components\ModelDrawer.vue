<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue';
import { getDatasourcesListApi } from "@/common/apis/llm"
import { getSemanticModelApi } from "@/common/apis/semantic";
import type { Database } from '@/pages/llm/dataSourceManage/typs'

const props = defineProps({
  visible: Boolean,
  treeData:{
    type:Array,
    default:[]
  },
  type: {
    type: String,
    default: 'add',
  },
  ModelData: {
    type: Object,
    default: () => ({})
  },
  fromId:{
    type:String,
    default:''
  },
});
const emit = defineEmits(['update:visible','successModel']);

const formRef = ref();
const form = ref({
  dataConnectionIds: [],
  parent_id: '',
  name: '',
  description: '',
});
const rules = {
  dataConnectionIds: [
    { required: true, message: '请选择数据连接', trigger: 'change' },
  ],
  parent_id: [
    { required: true, message: '请选择数据主题', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请输入数据模型名称', trigger: 'blur' },
  ],
};

const connectionOptions = ref<any>([]);
const themeOptions = <any>(props.treeData)

onMounted( async () => {
  getDatasetList()
  if (props.type === 'add') {
    form.value = { dataConnectionIds: [], parent_id: props.fromId, name: '', description: '' };
  } else if (props.type === 'edit' && props.ModelData) {
    // 获取数据模型详情
    const response = await getSemanticModelApi(Number(props.ModelData.id))
    // Ensure response is typed or safely accessed
    const data = (response as any)?.data || {};
    form.value = {
      dataConnectionIds: Array.isArray(data.dataConnectionIds) ? data.dataConnectionIds[0]: data.dataConnectionIds  || '',
      parent_id:  data.parent_id || '',
      name: data.name || '',
      description: data.description || '',
    };
  }
})
function getDatasetList() {
  getDatasourcesListApi().then((res) => {
    const response = res as { data: Database[] }
    connectionOptions.value = response?.data || []
  })
}

function handleClose() {
  emit('update:visible', false);
}
async function handleSubmit() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      if (!Array.isArray(form.value.dataConnectionIds)) {
        form.value.dataConnectionIds = form.value.dataConnectionIds ? [form.value.dataConnectionIds] : [];
      }
      emit('successModel', { ...form.value });
      // ElMessage.success(props.type === 'add' ? '新增成功' : '编辑成功');
      handleClose();

    }
  });
}
</script>

<template>
  <el-drawer
    :model-value="props.visible"
    :title="props.type === 'add' ? '新增数据模型' : '编辑数据模型'"
    size="720"
    :append-to-body="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
    class="model-drawer"
    footer-class="model-drawer-footer"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <div style="display: flex; gap: 16px;">
        <el-form-item label="数据连接" prop="dataConnectionIds" style="flex:1;">
          <el-select v-model="form.dataConnectionIds" placeholder="请选择数据连接" style="width: 100%;" :disabled="props.type === 'edit'" >
            <el-option v-for="item in connectionOptions" :key="item.id" :label="item.params.database" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据主题" prop="parent_id" style="flex:1;">
          <el-select v-model="form.parent_id" placeholder="请选择数据主题" style="width: 100%;">
            <el-option v-for="item in themeOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </div>
      <el-form-item label="数据模型名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入数据模型名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="4" resize="none" placeholder="定义描述，以便快速理解" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">{{ props.type === 'add' ? '新增' : '保存' }}</el-button>
    </template>
  </el-drawer>
</template>

<style lang="scss">
.model-drawer-footer{
  padding-bottom: 10px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
}

</style>
