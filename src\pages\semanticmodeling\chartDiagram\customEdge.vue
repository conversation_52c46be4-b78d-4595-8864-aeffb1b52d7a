<script setup>
import { SmoothStepEdge, EdgeLabelRenderer, getSmoothStepPath, useVueFlow } from '@vue-flow/core';
import relatedPopover from './relatedPopover.vue';
// import { computed } from 'vue'

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  sourceX: {
    type: Number,
    required: true,
  },
  sourceY: {
    type: Number,
    required: true,
  },
  targetX: {
    type: Number,
    required: true,
  },
  targetY: {
    type: Number,
    required: true,
  },
  sourcePosition: {
    type: String,
    required: true,
  },
  targetPosition: {
    type: String,
    required: true,
  },
  markerEnd: {
    type: String,
    required: false,
  },
  style: {
    type: Object,
    required: false,
  },
  modelType: {
    type: String,
    default: 'edit'
  }
})

const path = computed(() => getSmoothStepPath(props))
</script>

<script>
export default {
  inheritAttrs: false,
}
</script>

<template>
  <SmoothStepEdge :id="id" :style="style" :path="path[0]" :marker-end="markerEnd" />

  <EdgeLabelRenderer>
    <div
      :style="{
        pointerEvents: 'all',
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${props.targetX - 20}px,${props.targetY}px)`,
      }"
      class="nodrag nopan"
    >
      <relatedPopover :id="id" :modelType="modelType" />
    </div>
  </EdgeLabelRenderer>
</template>
