<script lang="ts" setup>
  const route = useRoute()
  const router = useRouter()
  const isExpand = ref(true);
  const activeIndex = ref('')
  const SideMenuList = computed(() => {
    const matchs = route.matched;
    let parentPath = matchs.length > 1 && matchs[matchs.length - 2].path;
    let curMenu = matchs.filter((item) => {
      return item.path === parentPath && parentPath !== '/';
    });
    if(curMenu && curMenu.length) {
      return curMenu[0].children;
    }
  })
  function handleToggleSidebar() {
    isExpand.value = !isExpand.value
  }
  function selectMenu(item: any) {
    activeIndex.value = item.path
    router.push({ path: item.path, query: {
      spaceCode: route.query.spaceCode || ''
    }
   })
  }
</script>

<template>
  <el-aside :width="isExpand ? '260px' : '50px'" class="chat-sider">
      <div class="sider-content">
        <!-- 标题 -->
        <div class="sidebar-header">
          <h3 v-if="isExpand">{{ $route.meta.groupTitle || $route.meta.title }}</h3>
            <el-icon class="iconfont" @click="handleToggleSidebar"><Fold v-if="isExpand" /><Expand v-else /></el-icon>
        </div>
        <!-- 左侧菜单内容 -->
        <el-menu :default-active="activeIndex" >
          <el-menu-item v-for="(item,key) in SideMenuList" :key="item.path" :class="{
            'active-menu':
              $route.path.indexOf(`${item.path}`) !== -1 }" @click="selectMenu(item)" v-show="!(item.meta?.hide)">
              <i class="iconfont mr2" :class="item.meta?.icon"></i> {{ item.meta?.title }}
          </el-menu-item>
        </el-menu>
      </div>
    </el-aside>
</template>

<style lang="scss" scoped>
.sider-content {
  padding: 0 10px;
  height: 100%;
  background: #FCFCFC;
  border-right: 1px solid #e5e7eb;
  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    border-bottom: 1px solid #e4e7ed;
    padding: 0 8px;
    .iconfont {
      font-size: 20px;
      cursor: pointer;
    }

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1f2937;

      .dark & {
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }
  .el-menu {
    border-right:none;
    margin-top: 15px;
  }
  .el-menu-item {
    height: 36px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    padding:0 15px !important;
    margin: 0 !important;
    &.active-menu {
      background: #EBF3FD;
      color: #005EE0;
      &:after {
        position: absolute;
        content: "";
        left: 0;
        top: 25%;
        width: 3px;
        height: 50%;
        background: #005EE0;
        border-radius: 0 5px 5px 0;
      }

    }
  }

}
</style>
