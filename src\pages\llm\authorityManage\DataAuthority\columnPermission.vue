<template>
  <el-form label-width="90px">
    <el-form-item label="数据连接">
      <el-select v-model="form.datasource" placeholder="请选择数据连接" style="width: 100%;">
        <el-option v-for="item in connectionOptions" :key="item.id" :label="item.params.database" :value="item.id" />
      </el-select>
    </el-form-item>
    <el-form-item label="权限规则">
      <el-radio-group v-model="form.rule">
        <el-radio label="forbid">禁止查看</el-radio>
        <el-radio label="mask">数据脱敏</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="权限字段" class="fields-list" v-if="form.datasource">
      <!-- 禁止查看 -->
      <div v-if="form.rule === 'forbid'" v-for="(item, idx) in form.forbidFields" :key="'forbid-' + idx"
        class="field-row">
        <el-select v-model="item.table" placeholder="请选择表" class="table-select mr5"
          @change="changeTableData(item)">
          <el-option v-for="table in tableList" :key="table.table_name" :label="table.table_name" :value="table.table_name" />
        </el-select>
        <el-select v-model="item.field" placeholder="请选择不可见字段" class="column-select" :disabled="!item.table"
          @visible-change="val => onFieldDropdownVisible(val, item)">
          <el-option v-for="col in item.fieldList" :key="col.alias" :label="col.alias" :value="col.alias" />
        </el-select>
        <el-button icon="Delete" type="text" @click="removeField(idx)" />
      </div>
      <!-- 数据脱敏 -->
      <div v-else v-for="(item, idx) in form.fields" :key="'mask-' + idx" class="field-row">
        <div class="left-row">
          <div class="table-width">
            <el-select v-if="item.showTable" v-model="item.table" placeholder="请选择表" class="table-select mr5"
              @change="changeTableData(item)">
              <el-option v-for="table in tableList" :key="table.id" :label="table.table_name" :value="table.id" />
            </el-select>
          </div>
          <el-select v-model="item.field" placeholder="请选择字段" class="column-select" :disabled="!item.table"
            @change="val => onColumnChange(item, val, item.table)"
            @visible-change="val => onFieldDropdownVisible(val, item)">
            <el-option v-for="col in item.fieldList" :key="col.alias" :label="col.alias" :value="col.alias" />
          </el-select>
          <template v-if="item.field">
            <span class="field-type">
              <el-icon v-if="item.type === '日期时间'" class="type-icon">
                <Calendar />
              </el-icon>
              <el-icon v-else-if="item.type === '整数'" class="type-icon">
                <Sort />
              </el-icon>
              <span>{{ item.type }}</span>
            </span>
            <el-checkbox v-model="item.canView" class="perm-checkbox">查看</el-checkbox>
            <el-checkbox v-model="item.canExport" class="perm-checkbox">导出</el-checkbox>
            <el-tooltip effect="dark" content="添加字段" placement="bottom">
              <el-icon @click="addField(idx, item)" style="margin:0 10px; cursor: pointer;">
                <Tickets />
              </el-icon>
            </el-tooltip>
          </template>
        </div>
        <el-button type="text" icon="Delete" @click="removeField(idx)" />
      </div>
      <div>
        <el-button type="primary" plain size="small" icon="Plus" @click="addRow" class="add-row-btn">新增一行</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { Calendar, Sort } from '@element-plus/icons-vue';
import { getDatasourcesListApi, getDatasourceTableListApi, getDatasourceTableFieldListApi } from "@/common/apis/llm"
import type { Database } from '@/pages/llm/dataSourceManage/typs'
import { ElMessage } from 'element-plus';

interface Props {
  selectedAuth: Object
}
const props = defineProps<Props>()
const connectionOptions = ref<any>([]);
const form = ref({
  datasource: '',
  rule: 'forbid',
  forbidFields: [
    { table: '', field: '', fieldList: [] }
  ],
  fields: [
    { table: '', field: '', type: '',  canView: false, canExport: false, showTable: true, fieldList: [] },
  ]
});
watch(() => props.selectedAuth, (v:any) => {
  setInitData()
});
watch(
  () => form.value.datasource,
  (val) => {
    if (val) {
      changeDataSource();
    }
  }
);

const tableList = ref([]);  //表
const dataTypeMap = ref({
  Integer: '整形',
  String: '字符串',
  Date: '日期',
  Float: '浮点',
});
onMounted(async () => {
  setInitData()
  getDatasetList()
})
function setInitData(){
  const v = props.selectedAuth as any
  form.value.datasource = v.datasource || ''
  form.value.rule = v.rule || 'forbid'
  if(form.value.rule === 'forbid') {
    form.value.forbidFields = (v.fields || [{ table: '', field: '' }]).map((f:any) => ({ ...f, fieldList: [] }))
  } else {
    form.value.fields = (v.fields || [{ table: '', field: '', type: '', canView: false, canExport: false, showTable: true }]).map((f:any) => ({ ...f, showTable: true, fieldList: [] }))
  }
}
// 获取数据源
function getDatasetList() {
  getDatasourcesListApi().then((res) => {
    const response = res as { data: Database[] }
    connectionOptions.value = response?.data || []
  })
}
// 获取数据源下的表
async function changeDataSource() {
  tableList.value = []
  const response = await getDatasourceTableListApi(Number(form.value.datasource))
  tableList.value = (response as any).data
}
// 获取表下字段（mask/forbid 通用，item 必须有 fieldList）
async function onFieldDropdownVisible(visible: boolean, item: any) {
  if (visible && item.table) {
    // mask 模式表id 可能是 id，forbid 模式是 table_name
    let tableId = item.table;
    if (typeof tableId !== 'number') {
      const table = tableList.value.find((t:any) => t.table_name === item.table);
      if (!table) return;
      tableId = table.id;
    }
    const response = await getDatasourceTableFieldListApi(Number(tableId));
    const data = (response as any).data;
    if (data) {
      item.fieldList = data.map((field: any) => ({
        fieldId: field.id,
        alias: field.alias || field.field || '未知字段',
        fieldType: field.data_type || 'string',
      }));
    } else {
      item.fieldList = [];
    }
  }
}
// 选择表事件
async function changeTableData(item: any) {
  item.field = '';
  item.fieldList = [];
}
// 选择字段事件
function onColumnChange(item: any, val: string, table: string) {
  const col = item.fieldList.find(c => c.fieldId == val);
  item.type = col ? dataTypeMap.value[col.fieldType] : '';
}
function addField(idx: number, item: any) {
  form.value.fields.splice(idx + 1, 0, { table: item.table, field: '', type: '', canView: false, canExport: false, showTable: false, fieldList: [] });
}
function addRow() {
  if (form.value.rule === 'forbid') {
    form.value.forbidFields.push({ table: '', field: '', fieldList: [] });
  } else {
    form.value.fields.push({ table: '', field: '', type: '',  canView: false, canExport: false, showTable: true, fieldList: [] });
  }
}
function removeField(idx: number) {
  if (form.value.rule === 'forbid') {
    if (form.value.forbidFields.length) {
      form.value.forbidFields.splice(idx, 1);
    }
  } else {
    if (form.value.fields.length) {
      form.value.fields.splice(idx, 1);
    }
  }
}
function saveEvent(){
  if(form.value.rule === 'forbid'){
    for(const [idx, item] of form.value.forbidFields.entries()){
      if(!item.table || !item.field){
        ElMessage.error(`第${idx+1}行表和字段必填`);
        return;
      }
    }
  }else{
    for(const [idx, item] of form.value.fields.entries()){
      if(!item.table || !item.field){
        ElMessage.error(`第${idx+1}行表和字段必填`);
        return;
      }
    }
  }
  // 校验通过
  let submitData = {
    datasource:form.value.datasource,
    rule:form.value.rule,
    fields:[]
  }
  if(form.value.rule === 'forbid') {
    submitData.fields = form.value.forbidFields
  } else {
    form.value.fields.forEach(val=>{
      const { table, field, type, canView, canExport } = val;
      submitData.fields.push({
        table,
        field,
        type,
        canView,
        canExport
      })
    })
  }
  return submitData
}

defineExpose({ saveEvent });
</script>

<style scoped lang="scss">
.el-form-item {
  margin-bottom: 18px;

  &.fields-list {
    :deep(.el-form-item__content) {
      display: block;
    }
  }
}

.data-select {
  width: 300px;
}

.field-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;

  .left-row {
    display: flex;
    flex: 1;
    white-space: nowrap;
    align-items: center;

    .table-width {
      width: 180px;
      margin-right: 8px;
    }

    .table-select {
      width: 100%
    }

    .column-select {
      width: 180px;
    }

    .mask-select {
      flex: 1;
      margin-right: 8px;
    }
  }
}

.column-select {
  margin-right: 8px;
}

.field-type {
  display: flex;
  align-items: center;
  margin-right: 8px;
  min-width: 60px;
  color: #888;
}

.type-icon {
  margin-right: 2px;
}

.perm-checkbox {
  margin-right: 8px;
}

.add-field-btn {
  color: #1677ff;
  margin-right: 8px;
  font-size: 14px;
  padding: 0 4px;
}

.add-row-btn {
  margin-top: 4px;
}
</style>
