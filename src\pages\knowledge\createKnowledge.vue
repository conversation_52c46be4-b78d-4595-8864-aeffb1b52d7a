<script lang="ts" setup>
import type { FormInstance, FormRules } from "element-plus"
import { Back } from "@element-plus/icons-vue"
import { ElMessage } from "element-plus"
import { v4 as uuidv4 } from "uuid"
import { computed, reactive, ref } from "vue"
import { useRoute, useRouter } from "vue-router"
import {
  addKnowledgeApi,
  addKnowledgeTmpApi,
  postDocumentApi,
  syncbatchDocumentApi
} from "@/common/apis/knowledge"
import tagSelect from "./components/tagSelect/tagSelect.vue"
import FileFormComponent from "./fileFormComponent.vue"

const router = useRouter()
const route = useRoute()

// 表单数据
const formData = reactive({
  // 配置
  name: "",
  vector_type: "VectorStore",
  domain_type: "Normal",
  labels: [],
  desc: ""
})

// 表单规则
const rules = reactive<FormRules>({
  name: [{ required: true, message: "请输入知识库名称", trigger: "blur" }],
  desc: [{ required: true, message: "请输入知识库描述", trigger: "blur" }]
})

const formRef = ref<FormInstance>()
const fileFormRef = ref()
const loading = ref<boolean>(false)

// 文件表单数据
const fileFormData = reactive({
  doc_type: "TEXT",
  doc_name: "",
  source: "",
  content: "",
  chunk_strategy: "Automatic",
  chunk_size: 512,
  chunk_overlap: 50,
  separator: "\n",
  enable_merge: false
})

// 是否为新增文件
const isFileAdd = computed<boolean>(() => {
  return !!route?.query?.knowledge_name
})
// 新增文件时，获取知识库名称
const knowledgeName = computed<any>(() => {
  return route?.query?.knowledge_name
})

const titleText = computed<string>(() => {
  return isFileAdd.value ? "新增文件" : "新增知识库"
})

// 表单验证方法
async function validateForm() {
  if (!formRef.value) return false
  try {
    await formRef.value.validate()
    return true
  } catch (error) {
    return false
  }
}

// 重置表单
function resetForm(params: any = {}) {
  if (!formRef.value) return
  formRef.value.resetFields()

  // 如果有传入参数，则设置表单数据
  if (params && Object.keys(params).length > 0) {
    Object.assign(formData, params)
  }
}

// 后退到知识库首页
function goBack(): void {
  if (isFileAdd.value) {
    router.push({
      name: "knowledgeDetail",
      query: {
        spaceCode: route.query.spaceCode || "",
        knowledge_id: route.query.knowledge_id || ""
      }
    })
  } else {
    router.push({
      name: "knowledge",
      query: {
        spaceCode: route.query.spaceCode || ""
      }
    })
  }
}

// 提交新增
async function submitKnowledge(): Promise<void> {
  // 开始校验基础表单
  let validBase: boolean = true
  if (!isFileAdd.value) {
    validBase = await validateForm()
  }
  // 校验文件表单
  let validFile: boolean = true
  if (fileFormRef.value) {
    validFile = await fileFormRef.value.validateForm()
  }
  if (!!validBase && !!validFile) {
    loading.value = true
    // 获取文件表单数据
    const fileData = fileFormRef.value ? fileFormRef.value.getFormData() : {}
    try {
      const space_name = formData.name
      console.log(formData,'formDataformData')
      if (space_name) {
        if(fileData.doc_type === 'TEXT') {
          if(!isFileAdd.value) {
            // 1. 调用新增知识库基础信息
            await addKnowledgeApi(formData);
          }
          //2 新增文本
          const fileParams = {
            doc_name: fileData.doc_name,
            source: fileData.source,
            content: fileData.content,
            doc_type: fileData.doc_type
          }
          const fileRes = await postDocumentApi(space_name, fileParams)
          const response = fileRes as { data: any }
          // 3 文本切片
          const trunkData = [
            {
              name: fileData.doc_name,
              doc_id: response?.data,
              chunk_parameters: {
                chunk_strategy: fileData.chunk_strategy,
                chunk_size: fileData.chunk_size,
                chunk_overlap: fileData.chunk_overlap,
                separator: fileData.separator,
                // enable_merge: fileData.enable_merge
              }
            }
          ]
          await syncbatchDocumentApi(space_name, trunkData)
        } else {
          // 文档类型
          if(!isFileAdd.value) {
            // 新增知识库
            // 1 调用从临时空间创建知识库
            let tmpParmas = {
              temp_space_name: uuidv4(),
              real_space_name: formData.name,
              vector_type: formData.vector_type,
              labels:formData.labels,
              desc: formData.desc,
              domain_type: formData.domain_type,
              uploaded_files: fileData.files
            }
            const resData = await addKnowledgeTmpApi(tmpParmas)
            const response = resData as { data: any }
            const created_documents = response.data.created_documents
            // 2 文档切片
            // 遍历 fileData.files 和 created_documents，组成 trunkData
            const trunkData = created_documents.map((doc: any, idx: number) => {
              const file = fileData.files[idx]
              return {
                name: doc.doc_name,
                doc_id: doc.doc_id,
                chunk_parameters: {
                  chunk_strategy: file.chunk_strategy,
                  chunk_size: file.chunk_size,
                  chunk_overlap: file.chunk_overlap,
                  separator: file.separator,
                  // enable_merge: file.enable_merge
                }
              }
            })
            await syncbatchDocumentApi(space_name, trunkData)
          } else {
            // 新增文件 批量分片
            let trunkData = []
            fileData.files.forEach((val) => {
              trunkData.push({
                name: val.name,
                doc_id: val.doc_id,
                chunk_parameters: {
                  chunk_strategy: val.chunk_strategy,
                  chunk_size: val.chunk_size,
                  chunk_overlap: val.chunk_overlap,
                  separator: val.separator,
                  // enable_merge: val.enable_merge
                }
              })
            })
            await syncbatchDocumentApi(space_name, trunkData)
          }
        }
      }
      ElMessage.success("操作成功")
      loading.value = false
      goBack()
    } catch (err) {
      loading.value = false
    }
  }
}
onMounted(()=>{
  if(isFileAdd.value) {
    formData.name = knowledgeName.value
  }
})
</script>

<template>
  <div class="knowledge-manage-container" v-loading="loading">
    <div class="head">
      <div class="title">
        <div class="back-icon-wrapper" @click="goBack">
          <i class="ChatData-fanhui iconfont"></i>
        </div>
        <span class="title-text">{{ titleText }}</span>
      </div>
      <div class="btns">
        <el-button type="primary" size="small" @click="submitKnowledge">
          确定
        </el-button>
        <el-button type="info" size="small" @click="goBack">
          取消
        </el-button>
      </div>
    </div>

    <div class="knowledge-form-card">
      <el-form
        v-if="!isFileAdd"
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-position="top"
        class="knowledge-form"
      >
        <!-- 配置部分 -->
        <div class="form-section">
          <h3 class="section-title">
            <span class="icon"><i class="iconfont ChatData-peizhi" /></span>
            <span class="text">配置</span>
          </h3>
          <el-form-item label="知识库名称" prop="name" required>
            <el-input
              v-model="formData.name"
              placeholder="请输入知识库名称"
              clearable
            />
          </el-form-item>
          <el-row>
            <el-col :span="12">
              <el-form-item label="存储类型" required>
                <el-select v-model="formData.vector_type" disabled>
                  <el-option label="Vector Store" value="Vector Store" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item label="领域类型" required>
                <el-input v-model="formData.domain_type" disabled />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="标签">
            <tagSelect
              valueKey="label_name"
              allow-create
              v-model="formData.labels"
              style="width: 50%"
            />
          </el-form-item>

          <el-form-item label="描述" prop="desc" required>
            <el-input
              v-model="formData.desc"
              type="textarea"
              :rows="4"
              placeholder="定义知识库描述"
            />
          </el-form-item>
        </div>
        <!-- 文件表单组件 -->
      </el-form>
      <FileFormComponent
        v-model="fileFormData"
        :basic-data="formData"
        ref="fileFormRef"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.knowledge-manage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .head {
    width: 100%;
    height: 50px;
    padding: 0 25px;
    box-sizing: border-box;
    box-shadow: 0px 1px 0px 0px #ededed;
    display: flex;
    align-items: center;

    .title {
      display: flex;
      align-items: center;
      gap: 15px;

      .back-icon-wrapper {
        padding: 5px;
        border-radius: 8px;
        color: #005ee0;
        background: #ebf3fd;
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      .title-text {
        font-weight: bold;
        font-size: 16px;
        color: #262626;
        line-height: 22px;
      }
    }

    .btns {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 5px;
      justify-content: end;
    }
  }
}

.knowledge-form-card {
  height: calc(100% - 50px);
  margin-top: 40px;
  overflow: auto;
}

.knowledge-form {
  max-width: 860px;
  margin: 0 auto;
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .knowledge-form-card {
    margin: 0;
  }
}
.form-section {
  border-left: 1px solid #ebeef5;
  position: relative;
  padding: 45px 0 20px 25px;
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    position: absolute;
    left: -16px;
    top: -10px;

    .icon {
      margin-right: 15px;
      font-weight: normal;
      width: 20px;
      height: 20px;
      border-radius: 20px;
      padding: 5px;
      background: #ebf3fd;
      border: 1px solid #d9e7fc;
      color: #005ee0;
    }
  }
}
</style>
