<script setup lang="ts">
import { Star, ArrowRight } from '@element-plus/icons-vue'

interface Props {
  icon?: string
  name: string
  active?: boolean
  showStar?: boolean
  showArrow?: boolean
  moreText?: string
  onlyShowIcon?: boolean
}

defineProps<Props>()

defineEmits<{
  click: []
}>()
</script>

<template>
  <div class="click-item" :class="{ active: active, 'only-icon': onlyShowIcon }" @click="$emit('click')">
    <span v-if="!onlyShowIcon" class="symbol">
      <span class="mark"></span>
    </span>
    <i class="iconfont click-icon" :class="icon" v-if="icon"></i>
    <span v-if="!onlyShowIcon" class="content">{{ name }}</span>
    <!-- <el-icon v-if="showStar && !onlyShowIcon" class="star-icon">
      <Star />
    </el-icon> -->
    <span v-if="moreText && !onlyShowIcon" class="more-text">{{ moreText }}</span>
    <el-icon v-if="showArrow && !onlyShowIcon" class="arrow-icon">
      <ArrowRight />
    </el-icon>
    <slot></slot>
  </div>
</template>

<style scoped lang="scss">
.click-item {
  height: 36px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;

  &.active {
    background: #EBF3FD;
    color: #005EE0;
    font-weight: bold;

    .mark {
      display: block;
    }

    .click-icon {
      color: #005EE0;
    }
  }

  &:hover {
    background: #EBF3FD;
    color: #005EE0;
  }

  &.only-icon {
    justify-content: center;
    margin: 0 8px;

    .click-icon {
      margin: 0;
    }
  }

  .symbol {
    width: 10px;
  }

  .mark {
    display: none;
    width: 3px;
    height: 18px;
    background: #005EE0;
    border-radius: 0px 100px 100px 0px;
  }

  .click-icon {
    font-size: 14px;
    margin-right: 6px;
    color: #4C6F88;
  }

  .content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .star-icon {
    color: #FFD700;
    margin-left: 4px;
  }

  .more-text {
    color: #909399;
    font-size: 12px;
    margin-left: 4px;
  }

  .arrow-icon {
    color: #909399;
    margin-left: 4px;
  }
}
</style>