<script lang="ts" setup>
import type {
  ChatHistoryResponse,
  IChatDialogueSchema,
  IApp,
  UserChatContent,
  StoreChatDataHistoryRequest,
} from "@@/apis/chat/type";
import {
  getSqlExampleApi,
  getIntentAnalysisApi,
  generateSqlApi,
  correctSqlApi,
  executeSqlApi,
  addChatDataHistoryApi,
  deleteHistoryApi,
  volatilityAttributionApi,
  getQuestionRecommendApi,
  volatilityAttributionSqlApi,
} from "@/common/apis/chat";
import type { Agent, Dialogue } from "@/common/apis/chat/type";
import { provide, ref, watch, onMounted, toRaw, computed } from "vue";
import { useChat } from "./composables/useChat";
import ChatContentContainer from "./components/ChatContentContainer.vue";
import ChatInputPanel from "./components/ChatInputPanel.vue";
import ClickItem from "@/common/components/ClickItem/index.vue";
import HistoryItem from "@/common/components/HistoryItem/index.vue";
import { Plus, Search, Filter } from "@element-plus/icons-vue";
import {
  saveMemoryDetail
} from "@/common/apis/agent/index";
import { set, get, omit, cloneDeep } from "lodash-es";
import {
  getAgentListApi,
  getDialogueListApi,
  getMessageDtailList,
  QAguidance,
  QAoptimize,
  getSqlToJsonApi,
} from "@/common/apis/chat";
import { getDatasourceTableFieldListApi } from "@/common/apis/llm";
import markdownit from "markdown-it";
import { v4 as uuidv4 } from "uuid";
import { useRoute, useRouter } from "vue-router";
// 响应式数据
const history = ref<ChatHistoryResponse>([]);
const replyLoading = ref(false);
const canAbort = ref(false);
const currentDialogue = ref<IChatDialogueSchema>({} as IChatDialogueSchema);
const appInfo = ref<IApp>({});
const temperatureValue = ref(0.6);
const maxNewTokensValue = ref(4000);
const resourceValue = ref<any>(null);
const modelValue = ref("");
const dialogueList = ref<Dialogue.DialogueList[]>([]);
const listLoading = ref(false);
const historyLoading = ref(false);
const historyKeyword = ref<string>(""); // 历史对话 查询关键字
const isExpand = ref(true);
const searchVisible = ref(false);

const searchAgent = ref("");
const agentList = ref<Agent.TableData[]>([]);
const agentSearchPopoverRef = ref<any>(null); // 助手搜索弹窗ref
const selectedAgent = ref<Agent.TableData | null>(null);
const fieldList = ref<Record<string, any>[]>([]);
const route = useRoute();
const router = useRouter();
// 当前选中是否为chat data 助手
const isChatDataAgent = computed(() => {
  const { app_code, app_type } = toRaw(selectedAgent.value) as Agent.TableData;
  return app_code == "chat_with_db_execute" || app_type === "chat_with_db_execute";
});

// markdown
const md = markdownit({
  html: true,
  linkify: true,
  typographer: true,
  langPrefix: "chatData-md-", //css prefix
});
const inputRef = ref(null);
const handleShow = () => {
  if (inputRef.value) {
    inputRef.value.focus(); // 聚焦 input 元素
  }
};
// 获取助手列表
function getAgentList(params: Record<string, any> = {}) {
  getAgentListApi({
    page: 1,
    pageSize: 10,
    app_name: searchAgent.value,
    workspace_id:route.query.spaceCode || ''
  }).then((res) => {
    const response = res as { data: Agent.ListResponse };
    agentList.value = response.data.app_list;
    const { initAgent = "" } = params;
    // 重置选择的助手
    selectedAgent.value = initAgent
      ? agentList.value.find((item) => item.app_code === initAgent)
      : agentList.value[0];
    // 根据助手类型切换 历史对话
    getDialogueList();
  });
}

// 获取对话列表
function getDialogueList() {
  dialogueList.value = [];
  getDialogueListApi({
    app_code: selectedAgent.value?.app_code,
    keyword: historyKeyword.value,
  }).then((res) => {
    const response = res as Dialogue.DialogueListResponseData;
    dialogueList.value = response.data || [];
  });
}
async function handleSelectAgent(agent: Agent.TableData) {
  selectedAgent.value = agent;
  // 重置选择的历史对话,生成新的uuid 为当前窗口的
  // currentDialogue.value = {} as IChatDialogueSchema;
  // getDialogueList();
  // history.value = [];
  onAddNewDialogue();
  console.log("选择助手:", agent.app_name, agent, isChatDataAgent.value);
  // 这里可以添加切换助手的业务逻辑
}

function onAddNewDialogue() {
  // console.log("新增对话");
  // 重置选择的历史对话,生成新的uuid 为当前窗口的
  currentDialogue.value = {} as IChatDialogueSchema;
  getDialogueList();
  history.value = [];
}

function handleSelectDialogue(dialogue: Dialogue.DialogueList) {
  (currentDialogue as any).value = dialogue;
  // 这里可以添加切换对话的业务逻辑
  getChatMsgs();
}
// 获取单个会话历史数据
function getChatMsgs() {
  getMessageDtailList({
    conv_uid: currentDialogue.value.conv_uid,
    dialog_id: currentDialogue.value.dialog_id,
  }).then(({ data = [] }) => {
    history.value = [];
    historyLoading.value = true;
    // 过滤掉data中role为 view 的数据
    let preChatLog = data.filter(
      (item: Record<string, any>) => item.role !== "view"
    );
    // chat data 助手 需要转换数据格式
    (history as any).value = isChatDataAgent.value
      ? transformChatDataHistory(preChatLog as any)
      : preChatLog;
    console.log(history.value, isChatDataAgent.value, "history.value");
    setTimeout(() => {
      historyLoading.value = false;
    }, 100);
  });
}

function handleSearch() {
  agentList.value = [];
  getAgentList();
}
function handleCancel() {
  agentSearchPopoverRef.value.hide();
}
function handleToggleSidebar() {
  isExpand.value = !isExpand.value;
}

// 聊天相关
const { chat, ctrl } = useChat({
  app_code: selectedAgent.value?.app_code || "",
});

function onAddAgent() {
  router.push({
    path: "/agent/agentManage",
    query: {
      spaceCode: route.query.spaceCode || "",
    },
  });
}

// 滚动引用
const scrollRef = ref<any>(null);
const inputPanelRef = ref();

// 处理聊天
async function handleChat(content: UserChatContent, data?: any): Promise<void> {
  return new Promise<void>((resolve) => {
    setReplyLoading(true);
    // 会话id 弹窗id 优先级以 当前点击的历史-现在的data-重新创建
    let conv_uid = currentDialogue.value?.conv_uid || data.conv_uid || uuidv4();
    let dialog_id =
      currentDialogue.value?.dialog_id || data.dialog_id || uuidv4();
    // 添加用户消息到历史记录
    const userMessage = {
      role: "human" as const,
      context: typeof content === "string" ? content : JSON.stringify(content),
      // model_name: "qwen2.5-72b-instruct",
      order: history.value.length + 1,
      conv_uid, // 每条用户提问内添加conv_uid，取历史记录内的conv_uid或 当前新增的会话内的conv_uid
      time_stamp: Date.now(),
      dialog_id,
    };

    // 添加AI思考消息
    const aiMessage = {
      role: "view" as const,
      context: "",
      // model_name: "qwen2.5-72b-instruct",
      tipAlias: "AI正在分析问题...", // 另外的提示语
      order: history.value.length + 2,
      time_stamp: Date.now(),
      is_new_answer: true,
      thinking: true,
    };

    let tempHistory = [...history.value, userMessage, aiMessage] as any;
    const aiMessageIndex = tempHistory.length - 1;
    setHistory(tempHistory);
    const queryData = {
      user_input: content,
      conv_uid,
      dialog_id,
      app_code: selectedAgent.value?.app_code,
    };
    // console.log(currentDialogue, data, "handleChat");
    // 调用聊天API、首先发起引导
    let guideContent = "";
    chat({
      data: queryData, // queryData已包含app_code
      queryAgentURL: "/api/v1/chat/guide-question",
      chatId: currentDialogue.value.conv_uid || data.conv_uid || "default",
      onMessage: (message: string | any) => {
        guideContent += message?.choices[0].delta.content || "";
        // console.log(guideContent, "guide message");
        // 问题格式匹配正确，无需引导
        if (
          guideContent === "<understand/>" ||
          guideContent.includes("<understand/>")
        ) {
          // 如果当前助手是chat_data，则提前设置思考状态
          if (!!isChatDataAgent.value) {
            // 设置思考状态
            tempHistory[aiMessageIndex].thinking = true;
            tempHistory[aiMessageIndex].tipAlias = "AI正在优化问题...";
            setReplyLoading(true);
            setHistory([...tempHistory]);
          }
          // 调用问题优化接口
          QAoptimize(omit(queryData, ["dialog_id"]) as any)
            .then((optimizeQuestionData: any) => {
              // 如果当前助手是chat_data，则调用chat_data的聊天API、其他继续使用sse方式
              if (!!isChatDataAgent.value) {
                // 调用chat_data的聊天API
                chatDataGetData(
                  content as string,
                  tempHistory,
                  aiMessageIndex,
                  {
                    data,
                    ...queryData,
                  },
                  optimizeQuestionData
                );
              } else {
                // 调用聊天API
                chat({
                  data: {
                    user_input: content,
                    // model_name: modelValue.value,
                    ...data,
                  },
                  queryAgentURL: "/api/v1/chat/completions",
                  chatId:
                    currentDialogue.value.conv_uid ||
                    data.conv_uid ||
                    "default",
                  onMessage: (message: string) => {
                    setCanAbort(true);
                    // md.render(message)
                    tempHistory[aiMessageIndex].context = message;
                    tempHistory[aiMessageIndex].thinking = false;
                    setHistory([...tempHistory]);
                  },
                  onDone: () => {
                    setReplyLoading(false);
                    setCanAbort(false);
                    resolve();
                  },
                  onError: (message: string) => {
                    setReplyLoading(false);
                    setCanAbort(false);
                    tempHistory[aiMessageIndex].context = message;
                    tempHistory[aiMessageIndex].thinking = false;
                    setHistory([...tempHistory]);
                    resolve();
                  },
                  ctrl,
                });
              }
            })
            .catch(() => {
              setReplyLoading(false);
              setCanAbort(false);
              tempHistory[aiMessageIndex].context = message;
              tempHistory[aiMessageIndex].thinking = false;
              setHistory([...tempHistory]);
            });
        } else {
          // 问题格式匹配错误，需要引导
          setReplyLoading(false);
          setCanAbort(true);
          tempHistory[aiMessageIndex].context = guideContent;
          tempHistory[aiMessageIndex].thinking = false;
          setHistory([...tempHistory]);
        }
      },
      onDone: () => {
        // console.log(arguments, "onDone");
        setCanAbort(false);
        // tempHistory[aiMessageIndex].tipAlias = "";
        resolve();
      },
      onError: (message: string) => {
        setReplyLoading(false);
        setCanAbort(false);
        tempHistory[aiMessageIndex].context = message;
        tempHistory[aiMessageIndex].tipAlias = "";
        tempHistory[aiMessageIndex].thinking = false;
        setHistory([...tempHistory]);
        resolve();
      },
      ctrl,
    });
  });
}

// 处理chat-data 助手下的请求
/**
 *
 * @param query 用户输入的字符串
 * @param origin 原始对话框历史数据
 * @param aiIndex 当前最新AI消息的索引
 * @param originData chat 方法传入的参数
 * @param optimizeQuestion 优化的问题数据
 */
async function chatDataGetData(
  query: string,
  origin: Array<Record<string, any>>,
  aiIndex: number,
  originData: Record<string, any>,
  optimizeQuestion: any
) {
  // 设置初始渲染数据，chatdata 使用vue组件来实行渲染
  let renderData:any = { renderType: "vnode" };
  try {
    // 设置优化问题
    // 获取优化后的问题字符串
    let optimized_question = get(
      optimizeQuestion,
      "data.optimized_question",
      ""
    );
    // 获取优化后的 实际的 问题,在后续模型调用链中使用
    let optimized_question_real = getQuesOpt(optimized_question);

    setReplyLoading(true);
    // 第一步：并行请求 SQL 示例和意图分析、推荐问题
    const [sqlExampleData, intentAnalysisData]: any = await Promise.all([
      getSqlExampleApi({
        query: optimized_question_real,
        limit: 5,
        app_code: selectedAgent.value?.app_code,
      }),
      getIntentAnalysisApi({
        user_input: optimized_question_real,
        app_code: selectedAgent.value?.app_code,
      }),
    ]);

    // 设置意图分析数据
    set(
      renderData,
      "intentAnalysisData",
      intentAnalysisData?.data?.intent_analysis
    );
    // 获取dataset_id
    const dataset_id = [intentAnalysisData?.data?.intent_analysis?.dataset_id];

     // 获取推荐问题
    getQuestionRecommendApi({
      user_input: optimized_question_real,
      app_code: selectedAgent.value?.app_code,
      dataset_id
    }).then(res => {
      let req_rec_data: any = res;
      set(renderData, "req_rec", req_rec_data?.data?.recommendations);
    });


    // 设置 SQL few_shot sql示例 数据、初始等待状态
    set(renderData, "sqlData.few_shot", sqlExampleData?.data);
    set(renderData, "sqlData.renderSQL.status", "pending");
    set(renderData, "sqlData.lastExecutionSql.status", "pending");
    set(renderData, "sqlData.sqlFlowChart.status", "pending");
    set(renderData, "sqlData.sql_edit.status", "pending");
    set(renderData, "sqlData.attribution_analysis.status", "pending");
    set(renderData, "sqlData.data_explain.status", "pending");
    // 关闭思考状态 先显示现有数据
    origin[aiIndex].thinking = false;
    origin[aiIndex].context = renderData;
    setHistory([...origin] as any);

    // 第二步：生成 SQL、设置显示状态、数据
    const generateResult: any = await generateSqlApi({
      user_input: optimized_question_real,
      dataset_id,
      app_code: selectedAgent.value?.app_code,
      ext_info: {
        few_shot: sqlExampleData.data,
      },
    });

    await set(renderData, "sqlData.renderSQL", {
      data: generateResult?.data?.sql,
      status: "success",
    });
    (history.value as any)[aiIndex].context = {
      ...(history.value as any)[aiIndex].context,
      ...renderData,
    };
    setHistory([...(history.value as any)]);

    // 第三步：修正 SQL、设置显示状态、数据，存储sql  json格式化
    const correctResult: any = await correctSqlApi({
      ext_info: {
        few_shot: sqlExampleData.data,
      },
      dataset_id,
      app_code: selectedAgent.value?.app_code,
      user_input: `${query}\n${generateResult?.data?.sql}`,
    });
    set(renderData, "sqlData.lastExecutionSql", {
      data: correctResult?.data?.sql,
      status: "success",
    });
    // 存储sql  json格式化
    const sqlToJsonResult: any = await getSqlToJsonApi({
      sql: correctResult?.data?.sql,
      action: "sql2json",
      option: {
        dialect: "mysql",
      },
      db_type: "sql",
    });
    set(renderData, "sqlData.sql_edit", {
      data: sqlToJsonResult?.data,
      status: "success",
    });
    (history.value as any)[aiIndex].context = {
      ...(history.value as any)[aiIndex].context,
      ...renderData,
    };
    setHistory([...(history.value as any)]);

    // 第四步：执行 SQL、设置显示状态、数据
    const executeResult: any = await executeSqlApi({
      // db_name: "budget",
      sql: correctResult?.data?.sql,
      dataset_id: dataset_id[0] ? dataset_id : [18],
      // dataset_id: [9],
      user_input: query,
      field_id: (get(renderData, "intentAnalysisData.metrics[0]", "").split(",")[0]) - 0,
    }).catch((error: any) => {
      console.error("SQL执行失败66:", error);
      // 流程调用接口时, 需要处理错误数据
      set(renderData, "sqlData.sqlFlowChart", {
        data: error.message,
        status: "success",
      });
      // 更新状态
      (history.value as any)[aiIndex].context = {
        ...(history.value as any)[aiIndex].context,
        ...renderData,
      };
      throw error;
    });
    set(renderData, "sqlData.sqlFlowChart", {
      data: { ...executeResult?.data, label: executeResult?.data?.label },
      status: "success",
    });

    // 是否需要波动归因/数据解读
    if (!!executeResult?.data?.attribution_analysis && !!executeResult?.data?.dimensions?.length) {
      let resultData = await volatilityAttribution(
        executeResult?.data,
        sqlToJsonResult?.data,
        optimized_question_real,
        originData,
        aiIndex,
        dataset_id
      );
      if (!!resultData?.volatilityAttributionData) {
        set(renderData, "sqlData.attribution_analysis", {
          data: resultData?.volatilityAttributionData.data,
          status: "success",
        });
      }
      if (!!resultData?.dataExplain) {
        set(renderData, "sqlData.data_explain", {
          data: resultData?.dataExplain,
          status: "success",
        });
      }
      // console.log(resultData, "dataExplain attributionData after");
    }
    // 更新状态
    (history.value as any)[aiIndex].context = {
      ...(history.value as any)[aiIndex].context,
      ...renderData,
    };
    // 存储历史对话
    handleChatDataHistory(query, history.value[aiIndex], originData);
    setHistory([...(history.value as any)]);
    console.log(renderData, "renderData");
    setReplyLoading(false);
  } catch (error) {
    // 错误处理
    console.error("SQL处理过程出错:", error);
    setReplyLoading(false);
    // 更新错误状态
    origin[aiIndex].thinking = false;
    origin[aiIndex].error = true;
    setHistory([...(origin as any)]);
  }
}

// 获取波动归因、数据解读
async function volatilityAttribution(
  executeResult: any,
  sqlToJsonResult: any,
  query: string,
  originData: Record<string, any>,
  curQuestionIndex: number,
  dataset_id: string[] | number[]
) {
  let dataExplainMsg = "";
  // 获取时间范围
  let [startTime = "", endTime = ""] = executeResult["time-range"] || [
  ];
  // 获取where 时间字段
  let timeField = executeResult["time-field"];
  let dimensions =
    executeResult.dimensions.length >= 5
      ? executeResult.dimensions.slice(0, 5)
      : executeResult.dimensions; // 默认先截取最大5个
  // 拼接波动归因rank请求参数
  let getJsonSqlData = {
    action: "json2sql",
    db_type: "sql",
    option: {
      dialect: "mysql",
    },
    json_data: dimensions.reduce((acc, cur) => {
      acc.push({
        ...sqlToJsonResult,
        groupBy: [...sqlToJsonResult?.groupBy, cur.field],
        columns: [
          ...sqlToJsonResult?.columns,
          {
            expression: cur.field,
            alias: cur.alias,
          },
        ],
        limit: 3,
      });
      return acc;
    }, []),
  };
  // 拼接波动归因calculate请求参数
  let getJsonSqlDataCalculate = {
    action: "json2sql",
    db_type: "sql",
    option: {
      dialect: "mysql",
    },
    json_data: {
      from: {
        ...sqlToJsonResult,
        where: {
          ...sqlToJsonResult.where,
          conditions: [
            ...sqlToJsonResult.where.conditions,
            ...timeField.reduce((acc, cur, index) => {
              acc.push({
                left: cur,
                operator: "eq",
                right: `'${startTime.split("-")[index]}'`,
              });
              return acc;
            }, []),
          ],
        },
        limit: 3,
      },
      to: {
        ...sqlToJsonResult,
        where: {
          ...sqlToJsonResult.where,
          conditions: [
            ...sqlToJsonResult.where.conditions,
            ...timeField.reduce((acc, cur, index) => {
              acc.push({
                left: cur,
                operator: "eq",
                right: `'${endTime.split("-")[index]}'`,
              });
              return acc;
            }, []),
          ],
        },
        limit: 3,
      },
    },
  };
  // 先获取波动归因 rank 的sql
  let getJsonSql: any = await getSqlToJsonApi(getJsonSqlData);
  // 获取波动归因 calculate 的sql
  let getJsonSqlCalculate: any = await volatilityAttributionSqlApi(
    getJsonSqlDataCalculate
  );
  let [volatilityAttributionData, dataExplain]: any[] = await Promise.all([
    volatilityAttributionApi({
      dataset_id,
      sql_calculate: getJsonSqlCalculate.data,
      sql_rank: dimensions.reduce((acc, cur, index) => {
        acc.push({
          [cur.id || cur.field_id]: getJsonSql.data[index],
        });
        return acc;
      }, []),
    }),
    chat({
      data: {
        dataset_id,
        sql_calculate: getJsonSqlCalculate.data,
        sql_rank: dimensions.reduce((acc, cur, index) => {
          acc.push({
            [cur.id]: getJsonSql.data[index],
          });
          return acc;
        }, []),
        user_input: query,
      },
      queryAgentURL: "/api/v1/chat/data-explain",
      chatId:
        currentDialogue.value.conv_uid || originData.conv_uid || "default",
      onMessage: (message: any) => {
        // 查看现在状态是否为pending
        let data_explain_status = get(
          history.value[curQuestionIndex],
          "context.sqlData.data_explain.status",
          ""
        );
        // 如果状态为pending，则设置为success
        if (data_explain_status == "pending") {
          set(
            history.value[curQuestionIndex],
            "context.sqlData.data_explain.status",
            "success"
          );
        }
        // 拼接数据解读数据，更新历史
        dataExplainMsg += message?.choices[0]?.delta?.content;
        // 方案1：使用响应式克隆和整体替换
        const curHistory = ref<any>(cloneDeep(history.value[curQuestionIndex]));
        curHistory.value.context.sqlData.data_explain.data = dataExplainMsg;
        history.value[curQuestionIndex] = curHistory.value;
        // set(history.value[curQuestionIndex], "context.sqlData.data_explain.data", dataExplainMsg);
      },
      onDone: () => {
        // console.log("done", dataExplainMsg);
      },
      onError: (message: string) => {
        console.log(message, "message");
      },
    }),
  ]);
  // console.log(
  //   executeResult,
  //   volatilityAttributionData,
  //   dataExplain,
  //   dataExplainMsg,
  //   history.value,
  //   curQuestionIndex,
  //   "executeResult before"
  // );
  if (!!volatilityAttributionData && !!dataExplainMsg) {
    return {
      volatilityAttributionData,
      dataExplain: dataExplainMsg,
    };
  }
}
/**
 * 解析时间字符串，返回标准化的开始和结束时间
 * @param timeRange 时间范围数组 [startTime, endTime]
 * @returns 返回标准化的时间对象
 */
function parseTimeRange(timeRange: [string, string]): {
  start: { year: number; month: number; day: number };
  end: { year: number; month: number; day: number };
} {
  const [startStr, endStr] = timeRange;

  // 处理开始时间
  const startParts = startStr.split("-");
  const startYear = parseInt(startParts[0]);
  const startMonth = startParts.length > 1 ? parseInt(startParts[1]) : 1;
  const startDay = startParts.length > 2 ? parseInt(startParts[2]) : 1;

  // 处理结束时间
  const endParts = endStr.split("-");
  const endYear = parseInt(endParts[0]);
  const endMonth = endParts.length > 1 ? parseInt(endParts[1]) : 12;

  // 获取月份的最后一天
  const getLastDayOfMonth = (year: number, month: number) => {
    return new Date(year, month, 0).getDate();
  };

  const endDay =
    endParts.length > 2
      ? parseInt(endParts[2])
      : getLastDayOfMonth(endYear, endMonth);

  return {
    start: {
      year: startYear,
      month: startMonth,
      day: startDay,
    },
    end: {
      year: endYear,
      month: endMonth,
      day: endDay,
    },
  };
}

// 处理chat-data 助手下的历史对话
function handleChatDataHistory(
  query: string,
  historyItem: ChatHistoryResponse[number],
  data: Record<string, any>,
  round_index: string | number | unknown = null
) {
  // 获取当前ai 消息的 context 数据
  const { context } = cloneDeep(toRaw(historyItem)) as any;
  // 拼接成存储的格式
  let queryData: StoreChatDataHistoryRequest = {
    user_input: query,
    conv_uid: currentDialogue.value?.conv_uid || data.conv_uid,
    dialog_id: currentDialogue.value?.dialog_id || data.dialog_id,
    app_code: selectedAgent.value?.app_code as string,
    assistant_content: {
      "few-shot": context?.sqlData?.few_shot,
      schema: context?.intentAnalysisData?.metrics,
      "generate-sql": context?.sqlData?.renderSQL?.data,
      "amendment-sql": context?.sqlData?.lastExecutionSql?.data,
      "parse-intent": context?.intentAnalysisData,
      "sql-chat": context?.sqlData?.sqlFlowChart?.data,
      sql_edit: context?.sqlData?.sql_edit?.data,
      req_rec: context?.req_rec,
      attribution_analysis: context?.sqlData?.attribution_analysis?.data,
      data_explain: context?.sqlData?.data_explain?.data,
    },
    round_index,
  };
  // (!!round_index || (round_index as number) >= 0) && (queryData.round_index = round_index);
  queryData.assistant_content = JSON.stringify(queryData.assistant_content);
  console.log(JSON.parse(queryData.assistant_content), "queryData");
  // 保存历史
  addChatDataHistoryApi(queryData);
  // 保存记忆
  saveMemoryDetail({
    ...queryData,
    create_memory: true
  })
}

// 方法一：使用正则表达式（推荐）
const getQuesOpt = (content: string): string => {
  if (!content) return "";

  const match = content.match(/<ques_opt>(.*?)<\/ques_opt>/s);
  return match ? match[1].trim() : "";
};

// 处理chat data 返回的历史对话格式转换
function transformChatDataHistory(history: ChatHistoryResponse) {
  try {
    return history.map((item: any) => {
      // 只处理 ai 的 context 数据
      if (item.role === "ai") {
        let { context } = item;
        context = JSON.parse(context);
        let {
          "few-shot": few_shot,
          "generate-sql": renderSQL,
          "amendment-sql": lastExecutionSql,
          schema,
          "parse-intent": parseIntent,
          "sql-chat": sqlChat,
          req_rec: reqRec,
          sql_edit: sqlEdit,
          attribution_analysis: attributionAnalysis,
          data_explain: dataExplain,
        }: any = context || {};
        item.context = {
          intentAnalysisData: {
            metrics: schema,
            ...parseIntent,
          },
          renderType: "vnode",
          req_rec: reqRec,
          sqlData: {
            few_shot,
            renderSQL: {
              data: renderSQL,
              status: "success",
            },
            lastExecutionSql: {
              data: lastExecutionSql,
              status: "success",
            },
            sql_edit: {
              data: sqlEdit,
              status: "success",
            },
            attribution_analysis: {
              data: attributionAnalysis,
              status: "success",
            },
            data_explain: {
              data: dataExplain,
              status: "success",
            },
          },
        };
        !!sqlChat &&
          set(item.context, "sqlData.sqlFlowChart", {
            data: sqlChat,
            status: "success",
          });
      }
      return item;
    });
  } catch (error) {
    return history;
  }
}

// 删除单个历史对话记录
function delHistoryItem(item: any) {
  deleteHistoryApi({
    dialog_id: item.dialog_id,
    conv_uid: item.conv_uid,
  }).then(async (res) => {
    // 删除当前点击的
    if (
      !!currentDialogue?.value?.dialog_id &&
      currentDialogue?.value?.dialog_id === item?.dialog_id
    ) {
      currentDialogue.value = {} as IChatDialogueSchema;
      history.value = [];
    }
    getDialogueList();
  });
}

/**
 * 编辑sql后 重新触发sql 执行及更新历史、存储历史
 * @param item 新的历史对话数据
 * @param index 当前历史对话索引
 * @param originConfig 存储的配置数据，用于二次编辑使用
 */
async function resetSqlExecution(
  item: any,
  originConfig: any[],
  index: number
) {
  let { context, order } = toRaw(history.value[index]) as any;
  let {dataset_id, metrics = []} = context?.intentAnalysisData || {}
  // 缓存当前context, 失败时恢复
  let cacheContext = cloneDeep(context);
  try {
    // 重新设置sql编辑内的数据
    context.sqlData.sql_edit.data.where.conditions = [
      ...cloneDeep(item.conditions),
    ];
    context.sqlData.sql_edit.data.columns = [...cloneDeep(item.columns)];
    context.sqlData.sql_edit.data.groupBy = [...cloneDeep(item.groupBy)];
    context.sqlData.sql_edit.data.originConfig = [...cloneDeep(originConfig)];
    // 触发sql_edit 的更新
    context.sqlData.sql_edit = {
      ...context.sqlData.sql_edit,
    };
    // 重新设置sql 执行状态与数据、修订sql的执行状态和数据
    context.sqlData.sqlFlowChart.data = null;
    context.sqlData.sqlFlowChart.status = "pending";
    context.sqlData.lastExecutionSql.data = null;
    context.sqlData.lastExecutionSql.status = "pending";
    setReplyLoading(true); // 设置输入loading

    // 重新设置 历史记录, 触发更新
    history.value[index] = {
      ...history.value[index],
    };
    console.log("resetSqlExecution before", item, originConfig);
    // 根据编排的sql 重新生成sql数据
    let sqlEditData = get(context.sqlData.sql_edit, "data", {});
    let resetSqlData: any = await getSqlToJsonApi({
      action: "json2sql",
      db_type: "sql",
      option: { dialect: "mysql" },
      json_data: omit(sqlEditData, "originConfig"),
    });
    // 重新设置最后执行的sql
    set(context, "sqlData.lastExecutionSql", {
      data: resetSqlData?.data,
      status: "success",
    });
    console.log("resetSqlExecution after", history.value, resetSqlData);

    // 获取前一个用户数据
    let preHumenData: Record<string, any> = history.value[index - 1] || {};
    // 重新触发sql 执行
    const executeResult: any = await executeSqlApi({
      db_name: "budget",
      sql: resetSqlData?.data,
      user_input: preHumenData?.context,
      dataset_id: [dataset_id],
      field_id: metrics[0].split(",")[0] - 0,
    });
    // 更新sql 执行状态与数据
    set(context, "sqlData.sqlFlowChart", {
      data: { ...executeResult?.data, label: executeResult?.data?.label },
      // status: "success",
    });

    // 触发子组件内 loading 图形等重渲染
    history.value[index] = {
      ...history.value[index],
    };
    // 存储历史对话
    handleChatDataHistory(
      preHumenData?.context,
      history.value[index],
      {
        conv_uid: currentDialogue.value?.conv_uid || preHumenData?.conv_uid,
        dialog_id: currentDialogue.value?.dialog_id || preHumenData?.dialog_id,
      },
      order
    );
    // 更新历史
    // setHistory([...(history.value as any)]);
    setReplyLoading(false);
  } catch (error) {
    // 失败时 恢复数据
    history.value[index].context = { ...cacheContext };
    setReplyLoading(false);
    setHistory([...(history.value as any)]);
  }
}

// 全局方法，供 onclick 使用
(window as any).setInputValue = (value: string) => {
  // inputPanelRef.value.userInput = value;
  inputPanelRef.value.setInputValue(value);
};
function handleChatEvent() {
  // 添加用户消息到历史记录
}

// 设置函数
function setHistory(newHistory: ChatHistoryResponse) {
  history.value = newHistory;
}

function setReplyLoading(loading: boolean) {
  replyLoading.value = loading;
}

function setCanAbort(abort: boolean) {
  canAbort.value = abort;
}

function setAppInfo(info: IApp) {
  appInfo.value = info;
}

function setResourceValue(value: any) {
  resourceValue.value = value;
}

function refreshDialogList() {
  // 刷新对话列表的逻辑
  console.log("刷新对话列表");
}

function refreshHistory() {
  // 刷新历史记录的逻辑
  console.log("刷新历史记录");
}

function refreshAppInfo() {
  // 刷新应用信息的逻辑
  console.log("刷新应用信息");
}

// 获取字段列表
function getFieldList() {
  fieldList.value = [];
  getDatasourceTableFieldListApi(1).then((res: Record<string, any>) => {
    fieldList.value = res.data;
  });
}

// 提供数据给子组件
provide("history", history);
provide("selectedAgent", selectedAgent);
provide("replyLoading", replyLoading);
provide("canAbort", canAbort);
provide("handleChat", handleChat);
provide("appInfo", appInfo);
provide("currentDialogue", currentDialogue);
provide("temperatureValue", temperatureValue);
provide("maxNewTokensValue", maxNewTokensValue);
provide("resourceValue", resourceValue);
provide("setResourceValue", setResourceValue);
provide("refreshDialogList", refreshDialogList);
provide("scrollRef", scrollRef);
provide("resetSqlExecution", resetSqlExecution);
provide("fieldList", fieldList);
// 监听滚动引用变化
watch(scrollRef, (newRef) => {
  if (newRef?.scrollRef) {
    // [Vue warn]: provide() can only be used inside setup().但是注释掉可能影响滚动条
    provide("scrollRef", newRef.scrollRef);
  }
});

onMounted(() => {
  getFieldList();
  // 设置初始化助手
  const { initAgent = "" } = route.query;
  let params: Record<string, any> = initAgent
    ? {
        initAgent,
      }
    : {};
  getAgentList(params);
});
</script>

<template>
  <el-container class="chat-layout">
    <!-- 侧边栏 -->
    <el-aside :width="isExpand ? '260px' : '50px'" class="chat-sider">
      <div class="sider-content">
        <!-- 问答对话标题 -->
        <div class="sidebar-header">
          <h3 v-if="isExpand">问答对话</h3>
          <!-- <i :class="['iconfont', isExpand ? '' : 'radc-zhankaicaidan']"
            @click="handleToggleSidebar"></i> -->
          <el-icon class="iconfont" @click="handleToggleSidebar"
            ><Fold v-if="isExpand" /><Expand v-else
          /></el-icon>
        </div>

        <!-- 我的助手部分 -->
        <div class="my-agents">
          <div v-if="isExpand" class="agent-header">
            <span class="title">我的助手</span>
            <div class="agent-header-right">
              <el-popover
                placement="bottom-start"
                ref="agentSearchPopoverRef"
                :width="320"
                trigger="click"
                :show-arrow="false"
                @show="handleShow"
              >
                <template #reference>
                  <el-link :underline="false" style="font-size: 16px"
                    >
                    <i class="iconfont ChatData-sousuo"></i>
                  </el-link>
                </template>
                <div class="search-popover">
                  <el-input
                    ref="inputRef"
                    v-model="searchAgent"
                    style="width: 160px"
                    size="small"
                    placeholder="输入关键字..."
                    :prefix-icon="Search"
                    @keyup.enter="handleSearch"
                  />
                  <div class="search-actions">
                    <el-button type="primary" size="small" @click="handleSearch"
                      >搜索</el-button
                    >
                    <el-button size="small" @click="handleCancel"
                      >取消</el-button
                    >
                  </div>
                </div>
              </el-popover>
              <el-tooltip
                class="box-item"
                effect="dark"
                content="新增助手"
                placement="top"
              >
                <el-button
                  class="add-btn"
                  type="primary"
                  :icon="Plus"
                  circle
                  @click="onAddAgent()"
                />
              </el-tooltip>
              <el-tooltip
                content="新增对话"
                placement="top"
                popper-class="add-chat-icon-tooltip"
              >
                <svg class="add-chat-icon" size="4.2" @click="onAddNewDialogue">
                  <use href="#ChatData-duihua"></use>
                </svg>
              </el-tooltip>
            </div>
          </div>
          <div class="agent-list">
            <ClickItem
              v-for="agent in agentList"
              :key="agent.app_code"
              :icon="agent.icon || 'ChatData-AutoBa'"
              :name="agent.app_name"
              :show-star="agent.is_collected === 'true'"
              :active="selectedAgent?.app_code === agent.app_code"
              :only-show-icon="!isExpand"
              @click="() => handleSelectAgent(agent)"
            />

            <ClickItem
              icon="ChatData-faxian"
              name="发现更多助手"
              more-text="Ctrl+x"
              :show-arrow="true"
              :only-show-icon="!isExpand"
              @click="() => console.log('发现更多助手')"
            />
          </div>
        </div>

        <!-- 历史对话 -->
        <div class="history-section">
          <div :class="{ 'history-header': isExpand }" style="padding: 0 8px">
            <span class="title">历史对话</span>
            <div v-if="isExpand" class="agent-header-right">
              <el-link :underline="false" class="text-icon-btn">
                <i class="iconfont ChatData-shaixuan search-icon"></i>
              </el-link>
            </div>
          </div>

          <div v-if="isExpand" class="search-history">
            <el-input
              placeholder="输入关键字..."
              prefix-icon="Search"
              v-model="historyKeyword"
              @keyup.enter="getDialogueList"
              class="history-search"
            />
          </div>
          <div v-if="isExpand" class="history-list">
            <HistoryItem
              v-for="item in dialogueList"
              :key="item.conv_uid"
              :active="item.conv_uid === currentDialogue.conv_uid"
              :name="item.user_input"
              showDel
              @click="() => handleSelectDialogue(item)"
              @del="delHistoryItem(item)"
            />
          </div>
        </div>
      </div>
    </el-aside>
    <el-main class="main-layout">
      <div v-loading="historyLoading" class="chat-container">
        <!-- 聊天内容容器 -->
        <ChatContentContainer ref="scrollRef" />

        <!-- 输入面板 -->
        <ChatInputPanel :ctrl="ctrl" ref="inputPanelRef" />
      </div>
    </el-main>
  </el-container>
</template>

<style lang="scss" scoped>
.text-icon-btn {
  font-size: 16px;
  margin-right: 10px;
}

.chat-layout {
  height: 100%;
  background: transparent;

  .chat-sider {
    background: rgba(255, 255, 255, 0.95);
    border-right: 1px solid #e5e7eb;

    .dark & {
      background: rgba(0, 0, 0, 0.8);
      border-right-color: rgba(255, 255, 255, 0.1);
    }

    .sider-content {
      padding: 0 10px;
      height: 100%;
      background: #fcfcfc;

      .sidebar-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 48px;
        border-bottom: 1px solid #e4e7ed;
        padding: 0 8px;
        .iconfont {
          font-size: 20px;
          cursor: pointer;
        }

        h3 {
          font-size: 1.125rem;
          font-weight: 600;
          color: #1f2937;

          .dark & {
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }

      :deep(.my-agents) {
        padding-bottom: 10px;
        margin-bottom: 5px;
        border-bottom: 1px solid #e4e7ed;

        .agent-header {
          .add-chat-icon {
            cursor: pointer;
            margin-top: 2px;
          }
          // todo
        }

        .agent-list {
          // todo
        }
      }

      .history-section {
        .search-history {
          padding: 0 8px 8px;
        }
      }
    }
  }

  .agent-header,
  .history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 10px;
    height: 40px;

    .title {
      font-weight: 500;
      font-size: 14px;
      color: #4c6f88;
    }

    .agent-header-right {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .el-main.main-layout {
    padding: 0;
    overflow: hidden;
    background: transparent;
  }
}

.add-btn {
  width: 16px;
  height: 16px;
  background: #005ee0;
}

.dialogue-list {
  p {
    color: #6b7280;
    text-align: center;
    margin: 2rem 0;
  }
}

.chat-spin {
  height: 100%;

  :deep(.el-spin-container) {
    height: 100%;
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);

  .dark & {
    background: rgba(0, 0, 0, 0.8);
  }
  @media screen and (min-width: 3440px) {
    // 3K+
    padding: 0px 25vw;
    // max-width: 2400px;
    // font-size: 18px;
  }
}
</style>
<style lang="scss">
.search-popover {
  display: flex;
  align-items: center;
  gap: 10px;
}
</style>
