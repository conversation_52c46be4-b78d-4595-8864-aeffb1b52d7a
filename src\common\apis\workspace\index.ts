import { request } from "@/http/axios"
import type * as Agent from "./type"

/** 查询空间列表 */
export function getSapceListApi<T extends object>(data?: T) {
    return request({
        url: '/workspace/list',
        method: "get",
        version: "/api"
    })
}

// 创建空间
export function postSapceLApi(data: any) {
  return request({
    url: '/workspace/create',
    method: "post",
    data,
    version:'/api'
  })
}

// 获取空间详情
export function getSapceByIdApi(id: number) {
  return request({
    url: `/workspace/${id}`,
    method: "get",
    version:'/api'
  })
}

// 查询工作空间成员列表
export function getSapceMemberApi(id: number) {
  return request({
    url: `/workspace/${id}/members`,
    method: "get",
    version:'/api'
  })
}
// 添加成员
export function postSapceMemberLApi(id: number, data: any) {
  return request({
    url: `/workspace/${id}/members`,
    method: "post",
    data,
    version:'/api'
  })
}
// 设置默认空间
export function postSapceDefaultLApi(id: number,data: any) {
  return request({
    url: `/auth/user/default-workspace/${id}`,
    method: "post",
    data,
    version:'/api'
  })
}
// 取消默认空间
export function deleteSapceDefaultLApi() {
  return request({
    url: `/auth/user/default-workspace`,
    method: "delete",
    version:'/api'
  })
}

//转让工作空间
export function putSapceTransferLApi(id: number, data: any) {
  return request({
    url: `/workspace/${id}/transfer`,
    method: "put",
    data,
    version:'/api'
  })
}
//更改成员角色
export function putSapceMemberRoleLApi(id: number, data: any) {
  return request({
    url: `/workspace/${id}/members/batch_role`,
    method: "put",
    data,
    version:'/api'
  })
}
//更新工作空间
export function putSapceLApi(id: number, data: any) {
  return request({
    url: `/workspace/${id}`,
    method: "put",
    data,
    version:'/api'
  })
}
//删除工作空间
export function deleteSapceLApi(id: number) {
  return request({
    url: `/workspace/${id}`,
    method: "delete",
    version:'/api'
  })
}
//移除成员
export function deleteSapceMemberLApi(id: number, data: any) {
  return request({
    url: `/workspace/${id}/members/batch`,
    method: "delete",
    data,
    version:'/api'
  })
}
// 全部成员树
export function getUseTreeApi(data: any) {
  return request({
    url: `/workspace/user_tree`,
    method: "get",
    params:data,
    version:'/api'
  })
}
// 获取空间下的成员树
export function getWorkspaceUseTreeApi(data: any) {
  return request({
    url: `/workspace/workspace_user_tree`,
    method: "get",
    params:data,
    version:'/api'
  })
}