<template>
  <div class="code-container">
    <pre><code class="sql" v-html="highlightedCode"></code></pre>
    <el-button class="copy-btn" :icon="CopyDocument" circle @click="handleCopy" @mouseenter="copyTip = '复制'"
      @mouseleave="copyTip = ''" />
    <span class="copy-tip" v-if="copyTip">{{ copyTip }}</span>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import hljs from 'highlight.js/lib/core'
import sql from 'highlight.js/lib/languages/sql'
import 'highlight.js/styles/github.css'
import { CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

hljs.registerLanguage('sql', sql)

const props = withDefaults(defineProps<{
  code: string,
  lang?: string
}>(), {
  code: '',
  lang: 'sql'
})

const copyTip = ref('')

// SQL 美化函数简化版
function formatSQL(sql: string) {
  // 首先移除 SQL 代码块标记
  sql = (sql || '')
    .replace(/^```sql\s*/i, '')  // 移除开头的 ```sql
    .replace(/\s*```$/i, '');    // 移除结尾的 ```

  return sql
    .replace(/\s+/g, ' ')  // 合并多个空格
    .replace(/\s*SELECT\s+/gi, 'SELECT ')
    .replace(/\s*,\s*/g, '\n        , ')  // 逗号开头的行缩进8个空格
    .replace(/\s*FROM\s+/gi, '\nFROM ')
    .replace(/\s*WHERE\s+/gi, '\nWHERE ')
    .replace(/\s*GROUP BY\s+/gi, '\nGROUP BY ')
    .replace(/\s*HAVING\s+/gi, '\nHAVING ')
    .replace(/\s*ORDER BY\s+/gi, '\nORDER BY ')
    .replace(/\s*AND\s+/gi, '\n        AND ')  // AND 开头的行缩进8个空格
    .replace(/\s*OR\s+/gi, '\n        OR ')    // OR 开头的行缩进8个空格
    .replace(/\(\s*SELECT/gi, '(\n                SELECT')  // 子查询缩进16个空格
    .trim();
}

const highlightedCode = computed(() => {
  const formattedCode = formatSQL(props.code);
  return hljs.highlight(formattedCode, { language: props.lang }).value
})

const handleCopy = async () => {
  try {
    // 优先使用现代API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(props.code)
    } else {
      // 降级到传统方法
      fallbackCopyTextToClipboard(props.code)
    }
    ElMessage.success('复制成功')
    copyTip.value = '已复制!'
    setTimeout(() => {
      copyTip.value = ''
    }, 1500)
  } catch (err) {
    // 如果现代API失败，尝试降级方案
    try {
      fallbackCopyTextToClipboard(props.code)
      ElMessage.success('复制成功')
      copyTip.value = '已复制!'
      setTimeout(() => {
        copyTip.value = ''
      }, 1500)
    } catch (fallbackErr) {
      ElMessage.error('复制失败')
      copyTip.value = '复制失败'
    }
  }
}

// 降级复制方案
const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text

  // 避免在移动设备上弹出键盘
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  textArea.style.opacity = '0'
  textArea.style.pointerEvents = 'none'
  textArea.setAttribute('readonly', '')

  document.body.appendChild(textArea)

  // 兼容iOS
  if (navigator.userAgent.match(/ipad|iphone/i)) {
    const range = document.createRange()
    range.selectNodeContents(textArea)
    const selection = window.getSelection()
    selection?.removeAllRanges()
    selection?.addRange(range)
    textArea.setSelectionRange(0, 999999)
  } else {
    textArea.select()
  }

  const successful = document.execCommand('copy')
  document.body.removeChild(textArea)

  if (!successful) {
    throw new Error('降级复制方案失败')
  }
}
</script>

<style scoped>
.code-container {
  position: relative;
}

pre {
  padding: 16px;
  padding-right: 45px;
  border-radius: 8px;
  overflow-x: hidden;
  background: #ffffff;
  border: 1px solid #e1e8ed;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  white-space: pre-wrap;
  word-wrap: break-word;
  /* 增加行高，使代码更易读 */
  line-height: 1.6;
}

:deep(code) {
  font-family: 'Monaco', 'Consolas', monospace;
  font-size: 14px;
  color: #24292e;
  white-space: pre-wrap;
  word-break: break-all;
}



.copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 6px;
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e8ed;
  color: #586069;
  opacity: 0.8;
  transition: all 0.3s;

  &:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
    color: #24292e;
  }
}

.copy-tip {
  position: absolute;
  top: 8px;
  right: 45px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s;
}
</style>
