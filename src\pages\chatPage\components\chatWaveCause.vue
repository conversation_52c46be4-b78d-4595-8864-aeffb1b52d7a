<template>
  <p class="wave-cause-title">{{ props.label }}</p>
  <!-- 波动归因计算 -->
  <div class="calculate-container" v-if="calculateData">
    <div class="data-diff">
      <div class="pre-data">
        <span class="time-label">{{ timeRange[0] }}</span>
        <span class="count-label">{{ calculateData.from_value }}</span>
      </div>
      <i
        :class="[
          `diff-icon`,
          `iconfont`,
          calculateData.growth_rate > 0
            ? 'ChatData-shangshengqushi'
            : 'ChatData-xiajiangqushi',
          calculateData.growth_rate > 0 ? 'up-trend' : 'down-trend',
        ]"
      ></i>
      <div class="cur-data">
        <span class="time-label">{{ timeRange[1] }}</span>
        <span class="count-label">{{ calculateData.to_value }}</span>
      </div>
    </div>
    <div class="diff-precent">
      <el-button type="success" plain size="small" style="width: 250px">
        {{ calculateData.difference }}
        <span v-if="calculateData.growth_rate">
          (同比{{ calculateData.growth_rate }}%)</span
        >
      </el-button>
    </div>
  </div>
  <p class="wave-cause-title">{{ props.label }}</p>
  <!-- 按维度 展示、排序 -->
  <el-radio-group
    v-model="groupBySelect"
    size="small"
    fill="#005EE0"
    @change="handleDimensionsByChange"
  >
    <el-radio-button
      :label="item.field_id"
      v-for="(item, index) in flowChartData?.dimensions.slice(0, 5) || []"
      :key="item.field_id"
    >
      {{`${index + 1}.${item.alias || item.field}`}}
    </el-radio-button>
  </el-radio-group>
  <div v-if="!!props.data?.rank_data[groupBySelect]">
    <div
      class="waveCauseDiagram"
      ref="waveCauseDiagram"
      :style="{
        width: '100%',
        height: `${totalHeight + 40}px`,
        position: 'relative', // 添加相对定位
        marginTop: '15px',
      }"
    ></div>
  </div>
</template>
<script setup lang="ts">
import { ref, defineProps, computed } from "vue";
import * as echarts from "echarts";
const groupBySelect = ref<any>("");

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  flowChartData: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  attributionAnalysisData: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
  label: {
    type: String,
    default: "",
  },
});
// 获取x单位
const xUnit = computed(() => {
  return props.flowChartData["x-unit"] || "";
});

// 获取y单位
const yUnit = computed(() => {
  return props.flowChartData?.data
    ? props.flowChartData["y-unit"]?.split(",")
    : [];
});

// 获取x轴别名
const xAxisAlias = computed(() => {
  return props.flowChartData["x-title"].split("-");
});

// 获取y轴别名
const yAxisAlias = computed(() => {
  return props.flowChartData["y-title"].split(",");
});

// 获取时间区间
const timeRange = computed(() => {
  return props.flowChartData?.["time-range"] || [];
});

// 获取总高度
const totalHeight = computed(() => {
  const categoryHeight = 40 * (yAxisAlias.value.length || 1); // 每个类目的固定高度
  return (
    (props.data?.rank_data[groupBySelect.value].length || 1) * categoryHeight
  );
});

// 获取波动归因计算数据
const calculateData = computed(() => {
  return props.data?.calculate_data || null;
});

// 波动归因图表实例
const waveCauseDiagram = ref<any>(null);
// ------------------------

// 点击切换显示
const handleDimensionsByChange = (value: string) => {
  // console.log("value", value);
  // groupBySelect.value = value;
  toggleChart(groupBySelect.value);
};

function convertToEChartsOption(data: any[]) {
  //   debugger;

  let yAxisData = data.reduce((acc, cur) => {
    let labelStr = "";
    [...xAxisAlias.value].forEach((xKey, index) => {
      labelStr += `${cur[xKey] || cur.x || ""}${
        !!index && index !== xAxisAlias.value.length - 1 ? "-" : ""
      }`;
    });
    acc.push(labelStr);
    return acc;
  }, []);

  console.log(
    data,
    xAxisAlias.value,
    yAxisAlias.value,
    yUnit.value,
    xUnit.value,
    "data"
  );

  return {
    title: {
      //   text: props.label,
    },
    tooltip: {
      trigger: "item",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      show: false,
      bottom: "0%",
    },
    grid: {
      left: "3%",
      right: "10%",
      bottom: "3%",
      top: 15,
      containLabel: true,
      height: totalHeight.value, // 设置固定高度
    },
    xAxis: {
      type: "value",
      show: false,
      //   boundaryGap: [0, 0.01],
    },
    yAxis: {
      type: "category",
      inverse: true,
      //   name: xUnit.value,
      //   nameLocation: "start",
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisLabel: {
        interval: 0,
        height: 16, // 控制标签高度
      },
      data: yAxisData,
      height: data.length * 20, // 控制整个y轴的高度，每个类目20px
    },
    series: yAxisAlias.value.reduce((acc, cur, index) => {
      acc.push({
        name: cur,
        type: "bar",
        barMaxWidth: 16, // 使用固定宽度而不是最大宽度
        barGap: 10, // 同组柱子之间的间距
        barCategoryGap: 4, // 类目之间的间距
        realtimeSort: true,
        label: {
          show: true,
          position: "right",
          formatter: (params: any) => {
            return `${params.value}${yUnit.value[index]}`;
          },
        },
        itemStyle: {
          color: "#005EE0",
          borderRadius: 4,
          margin: 10,
        },
        // showBackground: true,
        data: data.map((item) => item[cur]),
      });
      return acc;
    }, []),
  };
}

function toggleChart(chartType?: string) {
  // 无类型或无获取时不处理
  if (!groupBySelect.value) return;

  // 初始化echarts实例
  nextTick(() => {
    // 如果已存在实例，先销毁
    if (waveCauseDiagram?.value?.dispose) {
      waveCauseDiagram.value.dispose();
    }
    // 重新初始化实例
    !waveCauseDiagram?.value?.dispose &&
      (waveCauseDiagram.value = echarts.init(waveCauseDiagram.value));
    let diagramData = props.data?.rank_data[groupBySelect.value] || [];
    try {
      waveCauseDiagram.value.setOption(convertToEChartsOption(diagramData));
    } catch (error) {
      // console.log(error, "error");
    }
  });
}

function initDiagram() {
  if (props.flowChartData.dimensions.length > 0 && !!props.data?.rank_data) {
    groupBySelect.value = props.flowChartData.dimensions[0].field_id;
    toggleChart();
  }
}

watch([() => props.flowChartData, () => props.data?.rank_data], () => {
  initDiagram();
}, { immediate: true, deep: true , flush: 'post'});
</script>

<style lang="scss" scoped>
.wave-cause-title {
  font-weight: 600;
  font-size: 13px;
  color: #262626;
  margin: 12px 0px;
  &::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
    background-color: #4c6f88;
  }
}
.calculate-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
  .data-diff {
    display: flex;
    align-items: end;
    gap: 30px;
    .diff-icon {
      font-size: 33px;
      line-height: 1;
    }
    .up-trend {
      background: linear-gradient(
        141deg,
        rgba(255, 215, 218, 1) 0%,
        #fd033b 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: #fd033b; /* 降级方案 */
    }
    .down-trend {
      background: linear-gradient(
        141deg,
        rgba(173, 233, 205, 1) 0%,
        // #ADE9CD
        #0dbf5b 100%
      );
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: #0dbf5b; /* 降级方案，用于不支持渐变的浏览器 */
    }
    .pre-data,
    .cur-data {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .time-label {
        font-size: 13px;
        color: #8c8c8c;
        line-height: 18px;
      }
      .count-label {
        font-size: 20px;
        color: #262626;
        line-height: 25px;
      }
    }
  }
}
</style>
