<template>
  <div class="pre-label-select">
    <span class="label">
      <slot name="label">
        {{ label }}
      </slot>
    </span>
    <el-select
      class="select-box"
      v-model="value"
      placeholder="请选择"
      v-bind="$attrs"
    >
      <slot>
        <el-option
          v-for="option in options"
          :key="option.value"
          :label="option.label"
          :value="option.value"
        />
      </slot>
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { defineModel, defineProps, watchEffect } from "vue";
import { cloneDeep } from "lodash-es";
const props = defineProps({
  options: {
    type: Array<any>,
    default: () => [],
  },
  label: {
    type: String,
    default: "",
  },
});
let value = defineModel<string>("value");
// 显示使用的实际options
let visOptions = ref<any[]>([]);

watchEffect(() => {
  visOptions.value = cloneDeep(props.options);
});

</script>

<style lang="scss" scoped>
$cm-font-size: 13px;
.pre-label-select {
  display: flex;
  align-items: center;
//   gap: 10px;
  box-shadow:  0 0 1px #dcdfe6 inset;
  border-radius: 4px;
  background: #fff;
  padding: 0 10px;
  height: 32px;
  border: 1px solid #dcdfe6;
  font-size: $cm-font-size;
  &:hover {
    border: 1px solid #c0c4cc;
  }
  .label {
    font-size: $cm-font-size;
    color: #8c8c8c;
    line-height: 16px;
    text-wrap: nowrap;
  }
  :deep(.select-box) {
    min-width: 150px;
    .el-select__wrapper {
        box-shadow: none;
        border-radius: 4px;
        background: transparent;
        padding: 0px;
        font-size: $cm-font-size;
    }
  }
}
</style>

