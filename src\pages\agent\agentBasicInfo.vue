<template>
  <div class="agent-basic-info">
    <div class="title-symbol">基础信息</div>
    <el-form
      ref="formRef"
      :model="basicForm"
      :rules="rules"
      label-width="auto"
      label-position="top"
      class="basic-info-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="助手名称" prop="app_name">
            <el-input
              v-model="basicForm.app_name"
              placeholder="请输入助手名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="助手类型" prop="app_type">
            <el-select
              v-model="basicForm.app_type"
              placeholder="请选择助手类型"
            >
              <el-option
                v-for="item in appList"
                :key="item.chat_scene"
                :label="item.scene_name"
                :value="item.chat_scene"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="知识库" prop="knowledgeBase">
            <el-select
              v-model="basicForm.knowledgeBase"
              multiple
              placeholder="请选择知识库，支持多选"
            >
              <el-option
                v-for="item in knowledgeBaseList"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据集" prop="dataset">
            <el-tree-select
              multiple
              value-key="id"
              :props="{
                label: 'name',
                value: 'id',
                emitPath: false,
                children: 'children',
                class: (node) => node.children && !node.children.length ? 'has-disabled' : ''
              }"
              v-model="basicForm.dataset"
              :data="datasetList"
              :render-after-expand="false"
              show-checkbox
              clearable
            >
            </el-tree-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="温度" prop="temperature">
            <template #label>
              <span class="flex-center">
                <span>温度</span>
                <el-tooltip
                  content="核采样阈值0~1。用于决定结果随机性，取值越高随机性越强即相同的问题得到的不同答案的可能性越高。"
                  placement="top"
                >
                  <i class="iconfont ChatData-zhushi tip-icon"></i>
                </el-tooltip>
              </span>
            </template>
            <el-input
              v-model.number="basicForm.temperature"
              placeholder="请输入temperature值"
              type="number"
              step="0.1"
            >
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="最大输出token数">
            <template #label>
              <div class="flex-center">
                <span>最大输出token数</span>
                <el-tooltip
                  content="最大输入Token 模型回答的tokens的最大长度"
                  placement="top"
                >
                  <i class="iconfont ChatData-zhushi tip-icon"></i>
                </el-tooltip>
              </div>
            </template>
            <el-input
              v-model.number="basicForm.max_tokens"
              placeholder="请输入最大输出token数"
              type="number"
              step="1"
            >
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="描述">
        <el-input
          v-model="basicForm.app_describe"
          type="textarea"
          placeholder="定义助手描述，以便快速理解"
        />
      </el-form-item>
      <el-form-item label="示例问题" class="example-question-form">
        <div
          v-for="(item, index) in basicForm.recommend_questions"
          :key="index"
          class="question-item"
        >
          <el-input
            v-model="item.question"
            placeholder="请输入示例问题"
            style="width: 100%"
            class="question-input"
          />
          <i
            class="iconfont ChatData-shanchu del-icon"
            @click="removeQuestion(index)"
          ></i>
        </div>

        <el-button
          type="primary"
          plain
          :icon="Plus"
          style="width: 100%"
          @click="addExampleQuestionItem"
        >
          新增一行
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import { ref, defineExpose } from "vue";
import type { FormRules, FormInstance } from "element-plus";
import {
  getApplicationTypeApi,
  getKnowledgeBaseListApi,
  getDatasetListApi,
} from "@/common/apis/agent";
import type { needSpaceCode } from "@/common/apis/agent/type";
import type { ApplicationType } from "./type";
import { get } from "lodash-es";
import { ElTreeSelect } from 'element-plus'
import { useRoute } from "vue-router";
interface questionItem {
  question: string;
  valid: boolean;
}
// 定义基础数据类型
interface BasicForm {
  // 必填字段
  app_type: string;
  knowledgeBase: string[];
  dataset: string[];
  app_name: string;
  // 可选字段
  temperature: number;
  max_tokens: number;
  app_describe: string;
  recommend_questions: questionItem[];
}

// ref
const formRef = ref<FormInstance>();
const route = useRoute();

// 表单验证规则
const rules = ref<FormRules>({
  app_name: [{ required: true, message: "请输入助手名称" }],
  app_type: [{ required: true, message: "请选择助手类型" }],
  knowledgeBase: [
    { required: true, message: "请选择知识库" },
    { type: "array", min: 1, message: "至少选择一个知识库" },
  ],
  dataset: [
    { required: true, message: "请选择数据集" },
    { type: "array", min: 1, message: "至少选择一个数据集" },
  ],
  temperature: [
    // { required: true, message: "请输入温度" },
    { type: "number", min: 0, max: 1, message: "温度值必须在0到1之间" },
  ],
});

// 表单数据
const initBasicForm = ref<BasicForm>({
  app_type: "",
  app_name: "",
  knowledgeBase: [],
  dataset: [],
  temperature: 0.7,
  max_tokens: 2000,
  app_describe: "",
  recommend_questions: [
    {
      question: "",
      valid: true,
    },
  ],
});
const basicForm = ref<BasicForm>({ ...initBasicForm.value });
const appList = ref<ApplicationType[]>([]);
const knowledgeBaseList = ref<any[]>([]);
const datasetList = ref<any[]>([]);
// 表单验证方法
const validateForm = async () => {
  if (!formRef.value) return false;
  try {
    await formRef.value.validate();
    return true;
  } catch (error) {
    return false;
  }
};

// 重置表单
const resetForm = (params: any = {}) => {
  if (!formRef.value) return;
  formRef.value.resetFields();
  // 重置表单值，根据传入的数据获取或初始默认值
  for (let key in initBasicForm.value) {
    basicForm.value[key] = params[key] || initBasicForm.value[key];
  }
};

// 获取表单数据
const getData = (): BasicForm => {
  return {
    ...basicForm.value,
  };
};

// 获取知识库列表
const getKnowledgeBaseList = async (params: Record<string, any> = {}) => {
  const res = await getKnowledgeBaseListApi({
    page: 1,
    page_size: 9999,
    ...params,
  });
  knowledgeBaseList.value = get(res, "data", []);
  // console.log(knowledgeBaseList.value, 888);
};

// 获取数据集列表
const getDatasetList = async (params: Record<string, any> = {}) => {
  const res = await getDatasetListApi({
    workspace_id: route.query.spaceCode || '',
    ...params
  });
  datasetList.value = get(res, "data", []);
};

// 获取应用类型
const getApplicationType = () => {
  appList.value = [];
  getApplicationTypeApi().then((res: any) => {
    appList.value = get(res, "data", []);
  });
};

// 添加示例问题
const addExampleQuestionItem = () => {
  basicForm.value.recommend_questions.push({
    question: "",
    valid: true,
  });
};

// 删除示例问题
const removeQuestion = (index: number) => {
  // if (basicForm.value.recommend_questions.length > 1) {
  basicForm.value.recommend_questions.splice(index, 1);
  // }
};

// 初始获取数据
getKnowledgeBaseList();
getApplicationType();
getDatasetList();

// 抛出方法
defineExpose({
  basicForm,
  validateForm,
  resetForm,
  getData,
});
</script>
<style lang="scss" scoped>
.title-symbol {
  border-left: 3px solid#005EE0;
  font-weight: bold;
  font-size: 14px;
  color: #262626;
  padding: 0px 10px;
}

.basic-info-form {
  margin-top: 15px;
}
.question-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}
.example-question-form {
  // display: flex;
  // align-items: center;
  // gap: 10px;
  .question-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    width: 100%;
    .question-input {
      flex: 1 1 auto;
    }
  }
}
.del-icon {
  cursor: pointer;
  color: #005ee0;
  font-size: 13px;
}
.tip-icon {
  font-size: 13px;
  color: #005ee0;

  cursor: pointer;
}
.flex-center {
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: flex-start !important;
}
</style>
<style>
.has-disabled {
  pointer-events: none;
}
</style>
