<template>
  <div class="field-list" v-loading="loading">
    <div class="field-list-header">
      <div class="field-list-title">字段列表</div>
      <div class="field-list-actions">
        <el-input
          class="field-search gray-inp-bg"
          placeholder="输入关键字…"
          :prefix-icon="Search"
          v-model="search"
          clearable
          @keyup.enter="handleFiilterSearch"
        />
        <el-button type="text" class="meta-btn" @click="openFieldMetaDrawer"
          >字段元信息</el-button
        >
        <!-- <el-button type="text" class="download-btn">
          <el-icon><Download /></el-icon>
        </el-button> -->
      </div>
    </div>
    <div :gutter="20" class="field-list-content">
      <!-- 维度树 -->
      <!-- <el-col :span="12" class="dimension-content"> -->
      <el-card
        class="field-card dimension-content"
        shadow="never"
        draggable
        field-type="Dimension"
        @dragover="handleDragEnter"
      >
        <div class="field-title flex-row">
          <span class="flex1">维度</span>
          <span
            class="text-btn"
            @click="onComputedField('dimension')"
            v-if="!isView"
          >
            <i class="iconfont ChatData-jiahao"></i> 计算维度
          </span>
        </div>
        <div class="field-tree">
          <el-tree
            ref="dimensionTreeRef"
            :data="cachedDimension"
            v-if="!!cachedDimension.length"
            :props="treeProps"
            default-expand-all
            node-key="id"
            highlight-current
            :draggable="!isView"
            :allow-drag="allowDrag"
            :allow-drop="allowDrop"
            @node-drag-start="handleDragStart"
            @node-drag-end="handleDragEnd"
            :filter-node-method="filterNode"
          >
            <template #default="{ data, node }">
              <!-- 目录节点 -->
              <div v-if="node.level === 1" class="field-catalog">
                <span class="field-label">{{ data.label }}</span>
                <!-- 层级显示编辑 -->
                <span
                  class="tools"
                  v-if="data.display_type === 'layer' && !isView"
                >
                  <i
                    class="iconfont ChatData-bianji"
                    @click="handleMaintainLayer('edit', data.label, data)"
                  ></i>
                  <i
                    class="iconfont ChatData-shanchu"
                    @click="handleRemoveLayer(data)"
                  ></i>
                </span>
              </div>
              <!-- 叶子节点 -->
              <div v-else class="field-leaf">
                <span class="field-icon">
                  <el-icon v-if="data.data_type === 'Datetime'"
                    ><Calendar
                  /></el-icon>
                  <el-icon v-else-if="data.data_type === 'String'"
                    ><Coin
                  /></el-icon>
                  <i
                    class="iconfont ChatData-weidu"
                    v-else
                    style="font-size: 10px"
                  ></i>
                </span>
                <span class="field-label">
                  <el-tooltip :content="`原名:${data.alias || data.field}`">
                    <div :class="data.ignore ? 'ignore-field' : ''">
                      {{ data.label }}
                    </div>
                  </el-tooltip>
                </span>
                <span class="tools" v-if="!isView">
                  <el-icon
                    v-if="!data.ignore"
                    @click="data.ignore = !data.ignore"
                  >
                    <el-tooltip content="忽略列">
                      <View />
                    </el-tooltip>
                  </el-icon>
                  <el-icon v-else @click="data.ignore = !data.ignore"
                    ><el-tooltip content="已忽略">
                      <Hide />
                    </el-tooltip>
                  </el-icon>
                  <el-dropdown
                    trigger="hover"
                    :hide-on-click="false"
                    @command="(cmd) => leftToolCommand(cmd, data)"
                  >
                    <span class="el-dropdown-link">
                      <el-icon class="el-icon--right"><MoreFilled /></el-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="addLayer">
                          新增层级
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <el-popover
                            title="加入层级"
                            width="200"
                            placement="left"
                            @show="getLayerList(node?.parent?.data?.id)"
                          >
                            <div
                              class="layer-item"
                              v-for="item in layerList"
                              :key="item.id"
                            >
                              <span
                                class="layer-label"
                                @click="handleSelectLayer(data, item)"
                                >{{ item.label }}</span
                              >
                              <!-- 选中显示 -->
                              <el-icon
                                class="layer-icon"
                                v-if="selectedLayer?.id === node.id"
                                ><Select
                              /></el-icon>
                            </div>
                            <template #reference>
                              <span>加入层级</span>
                            </template>
                          </el-popover>
                        </el-dropdown-item>
                        <el-dropdown-item
                          command="removeLayer"
                          v-if="data.display_type === 'layer'"
                        >
                          移出层级
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="data.display_type === 'custom'"
                          command="edit"
                        >
                          <span>编辑</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </el-card>
      <!-- </el-col> -->

      <!-- 度量树 -->
      <!-- <el-col :span="12" class="measure-content"> -->
      <el-card
        class="field-card measure-content"
        shadow="never"
        draggable
        field-type="Measure"
        @dragenter="handleDragEnter"
      >
        <div class="field-title flex-row">
          <span class="flex1">度量</span>
          <span
            class="text-btn"
            @click="onComputedField('measure')"
            v-if="!isView"
          >
            <i class="iconfont ChatData-jiahao"></i> 计算度量
          </span>
        </div>
        <div class="field-tree">
          <el-tree
            ref="measureTreeRef"
            id="measure-tree"
            :data="cachedMeasure"
            :props="treeProps"
            v-if="!!cachedMeasure.length"
            default-expand-all
            node-key="id"
            highlight-current
            :draggable="!isView"
            :allow-drag="allowDrag"
            :allow-drop="allowDrop"
            @node-drag-start="handleDragStart"
            @node-drag-end="handleDragEnd"
            :filter-node-method="filterNode"
          >
            <template #default="{ data, node }">
              <!-- 目录节点 -->
              <div v-if="node.level === 1" class="field-catalog">
                <span class="field-label">{{ data.label }}</span>
              </div>
              <div v-else class="field-leaf">
                <span class="field-icon">
                  <span class="field-icon">
                    <el-icon v-if="data.data_type === 'Datetime'"
                      ><Calendar
                    /></el-icon>
                    <el-icon v-else-if="data.data_type === 'String'"
                      ><Coin
                    /></el-icon>
                    <i
                      class="iconfont ChatData-weidu"
                      v-else
                      style="font-size: 10px"
                    ></i>
                  </span>
                </span>
                <span class="field-label">
                  <el-tooltip :content="`原名:${data.alias || data.field}`">
                    <div :class="data.ignore ? 'ignore-field' : ''">
                      {{ data.label }}
                    </div>
                  </el-tooltip>
                </span>
                <span class="tools" v-if="!isView">
                  <el-icon
                    v-if="!data.ignore"
                    @click="data.ignore = !data.ignore"
                  >
                    <el-tooltip content="忽略列">
                      <View />
                    </el-tooltip>
                  </el-icon>
                  <el-icon v-else @click="data.ignore = !data.ignore"
                    ><el-tooltip content="已忽略">
                      <Hide />
                    </el-tooltip>
                  </el-icon>
                  <el-dropdown
                    trigger="hover"
                    :hide-on-click="false"
                    v-if="data.display_type === 'custom'"
                    @command="(cmd) => leftToolCommand(cmd, data)"
                  >
                    <span class="el-dropdown-link">
                      <el-icon class="el-icon--right"><MoreFilled /></el-icon>
                    </span>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-if="data.display_type === 'custom'"
                          command="edit"
                        >
                          <span>编辑</span>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </span>
              </div>
            </template>
          </el-tree>
        </div>
      </el-card>
      <!-- </el-col> -->
    </div>
    <FieldMetaDrawer
      v-model:visible="metaDrawerVisible"
      :data="fieldMetaList"
      :type="props.editType"
      @save="handleSaveFieldMeta"
    />
    <computeFieldDrawer
      ref="computeFieldDrawerRef"
      :dimensionList="calculatedDimension"
      :measureList="calculatedMeasure"
      @confirm="handleComputedFieldConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { defineProps, watch, ref, nextTick } from "vue";
import FieldMetaDrawer from "./FieldMetaDrawer.vue";
import { Search } from "@element-plus/icons-vue";
import computeFieldDrawer from "./computeFieldDrawer.vue";
import {
  View,
  Hide,
  MoreFilled,
  Select,
  Coin,
  Calendar,
} from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";
import { v4 as uuidv4 } from "uuid";
import { getDatasourceTableFieldListApi } from "@/common/apis/llm";

const metaDrawerVisible = ref(false);
const props = defineProps({
  dimension: {
    type: Array as () => Array<{
      id: number;
      label: string;
      icon?: string;
      children?: any[];
    }>,
    default: () => [],
  },
  measure: {
    type: Array as () => Array<{
      id: number;
      label: string;
      icon?: string;
      children?: any[];
    }>,
    default: () => [],
  },
  editType: {
    type: String,
    default: "add",
  },
});
const search = ref<string>("");
const loading = ref(false);

// 维度 度量数据 本地编辑缓存，避免频繁更新
const cachedDimension = ref<any[]>([]);
const cachedMeasure = ref<any[]>([]);
watch(
  [() => props.dimension, () => props.measure],
  (newVal) => {
    const [dimension = [], measure = []] = newVal;
    cachedDimension.value = cloneDeep(dimension);
    cachedMeasure.value = cloneDeep(measure);
  },
  {
    deep: true,
    immediate: true,
    flush: "post",
  }
);

// 分层list
const layerList = ref<any[]>([]);
// 选中的分层
const selectedLayer = ref<any>(null);
// 当前要计算的维度 | 度量
const currentComputedType = ref<string | null>(null);

const treeProps = {
  children: "children",
  label: "label",
};

// 计算维度 度量 抽屉 ref
const computeFieldDrawerRef = ref(null);

// 记录当前拖拽的节点来源
const currentToArea = ref<string | null>(null);
// 维度树 ref
const dimensionTreeRef = ref();
// 度量树 ref
const measureTreeRef = ref();

// 计算维度list
const calculatedDimension = computed(() => {
  return cachedDimension.value.filter((item) => item.display_type === "table");
});
// 计算度量list
const calculatedMeasure = computed(() => {
  return cachedMeasure.value.filter((item) => item.display_type === "table");
});

// 是否为view试图
const isView = computed(() => {
  return props.editType === "view";
});

// 获取模型数据
const curentEditModelData = inject<any>("curentEditModelData");
// 字段元信息列表
const fieldMetaList = computed(() => {
  let newFieldMeta = {};
  // 先将维度数据 生成基础表结构
  cachedDimension.value.reduce((acc, item) => {
    if (item.display_type === "table") {
      item.children = item.children.map((child) => {
        child.belong_uId = item.id;
        return child;
      });
      !acc[item.table_id] && (acc[item.table_id] = {});
      if (acc[item.table_id]) {
        acc[item.table_id] = { ...item };
      }
    }
    return acc;
  }, newFieldMeta);
  cachedMeasure.value.forEach((item) => {
    if (item.display_type === "table") {
      item.children = item.children.map((child) => {
        child.belong_uId = item.id;
        return child;
      });
      !newFieldMeta[item.table_id] && (newFieldMeta[item.table_id] = {});
      if (newFieldMeta[item.table_id]) {
        newFieldMeta[item.table_id].children = [
          ...(newFieldMeta[item.table_id].children || []),
          ...(item.children || []),
        ];
      }
    }
  });
  // console.log(newFieldMeta, "newFieldMeta");
  return Object.values(newFieldMeta);
});

// --------------------------------------------------

// 搜索
function handleFiilterSearch(value) {
  dimensionTreeRef.value.filter(value);
  measureTreeRef.value.filter(value);
}

// 定义树节点过滤方法
function filterNode(value, data) {
  let inpVal = search.value;
  if (!inpVal) return true;
  let label = data.label || data.alias || data.field;
  return label.indexOf(inpVal) !== -1;
}

// 保存字段元信息
function handleSaveFieldMeta(value) {
  let { dimensionData = [], measureData = [] }: any = value;
  // 重新拼接数据
  cachedDimension.value = [
    ...dimensionData,
    ...cachedDimension.value.filter((item) => item.display_type !== "table"),
  ];
  cachedMeasure.value = [
    ...measureData,
    ...cachedMeasure.value.filter((item) => item.display_type !== "table"),
  ];
}

// 打开字段元信息抽屉
function openFieldMetaDrawer() {
  console.log(fieldMetaList.value, "open meta drawer");
  metaDrawerVisible.value = true;
}

// 选中要跳转的分层 进行跳转
function handleSelectLayer(source, target) {
  selectedLayer.value = target;
  // 删除原分层位置下的节点
  dimensionTreeRef.value.remove(source);
  // 选中分层加节点
  dimensionTreeRef.value.append(
    {
      ...source,
      display_type: "layer",
      display_name: target.label,
      display_id: target.id,
    },
    target.id
  );

  // console.log(source, target, cachedDimension.value, "move layer");
}

// 获取分层列表
function getLayerList(filterId?: string) {
  layerList.value = [];
  selectedLayer.value = null;
  layerList.value = cachedDimension.value.filter(
    (item: any) => item.display_type === "layer" && item.id !== filterId
  );
}

// leaf左侧工具栏 menu
function leftToolCommand(command: string, data: any) {
  if (command === "addLayer") {
    handleMaintainLayer("add", "", data);
  } else if (command === "edit") {
    onComputedField(
      data.role === "Dimension" ? "dimension" : "measure",
      "edit",
      data
    );
  } else if (command === "removeLayer") {
    // console.log(data, "remove layer");
    // debugger
    let pNode = dimensionTreeRef.value.getNode(data.table_id);
    dimensionTreeRef.value.remove(data.id);
    pNode &&
      dimensionTreeRef.value.append(
        {
          ...data,
          display_type: "table",
          display_name: data.display_name,
          display_id: data.id,
        },
        pNode
      );
  }
}

// 新增层级  编辑 | 新增
function handleMaintainLayer(
  type: string = "add",
  initialValue?: string,
  editItem?: any
) {
  ElMessageBox.prompt("层级名称", `${type === "add" ? "新增" : "编辑"}层级`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputPattern: /^(?=.*[\p{Script=Han}a-zA-Z0-9])[\s\S]+$/u,
    inputValue: initialValue,
    inputErrorMessage: "名称不能为空",
  })
    .then(({ value }) => {
      // 新增
      if (type === "add") {
        let newUid = uuidv4();
        let lastestTableIndex = (cachedDimension.value as any).findLast(
          (item) => item.display_type === "table"
        );
        // 在最后一个表格后插入
        dimensionTreeRef.value.insertAfter(
          {
            id: newUid,
            display_id: newUid,
            display_name: value,
            label: value,
            children: [],
            display_type: "layer",
          },
          lastestTableIndex
        );
        // 新增层级默认 在新增层级后移入到新增层级
        nextTick(() => {
          let newLayerNode = dimensionTreeRef.value.getNode(newUid);
          let itemOriginParent = dimensionTreeRef.value.getNode(
            editItem.belong_uId
          );
          // 先删除原来中的位置 再添加到新的位置
          if (newLayerNode && itemOriginParent) {
            dimensionTreeRef.value.remove(editItem);
            dimensionTreeRef.value.append(
              {
                ...editItem,
                display_id: newUid,
                display_name: value,
                display_order: 0,
                display_type: "layer",
              },
              newLayerNode
            );
          }
        });
      } else if (type === "edit" && editItem) {
        // 编辑
        editItem.label = value;
      }

      // console.log(cachedDimension.value, "maintain layer");
    })
    .catch(() => {});
}

// 删除层级
function handleRemoveLayer(item) {
  // debugger
  ElMessageBox.confirm("确定删除该层级吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
  })
    .then(() => {
      if (!!item.id) {
        // 有子级的情况下， 将子级迁移回其他表内
        if (item.children.length) {
          let children = cloneDeep(item.children);
          children.forEach((child) => {
            if (child.table_id) {
              // 删除分层后，需要根据子级上一次编排原始id 进行还原迁移
              let pNode = dimensionTreeRef.value.getNode(child.belong_uId);
              pNode && dimensionTreeRef.value.append(child, pNode);
            }
          });
        }
        dimensionTreeRef.value.remove(item.id);
      }
    })
    .catch(() => {});
}

// 打开字段元
function onComputedField(
  type: string = "",
  editType: string = "add",
  editData: any = {}
) {
  currentComputedType.value = type;
  if (!!type) {
    computeFieldDrawerRef.value.show(editType, editData);
  }
}

// 判断是否允许拖拽
function allowDrag(draggingNode) {
  // 只允许叶子节点拖拽
  const isLeaf =
    !draggingNode.childNodes || draggingNode.childNodes.length === 0;
  return isLeaf;
}

// 判断是否允许放置
function allowDrop(draggingNode, dropNode, type) {
  // 不允许拖到空白处
  if (!dropNode) return false;

  // 叶子节点拖拽至 层级内
  if (
    dropNode.level === 1 &&
    draggingNode.data.display_type === "table" &&
    dropNode.data.display_type === "layer" &&
    type === "inner"
  ) {
    return true;
  }
  // 层级节点拖到 表格节点内
  if (
    dropNode.level === 1 &&
    draggingNode.data.display_type === "layer" &&
    dropNode.data.display_type === "table" &&
    type === "inner"
  ) {
    // nextTick(() => {
    //   dimensionTreeRef.value.remove(draggingNode.data.id);
    //   dimensionTreeRef.value.append(
    //     {
    //       ...draggingNode.data,
    //       display_type: "table",
    //       display_name: dropNode.data.label,
    //       display_id: dropNode.data.id,
    //     },
    //     dropNode.data.id
    //   );
    // });
    return true;
  }
  // 层级节点内允许顺序拖拽
  const sameParent = draggingNode.parent === dropNode.parent;
  const sourceParentIsLayer =
    draggingNode?.parent?.data?.display_type === "layer";
  if (sameParent && type !== "inner" && sourceParentIsLayer) {
    // console.log(draggingNode, dropNode, "drop");
    return true;
  }

  return false;
}

// 开始拖拽时记录来源
function handleDragStart(node, ev) {
  currentToArea.value = node.data.role;
  // draggingSource.value = node.data.role;
}

// 拖拽结束时清除来源记录, 在此判断是否拖出了区域 并交互了区域
function handleDragEnd(node, ev) {
  console.log(node, ev, cachedDimension.value, "end");
  // 拖拽结束时，如果节点来源和当前区域不一致，判断为 维度 / 度量 交换的拖拽
  if (
    !!node &&
    !ev &&
    node.data.role !== currentToArea.value &&
    node.parent.data.display_type === "table"
  ) {
    const sourceArea = node.data.role;
    const sourceParent = cloneDeep(node.parent);
    const targetArea = currentToArea.value;
    // 维度 拖拽至 度量
    if (sourceArea === "Dimension" && targetArea === "Measure") {
      // 将节点从维度树中移除
      dimensionTreeRef.value.remove(node.data.id);

      // 将节点添加到度量树中
      let targetParentInMeasure = cachedMeasure.value.find(
        (item) => item.table_id === sourceParent.data.table_id
      );
      // 先判断是否存在 不存在则新创建表
      if (!targetParentInMeasure) {
        cachedMeasure.value.push({
          ...sourceParent.data,
          children: [],
        });
      }
      nextTick(() => {
        // 存在直接添加
        measureTreeRef.value.append(
          { ...node.data, role: "Measure", id: `-${node.data.id}` },
          measureTreeRef.value.getNode(
            targetParentInMeasure.id || sourceParent.data.id
          )
        );
      });
    } else if (sourceArea === "Measure" && targetArea === "Dimension") {
      // 度量 拖拽至 维度
      // 将节点从度量树中移除
      measureTreeRef.value.remove(node.data.id);
      // 先判断是否存在 不存在则新创建表
      let targetParentInDimension = cachedDimension.value.find(
        (item) => item.table_id === sourceParent.data.table_id
      );

      if (!targetParentInDimension) {
        cachedDimension.value.push({
          ...sourceParent.data,
          children: [],
        });
      }
      nextTick(() => {
        // 存在直接添加
        dimensionTreeRef.value.append(
          { ...node.data, role: "Dimension", id: `-${node.data.id}` },
          dimensionTreeRef.value.getNode(
            targetParentInDimension.id || sourceParent.data.id
          )
        );
      });
    }
  }
  // 都有表示 进入到了对应节点了
  if (!!node && !!ev) {
    let draggingNode = node;
    let dropNode = ev;
    // 表格 拖拽至 层级
    if (
      dropNode.level === 1 &&
      draggingNode.data.display_type === "table" &&
      dropNode.data.display_type === "layer"
    ) {
      nextTick(() => {
        dimensionTreeRef.value.remove(draggingNode.data.id);
        dimensionTreeRef.value.append(
          {
            ...draggingNode.data,
            display_type: "layer",
            display_name: dropNode.data.label,
            display_id: dropNode.data.id,
          },
          dropNode.data.id
        );
      });
    }
    // 层级 拖拽至 表格
    if (
      dropNode.level === 1 &&
      draggingNode.data.display_type === "layer" &&
      dropNode.data.display_type === "table"
    ) {
      nextTick(() => {
        dimensionTreeRef.value.remove(draggingNode.data.id);
        dimensionTreeRef.value.append(
          {
            ...draggingNode.data,
            // id: `-${draggingNode.data.id}`,
            display_type: "table",
            display_name: dropNode.data.label,
            display_id: String(dropNode.data.table_id),
            display_order: 0,
          },
          dropNode.data.id
        );
      });
    }
  }
}

// 判断节点是进入了哪个区域  维度 |  度量
function handleDragEnter(node, ev) {
  let fieldContainer = node.target.closest(".field-card");
  let fieldType = fieldContainer
    ? fieldContainer.getAttribute("field-type")
    : null;
  if (fieldContainer || fieldType) {
    currentToArea.value = fieldType;
  }
}

// 新增表 外抛方法 tableId 要新增的表的id、 uId 要新增的表的唯一id、 preId 要插在哪个表节点前
function addTable(tableId, uId, preId) {
  loading.value = true;
  // 获取表字段列表
  getDatasourceTableFieldListApi(tableId)
    .then(({ data }: { data: any[] }) => {
      loading.value = false;
      if (data) {
        // 根据字段类型 将字段分类
        let tableName = data[0].table_name;
        // 获取当前 维度 | 度量 表格的最后一个index
        let lastestDimensionTableIndex = (
          cachedDimension.value as any
        ).findLastIndex((item) => item.display_type === "table");
        let lastestMeasureTableIndex = (
          cachedMeasure.value as any
        ).findLastIndex((item) => item.display_type === "table");
        // 要插入时，需要根据index 获取到对应的前置节点idIndex
        let dimensionPreIdIndex: number = null;
        let measurePreIdIndex: number = null;
        // 有pid 的情况 需要根据 pid 获取pid的index 进行插入
        if (preId) {
          dimensionPreIdIndex = cachedDimension.value.findIndex(
            (item) => item.id === preId
          );
          dimensionPreIdIndex++;
          measurePreIdIndex = cachedMeasure.value.findIndex(
            (item) => item.id === preId
          );
          measurePreIdIndex++;
        }
        let newDimensionTable = {
          id: uId,
          label: tableName,
          display_type: "table",
          children: [],
        };
        let newMeasureTable = {
          id: uId,
          label: tableName,
          display_type: "table",
          children: [],
        };
        data.map((item) => {
          // 新增表获取的field 没有id，默认以field_id 负数作为id
          if (item.role === "Dimension") {
            newDimensionTable.children.push({
              // id: item.id,
              field_id: item.id,
              label: item.alias,

              ...item,
              id: -item.id,
              display_type: "table",
              display_name: tableName,
              display_id: uId,
            });
          } else if (item.role === "Measure") {
            newMeasureTable.children.push({
              // id: item.id,
              field_id: item.id,

              label: item.alias,
              ...item,
              id: -item.id,
              display_id: uId,
              display_type: "table",
              display_name: tableName,
            });
          }
        });
        // 有前置节点表示 插在 最后一个table前置节点后
        // if (!!preId) {
        cachedDimension.value.splice(
          dimensionPreIdIndex ?? lastestDimensionTableIndex + 1,
          0,
          newDimensionTable
        );
        cachedMeasure.value.splice(
          measurePreIdIndex ?? lastestMeasureTableIndex + 1,
          0,
          newMeasureTable
        );
        console.log(
          cachedDimension.value,
          cachedMeasure.value,
          "cachedDimension.value, cachedMeasure.value"
        );
        // } else if (tableId) {
        //   dimensionTreeRef.value.append(newDimensionTable);
        //   measureTreeRef.value.append(newMeasureTable);
        // }
      }
    })
    .catch(() => {
      loading.value = false;
    });
}

// 删除表
function removeTable(tableId: number | number[] | string | string[]) {
  if (Array.isArray(tableId)) {
    tableId.forEach((id) => {
      dimensionTreeRef.value.remove(dimensionTreeRef.value.getNode(id));
      measureTreeRef.value.remove(measureTreeRef.value.getNode(id));
    });
  } else {
    dimensionTreeRef.value.remove(dimensionTreeRef.value.getNode(tableId));
    measureTreeRef.value.remove(measureTreeRef.value.getNode(tableId));
  }
}

// 更新表
function updateTableData(uId: string, data: any) {
  if (uId && typeof data === "object") {
    // 同时更新 维度、度量表
    cachedDimension.value.forEach((item) => {
      if (item.id === uId) {
        Object.assign(item, data);
      }
    });
    cachedMeasure.value.forEach((item) => {
      if (item.id === uId) {
        Object.assign(item, data);
      }
    });
  }
}

// 更新表格的顺序，删除不存在的  uIdList 最新排序的表格列表
function updateTableFieldIndex(uIdList: any[]) {
  // 获取 维度 | 度量 表格的列表Map
  let dimensionListMap = cachedDimension.value.reduce((pre, cur) => {
    if (cur.display_type === "table") {
      pre[cur.id] = cur;
    }
    return pre;
  }, {});
  let measureListMap = cachedMeasure.value.reduce((pre, cur) => {
    if (cur.display_type === "table") {
      pre[cur.id] = cur;
    }
    return pre;
  }, {});
  // 根据uIdList 的顺序 更新表格的顺序
  cachedDimension.value = [
    ...uIdList.reduce((pre, cur) => {
      if (dimensionListMap[cur.id]) {
        pre.push(dimensionListMap[cur.id]);
      }
      return pre;
    }, []),
    ...cachedDimension.value.filter((item) => item.display_type !== "table"),
  ];
  cachedMeasure.value = [
    ...uIdList.reduce((pre, cur) => {
      if (measureListMap[cur.id]) {
        pre.push(measureListMap[cur.id]);
      }
      return pre;
    }, []),
    ...cachedMeasure.value.filter((item) => item.display_type !== "table"),
  ];
}

// 计算维度 度量 确认  添加 / 编辑 自定义字段
function handleComputedFieldConfirm(value) {
  // 要新增的数据 | tree
  let sourceData =
    currentComputedType.value === "dimension"
      ? cachedDimension.value
      : cachedMeasure.value;
  let sourceRef =
    currentComputedType.value === "dimension"
      ? dimensionTreeRef.value
      : measureTreeRef.value;
  // 判断是否已经有自定义表
  let sourceHaveCustom = sourceData.find(
    (item) => item.display_type === "custom"
  );

  // 如果存在自定义表，直接插入，否则先新建再插入
  if (!sourceHaveCustom) {
    let customLabel = `自定义${
      currentComputedType.value === "dimension" ? "维度" : "度量"
    }`;
    let customId = uuidv4();
    // 生成自定义
    sourceHaveCustom = {
      id: customId,
      label: customLabel,
      display_type: "custom",
      children: [],
    };
    // 插入自定义
    sourceRef.append(sourceHaveCustom);
  }
  nextTick(() => {
    // 将新增的字段 插入自定义
    let fieldId = value.id || uuidv4();
    // 获取表达式
    const { label, field, expr } = value;
    const { id: pId, label: pLabel } = sourceHaveCustom;
    // 有id时，为编辑，先删除之前创建的节点
    if (value.id) {
      sourceRef.remove(value.id);
    }
    sourceRef.append(
      {
        id: value.id || `-${fieldId}`,
        field_id: fieldId,
        role:
          currentComputedType.value === "dimension" ? "Dimension" : "Measure",
        data_type: "String",
        ignore: false,
        label,
        field,
        expr,
        alias: label,
        // field_id: value,
        display_type: "custom",
        display_id: pId,
        display_name: pLabel,
        display_alias: pLabel,
        table_name: pLabel,
        table_alias: pLabel,
        table_id: pId,
        display_order: null,
        field_enum: null,
        model_id: curentEditModelData?.value?.id,
        logical_table_id: pId,
        database_name: "custom",
      },
      sourceRef.getNode(sourceHaveCustom.id)
    );
    ElMessage.success("操作成功");
    console.log(sourceData, curentEditModelData.value, "append custom field");
  });
}
// 将字段树  的二维数组转化为一维数组
function transformTreeDataToFlat(treeData: any[]) {
  return treeData.reduce((pre, cur) => {
    // 表格自定义直接合并
    if (cur.display_type !== "layer") {
      pre.push(...cur.children);
    } else if (cur.display_type === "layer") {
      // 分层需要根据当前排序 重置layer_order的排序
      let newChildren = cloneDeep(cur.children).map(
        (ychild: any, index: number) => {
          ychild.display_order = index;
          return ychild;
        }
      );
      pre.push(...newChildren);
    }
    return pre;
  }, []);
}

// 外抛 获取实际的field数据，
function getFieldData() {
  console.log(cachedDimension.value, cachedMeasure.value, "get field data");
  let dimensions = transformTreeDataToFlat(cloneDeep(cachedDimension.value));
  let measures = transformTreeDataToFlat(cloneDeep(cachedMeasure.value));
  return {
    dimensions,
    measures,
  };
  // console.log(dimension, measure, cachedDimension.value, cachedMeasure.value, "get field data");
}

defineExpose({
  addTable,
  removeTable,
  updateTableData,
  updateTableFieldIndex,
  getFieldData,
});
</script>

<style scoped lang="scss">
.field-list {
  display: flex;
  flex-direction: column;
  max-height: calc(100% - 80px);
}
.field-list-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  gap: 10px;
  position: relative;
  min-height: 0;
  .dimension-content,
  .measure-content {
    // height: 100%;
    flex: 1;
    overflow: hidden;
    width: 50%;
  }
  // :deep(.el-card) {
  //   height: 100%;
  //   overflow: auto;
  :deep(.el-card__body) {
    display: flex;
    height: 100%;
    overflow: hidden;
    flex-direction: column;
  }
  // }
  :deep(.field-tree) {
    overflow: auto;
    .field-catalog,
    .field-leaf {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 10px;
      .field-label {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        .ignore-field {
          color: #ccc;
        }
      }
      .tools {
        margin-right: 10px;
        display: flex;
        align-items: center;
        gap: 5px;
        display: none;
        color: #4c6f88;
        i {
          font-size: 12px;
          cursor: pointer;
        }
      }
      &:hover {
        .tools {
          display: flex;
        }
      }
    }
  }
}
.field-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  padding: 0 20px 12px 0;
}
.field-list-title {
  font-size: 16px;
  font-weight: bold;
  color: #222;
}
.field-list-actions {
  display: flex;
  align-items: center;
  gap: 5px;
}
.field-search {
  width: 180px;
}
.meta-btn {
  color: #005ee0;
  font-weight: 500;
}
.download-btn {
  color: #005ee0;
  font-size: 18px;
  padding: 0 4px;
}
.field-card {
  :deep(.el-card__body) {
    padding: 0;
  }
}
.field-title {
  height: 32px;
  line-height: 32px;
  padding: 0 20px;
  font-weight: bold;
  background: #fcfcfc;
  border-bottom: 1px solid #f2f2f2;
  .text-btn {
    font-weight: 600;
    font-size: 13px;
    color: #005ee0;
    cursor: pointer;
    .iconfont {
      font-size: 13px;
    }
  }
}

.layer-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px;
  cursor: pointer;
  &:hover {
    background: #f2f2f2;
  }
  .layer-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .layer-icon {
    color: #005ee0;
  }
}
</style>
