<template>
  <!-- 下拉框的apend to 容器 :translate-extent="translateExtent"-->
  <VueFlow ref="vueFlowRef" :nodes="nodes" :edges="edges" :min-zoom="1" :max-zoom="1"
    :translate-extent="translateExtent" :node-extent="translateExtent" :draggable="false">
    <template #node-sheet="{ id, data }">
      <sheet-node :id="id" :data="data" @rename="onRename" @remove="onRemove" :model-type="modelType" />
    </template>
    <template #node-virtual="props">
      <virtual-node />
    </template>
    <template #edge-custom="edgeProps">
      <customEdge :id="edgeProps.id" :source-x="edgeProps.sourceX" :source-y="edgeProps.sourceY"
        :target-x="edgeProps.targetX" :target-y="edgeProps.targetY" :source-position="edgeProps.sourcePosition"
        :target-position="edgeProps.targetPosition" :marker-end="edgeProps.markerEnd" :style="edgeProps.style"
        :model-type="modelType" />
    </template>
    <template #edge-virtual="edgeProps">
      <virtualEdge :id="edgeProps.id" :source-x="edgeProps.sourceX" :source-y="edgeProps.sourceY"
        :target-x="edgeProps.targetX" :target-y="edgeProps.targetY" :source-position="edgeProps.sourcePosition"
        :target-position="edgeProps.targetPosition" :marker-end="edgeProps.markerEnd" :style="edgeProps.style" />
    </template>
    <!-- 背景 -->
    <Background />
  </VueFlow>
</template>

<script setup lang="ts">
import { uniqueId, uniqBy, cloneDeep, drop } from "lodash-es";
import { VueFlow, useVueFlow } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import sheetNode from "./sheetNode.vue";
import virtualNode from "./virtualNode.vue";
import customEdge from "./customEdge.vue";
import virtualEdge from "./virtualEdge.vue";

const {
  onNodeDragStart,
  onNodeDrag,
  onNodeDragStop,
  onNodesInitialized,
  onPaneReady,
  removeNodes: removeNodes1,
  addEdges,
  addNodes,
  updateNode,
  updateEdge,
  getIntersectingNodes,
} = useVueFlow();

const props = defineProps({
  modelType: {
    type: String,
    desc: "edit | view", // 编辑 | 预览
    default: "edit",
  },
});

// 是否是预览模式
const isViewModel = computed(() => props.modelType === "view");

const emit = defineEmits(["change", "rename"]);

// 节点 宽200px 高 36px  间距120px
let nodes = ref([]);
let edges = ref([]);

let flowInstance = null;

let translateExtent = ref([
  [0, 0],
  [1000, 500],
]) as any;

onPaneReady((instance) => {
  flowInstance = instance;
  const { clientWidth, clientHeight } = instance.vueFlowRef.value;
  // console.log(90000, clientWidth, clientHeight )
  if (clientWidth && clientHeight) {
    instance.setViewport({ x: 0, y: 0, zoom: 1 });
    translateExtent.value = [
      [0, 0],
      [clientWidth, clientHeight],
    ];
  }
});

onNodesInitialized((a) => {
  nodes.value = flowInstance.getNodes.value;
  edges.value = flowInstance.getEdges.value;
});

// 拖拽的节点和边
let dragEdges = [];
let dragNodes = [];
// 图形数据映射
let coords = {};
let coords_x = [];
let coords_y = [];
let coords_x_y = {};
// 预览节点和边的id
let preview_nId = uniqueId("n_preview_");
let preview_eId = uniqueId("e_preview_");
// 拖拽的节点和边的拷贝
let copyDragNodes = [];
let copyDragEdges = [];
// 当前拖拽的节点
let curDragNode = null;
let oldTargetNode = { id: "1" };
let newPosition: any = {};

// 目标节点是拖动节点的前置节点或者是本节点则是无效拖动
let invalidDrag = false;

let disabledDropNodeIds = [];

// 相交的节点
let intersectingNodes = ref([]);
provide("getIntersectingNodes", intersectingNodes);

function onRename(id, name) {
  emit("rename", id, name);
}

function onRemove(id) {
  const delNodes = getNodeOuts(id);
  // vueflow 图形节点和本地nodes不绑定、需要同时删除
  removeNodes([{ id }, ...delNodes]);
  // removeNodes1([id, ...curNodes]);
  nextTick(() => {
    const curNodes = flowInstance.getNodes.value;
    nodes.value = curNodes;
    const arrs = getFlowDataToArr();

    emit("change", arrs);
  })


}

// 开始拖拽
onNodeDragStart(({ event, node }) => {
  console.log("开始拖拽节点:", node.id);
  intersectingNodes.value = [];
  invalidDrag = false;

  // 根节点不允许拖拽
  if (node.id === "1") {
    return;
  }

  // 重置拖拽相关变量
  dragEdges = [];
  dragNodes = [];
  disabledDropNodeIds = [];

  // 1. 收集当前节点及其所有后续节点
  const outNodes = getNodeOuts(node.id);
  dragNodes = [node, ...outNodes];
  console.log("拖拽节点组:", dragNodes.map(n => n.id));

  // 2. 收集相关连线
  dragEdges = getNodeOutsLines(dragNodes);
  const cEdges = dragEdges.filter((item) => item.target !== node.id);

  // 3. 创建预览副本（半透明显示）
  const { copyNodes, copyEdges, copyNodesMap } = createDragPreview(
    dragNodes,
    cEdges
  );

  // 4. 添加预览节点和连线到画布
  addNodes(copyNodes);
  addEdges(copyEdges);

  // 5. 更新本地状态
  nextTick(() => {
    nodes.value = flowInstance.getNodes.value;
    edges.value = flowInstance.getEdges.value;
  });

  // 6. 处理拖拽节点的入边连接到预览副本
  const incomingEdge = dragEdges.find((item) => item.target === node.id);
  if (incomingEdge) {
    const newEdge = {
      ...cloneDeep(incomingEdge),
      oid: incomingEdge.id,
      id: uniqueId("e_copy_"),
      target: copyNodesMap[incomingEdge.target],
    };
    edges.value = edges.value.filter((item) => item.target !== node.id);
    edges.value.push(newEdge);
  }

  // 7. 获取节点坐标映射（用于碰撞检测）
  const coordInfo = getNodesCoords();
  coords = coordInfo.coords;
  coords_x = coordInfo.coords_x;
  coords_y = coordInfo.coords_y;
  coords_x_y = {};
  Object.keys(coords).forEach((key) => {
    coords_x_y[`${key}`] = Object.keys(coords[key]) || [];
  });

  // 8. 设置禁止放置的节点ID
  const preNode = flowInstance.getIncomers(node.id)[0];
  const copyNode = flowInstance.getNodes.value.find((item: any) => item.oid === node.id);
  const outIds = getNodeOuts(copyNode?.id || "").map((cn) => cn.id);

  disabledDropNodeIds.push(
    preNode?.id,
    node.id,
    copyNode?.id,
    ...outIds
  );

  console.log("禁止放置的节点:", disabledDropNodeIds);
});
// 拖拽中
onNodeDrag(({ event, node }) => {
  if (node.id === "1") {
    return;
  }

  const { x, y } = node.position;
  const { movementX, movementY } = event as any;

  // 1. 同步移动所有拖拽节点组（包括后续节点）
  updateDragNodesPosition(node, movementX, movementY);

  // 2. 计算碰撞检测区域
  const collisionArea = {
    left: x - 60,    // 连线宽度120的一半
    right: x + 260,  // 节点宽度200 + 60
    top: y - 10,     // 节点间距20的一半
    bottom: y + 46   // 节点高度36 + 10
  };

  // 3. 查找最近的目标节点
  const targetNode = getClosestNode(
    [collisionArea.left, collisionArea.right],
    [collisionArea.top, collisionArea.bottom]
  );

  // 4. 检查是否为无效拖拽
  if (!targetNode || disabledDropNodeIds.includes(targetNode.id)) {
    invalidDrag = true;
    clearDropPreview();
    return;
  }

  invalidDrag = false;

  // 5. 更新放置预览
  if (oldTargetNode.id !== targetNode.id) {
    updateDropPreview(targetNode);
  }
});

// 更新拖拽节点组的位置
function updateDragNodesPosition(draggedNode, movementX, movementY) {
  dragNodes.forEach((item) => {
    if (item.id === draggedNode.id) {
      // 更新原始拖拽节点位置
      updateNode(item.id, {
        position: {
          x: item.position.x + movementX,
          y: item.position.y + movementY,
        },
      });
    } else {
      // 同步移动后续节点
      const relativeX = item.position.x - draggedNode.position.x;
      const relativeY = item.position.y - draggedNode.position.y;
      updateNode(item.id, {
        position: {
          x: draggedNode.position.x + movementX + relativeX,
          y: draggedNode.position.y + movementY + relativeY,
        },
      });
    }
  });
}

// 清除放置预览
function clearDropPreview() {
  removeEdges([{ id: preview_eId }]);
  removeNodes([{ id: preview_nId }]);
}

// 更新放置预览
function updateDropPreview(targetNode) {
  // 清除旧预览
  clearDropPreview();

  oldTargetNode = targetNode;
  newPosition = getNewNodePostion(targetNode, coords);

  if (!newPosition) {
    return false;
  }

  // 添加新的放置预览
  nodes.value.push({
    id: preview_nId,
    type: "virtual",
    position: { x: newPosition.x, y: newPosition.y },
    data: { label: "放置预览" },
    style: {
      opacity: 0.7,
      border: '2px dashed #005EE0'
    }
  });

  edges.value.push({
    id: preview_eId,
    type: "virtual",
    source: targetNode.id,
    target: preview_nId,
    style: {
      strokeDasharray: '5,5',
      stroke: '#005EE0',
      opacity: 0.7
    }
  });
}
// 找到node的x轴正负100范围内与coords_x_y中x轴坐标差值最小的坐标
function getClosestXInRange(nodeX, range = 100) {
  if (!coords_x_y || Object.keys(coords_x_y).length === 0) {
    return null;
  }

  // 获取所有现有的x轴坐标
  const existingXCoords = Object.keys(coords_x_y).map((x) => Number(x));

  // 筛选正负100范围内的x坐标
  const xInRange = existingXCoords.filter((x) => Math.abs(x - nodeX) <= range);

  if (xInRange.length === 0) {
    return null;
  }

  // 找到差值最小的x坐标
  let closestX = null;
  let minDistance = Infinity;

  xInRange.forEach((x) => {
    const distance = Math.abs(x - nodeX);
    if (distance < minDistance) {
      minDistance = distance;
      closestX = x;
    }
  });

  return closestX;
}
// 自定义碰撞检测函数
function getCustomIntersectingNodes(targetNode) {
  const nodes = flowInstance.getNodes.value;
  const intersecting = [];
  // 动态获取容器高度
  const containerHeight = flowInstance?.vueFlowRef?.value?.clientHeight || 500;
  const extendHeight = containerHeight / 2; // 向上扩展容器高度的一半
  nodes.forEach((node) => {
    if (node.id === targetNode.id) return;
    const { x, y } = node.position;
    // const isSingleYNode = coords_x_y?.[y]?.length === 1
    // console.log(x, y, coords_x_y, coords, isSingleYNode, "containerHeight");
    // 计算扩展的碰撞区域
    const nodeRect = {
      x: node.position.x,
      y: node.position.y, // 向上扩展动态高度
      width: 200,
      height: containerHeight, // 当前y轴只有一个元素 占满使用全屏，否则单个元素
    };

    const targetRect = {
      x: targetNode.position.x,
      y: targetNode.position.y,
      width: 200,
      height: 36,
    };

    // 检测矩形相交
    if (isRectIntersecting(nodeRect, targetRect)) {
      intersecting.push(node);
    }
  });

  return intersecting;
}

function isRectIntersecting(rect1, rect2) {
  return !(
    rect1.x + rect1.width < rect2.x ||
    rect2.x + rect2.width < rect1.x ||
    rect1.y + rect1.height < rect2.y ||
    rect2.y + rect2.height < rect1.y
  );
}

// 拖拽结束
onNodeDragStop(({ event, node }) => {
  console.log(
    node,
    dragNodes,
    nodes.value,
    edges.value,
    "in diagram onNodeDragStop::"
  );
  intersectingNodes.value = [];
  if (invalidDrag) {
    removeNodes1([{ id: preview_nId }, ...dragNodes]);
    // removeEdges(dragEdges);
    nextTick(() => {

      const curNodes = flowInstance.getNodes.value;
      const curLines = flowInstance.getEdges.value;

      // removeNodes(dragNodes);
      // 无效移动时 还原id
      curNodes.forEach((item) => {
        if (item?.id?.indexOf("n_copy_") > -1) {
          updateNode(item.id, {
            id: item.oid,
          });
        }
      });
      curLines.forEach((edge) => {
        if (edge.id.indexOf("e_copy_") > -1) {
          if (edge.target !== edge.targetNode.id) {
            edge.target = edge.targetNode.id;
          }
        }
      });
      nodes.value = curNodes;
      edges.value = curLines;
      // removeNodes([{ id: preview_nId }, ...dragNodes]);
      console.log(dragNodes, nodes.value, edges.value, curNodes, curLines, "nodes.value");
    })

    return;
  } else if (node.id === "1") {
    updateNode("1", {
      position: {
        x: 50,
        y: 50,
      },
    });
  } else {
    // 找到关联的节点后删除复制的节点和线，未找到则删除拖拽的节点和线
    // removeNodes1([{id: preview_nId}, ...copyDragNodes]);
    // 将变更的节点放到拖拽的位置，并发出change事件
    dropDragNodes(node);
    // edges.value.push
    addEdges({
      id: uniqueId("e_"),
      type: "custom",
      source: oldTargetNode.id,
      target: node.id,
    });
    // 结束后删除复制的节点
    // nextTick(() => {
    //   console.log(dragEdges, dragNodes, "dragEdges");
    //   for (let i = 0; i < nodes.value.length; i++) {
    //     if (nodes.value[i].id.indexOf("n_copy_") > -1) {
    //       removeNodes([{ id: nodes.value[i].id }]);
    //     }
    //   }
    // });
    // console.log(333, 'Node drag stop:', oldTargetNode.id, node.id, flowInstance.getNodes.value, flowInstance.getEdges.value);
    const arrs = getFlowDataToArr();
    emit("change", arrs);
  }
  invalidDrag = false;
});

// 放置拖拽的所有节点, sourceId:拖拽的节点
function dropDragNodes(dragNode) {
  const { id: dragId, position } = dragNode;
  const { x: n_x, y: n_y } = newPosition;
  let move_x = n_x - position.x;
  let move_y = n_y - position.y;
  dragNodes.forEach((item) => {
    const { x, y } = item.position;
    let drop_x = n_x;
    let drop_y = n_y;
    if (item.id !== dragId) {
      drop_x = move_x > 0 ? x + move_x : x - move_x;
      drop_y = move_y > 0 ? y + move_y : y - move_y;
    }
    updateNode(item.id, {
      position: {
        x: drop_x,
        y: drop_y,
      },
    });
  });
}

// 获取添加节点的位置
function getNewNodePostion(node, coords) {
  const { x, y } = node.position;
  let node_x = 0;
  let node_y = 0;
  const y_nodes = coords[`${x}`];
  if (!y_nodes) {
    return
    // return {
    //   x,
    //   y
    // };
  }
  const link_ys = Object.keys(y_nodes);
  const outgoers = flowInstance
    .getOutgoers(node.id)
    .filter((item) => item.type !== "virtual");
  // 1: 节点没有关联其他节点,在这个节点后面直接追加
  if (!outgoers.length) {
    node_x = x + 200 + 120;
    node_y = y;
  } else {
    if (link_ys.length > 1) {
      // 同一列有多个节点的时候, 需要将同列的节点往下移动
      let move_ys = link_ys.filter((item) => Number(item) > y);
      if (move_ys.length) {
        move_ys.forEach((my) => {
          const { id } = y_nodes[my];
          let outNodes = getNodeOuts(id);
          // 节点以及关联的节点 整体向下平移
          moveNodes([y_nodes[my], ...outNodes]);
        });
        // console.log(711, flowInstance.getNodes)
      }
      node_x = x + 200 + 120;
      node_y = y + 20 + 36;
    } else {
      let out_x = [];
      let out_y = [];
      outgoers.forEach((item) => {
        const { x, y } = item.position;
        out_x.push(x);
        out_y.push(y);
      });
      node_x = Math.min(...out_x);
      node_y = Math.max(...out_y) + 36 + 20;
    }
  }
  return { x: node_x, y: node_y };
}

// 同一列有多个节点的时候, 需要将同列的节点往下移动
function moveNodes(mvNodes) {
  // 节点以及关联的节点 整体向下平移
  mvNodes.forEach((n) => {
    const { id, position } = n;
    nodes.value.forEach((node: any) => {
      if (node.id === id) {
        // 记录历史位置，恢复位置使用
        node.oldPosition = {
          x: position.x,
          y: position.y,
        };
        node.position = {
          x: position.x,
          y: position.y + 20 + 36, //20+36=增加一个节点的空间
        };
      }
    });
  });
}

function restoreMoveNodesPositon() {
  nodes.value.forEach((node: any) => {
    const { oldPosition, position } = node;
    if (oldPosition) {
      node.position = {
        x: oldPosition.x,
        y: oldPosition.y,
      };
    }
  });
}

function getClosestNode(range_x, range_y) {
  const in_x = findClosestNumber(coords_x, range_x);
  // console.log(coords_x, range_x, range_y, "getClosestNode::")
  let cols_y = coords_x_y[`${in_x}`];
  if (cols_y.length) {
    const in_y = findClosestNumber(cols_y, range_y);
    // console.log(222, in_x, in_y);
    return coords[`${in_x}`][`${in_y}`];
  }
  return null;
}

// 创建拖拽预览副本（优化版本）
function createDragPreview(nodes, edges) {
  let copyNodesMap = {};
  let copyEdges = [];
  let copyNodes = [];

  // 创建预览节点（半透明效果）
  copyNodes = cloneDeep(nodes).map((item) => {
    const newId = uniqueId("n_preview_");
    copyNodesMap[item.id] = newId;

    return {
      ...item,
      oid: item.id, // 保存原始ID
      id: newId,
      data: {
        ...item.data,
        label: item.data.label || item.data.table_alias,
        table_alias: item.data.table_alias || item.data.label,
        isPreview: true // 标记为预览节点
      },
      draggable: false, // 预览节点不可拖拽
      selectable: false, // 预览节点不可选择
      type: item.type || "sheet",
      position: {
        x: item.position.x,
        y: item.position.y,
      },
      style: {
        opacity: 0.5, // 半透明效果
        pointerEvents: 'none' // 禁用鼠标事件
      },
      class: 'drag-preview-node' // 添加CSS类名
    };
  });

  // 创建预览连线
  copyEdges = cloneDeep(edges).map((item) => {
    const newId = uniqueId("e_preview_");
    return {
      ...item,
      oid: item.id,
      id: newId,
      source: copyNodesMap[item.source] || item.source,
      target: copyNodesMap[item.target] || item.target,
      style: {
        opacity: 0.5,
        strokeDasharray: '5,5' // 虚线效果
      },
      class: 'drag-preview-edge'
    };
  });

  return {
    copyNodes,
    copyEdges,
    copyNodesMap,
  };
}

// 保留原函数作为备用（兼容性）
function copyDragFlow(nodes, edges) {
  return createDragPreview(nodes, edges);
}
// 收集所有节点的坐标,以x轴为key，将节点分成一列一列
function getNodesCoords() {
  let coords = {};
  let coords_x = [];
  let coords_y = [];
  // const allNodes = flowInstance.getNodes;
  const curNodes = flowInstance.getNodes.value;
  // console.log(101987, allNodes.value, nodes.value);
  curNodes
    .filter((item) => item.type !== "virtual")
    .forEach((item) => {
      const { x, y } = item.position;
      const xkey = `${x}`;
      const ykey = `${y}`;
      coords_x.push(x);
      coords_y.push(y);
      if (!coords[xkey]) {
        coords[xkey] = {};
      }
      coords[xkey][ykey] = item;
    });
  return {
    coords,
    coords_x: [...new Set(coords_x)],
    coords_y: [...new Set(coords_y)],
  };
}

function getNodeOuts(nodeId) {
  function collectOuts(nodes = [], result = []) {
    for (let i = 0; i < nodes.length; i++) {
      const { id } = nodes[i];
      const outgoers = flowInstance.getOutgoers(id);
      if (outgoers.length) {
        result.push(...outgoers);
        collectOuts(outgoers, result);
      }
    }
  }

  let nodes = flowInstance.getOutgoers(nodeId);
  if (!nodes.length) {
    return [];
  }
  let finds = [...nodes];
  collectOuts(nodes, finds);
  return finds.filter((item) => item.type !== "virtual");
}

function getNodeOutsLines(nodes) {
  let result = [];
  for (let i = 0; i < nodes.length; i++) {
    const { id } = nodes[i];
    const lines = flowInstance.getConnectedEdges(id);
    result.push(...lines);
  }
  const edges = uniqBy(result, "id");
  return edges;
}

function removeNodes(delNodes) {
  // const delIds = delNodes.map((item) => item.id);
  // nodes.value = nodes.value.filter((item) => !delIds.includes(item.id));
  removeNodes1(delNodes);
  nextTick(() => {
    const curNodes = flowInstance.getNodes.value;
    nodes.value = curNodes;
  })
}

function removeEdges(delEdges) {
  const delIds = delEdges.map((item) => item.id);
  edges.value = edges.value.filter((item) => !delIds.includes(item.id));

}

function findClosestNumber(numbers, range) {
  const [min, max] = range;

  // 筛选范围内的数字
  const inRangeNumbers = numbers.find((num) => num >= min && num <= max);

  if (inRangeNumbers) {
    return inRangeNumbers; // 如果有范围内的数字，直接返回
  }

  // 如果没有范围内的数字，找到最接近的数字
  let closestNumber = null;
  let minDistance = Infinity;

  numbers.forEach((num) => {
    const distanceToMin = Math.abs(num - min);
    const distanceToMax = Math.abs(num - max);
    const distance = Math.min(distanceToMin, distanceToMax);

    if (distance < minDistance) {
      minDistance = distance;
      closestNumber = num;
    }
  });

  return closestNumber;
}

function getFlowDataToTables() {
  let tables = [];
  let tableIds = [];
  const allNodes = flowInstance.getNodes;
  const sheetNodes = allNodes.value.filter((item) => item.type !== "virtual");
  sheetNodes.forEach((node) => {
    const { id, data, join = null } = node;
    tableIds.push(data.table_id || data.id);
    // 首节点
    if (id === "1") {
      tables.push({
        nodeId: id,
        table_id: data.id,
        join: null,
        ...data,
      });
    } else {
      tables.push({
        nodeId: id,
        table_id: data.id,
        ...data,
        join: join,
      });
    }
  });
  return { tableIds, tables };
}

function getGraphDatas() {
  return {
    nodes: nodes.value,
    edges: edges.value,
  };
}

// 将图的数据深度遍历有序的放进一个数组里面
function getFlowDataToArr() {
  const allNodes = flowInstance.getNodes;
  const sheetNodes = allNodes.value.filter((item) => item.type !== "virtual");
  // let deepArr = [];
  // sheetNodes.forEach(node => {

  // });
  return sheetNodes;
}

function resetFlowChart(nodes1 = [], edges1 = []) {
  nodes.value = nodes1.map(node => ({
    ...node,
    draggable: props.modelType === "edit"
  }));
  edges.value = edges1;
  console.log(nodes.value, edges.value, "resetFlowChart");
}

defineExpose({
  getNodesCoords,
  getClosestNode,
  findClosestNumber,
  getNewNodePostion,
  getFlowDataToTables,
  getGraphDatas,
  resetFlowChart,
});
</script>

<style>
/* import the necessary styles for Vue Flow to work */
@import "@vue-flow/core/dist/style.css";

/* import the default theme, this is optional but generally recommended */
@import "@vue-flow/core/dist/theme-default.css";
</style>

<style lang="scss" scoped>
/* 拖拽预览节点样式 */
:deep(.drag-preview-node) {
  opacity: 0.6 !important;
  transform: scale(0.95);
  border: 2px dashed #005EE0 !important;
  background: rgba(0, 94, 224, 0.1) !important;
  pointer-events: none;
  transition: all 0.2s ease;
}

/* 拖拽预览连线样式 */
:deep(.drag-preview-edge) {
  opacity: 0.5 !important;
  stroke-dasharray: 8,4 !important;
  stroke: #005EE0 !important;
  stroke-width: 2px !important;
}

/* 放置预览样式 */
:deep(.vue-flow__node[data-id*="n_preview_"]) {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 0.8; }
  100% { opacity: 0.5; }
}

/* 拖拽中的节点高亮 */
:deep(.vue-flow__node.dragging) {
  box-shadow: 0 8px 25px rgba(0, 94, 224, 0.3);
  z-index: 1000;
}

/* 无效拖拽区域提示 */
:deep(.vue-flow__node.invalid-drop) {
  border: 2px solid #ff4757 !important;
  background: rgba(255, 71, 87, 0.1) !important;
}
</style>
