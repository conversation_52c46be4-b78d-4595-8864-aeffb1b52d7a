<template>
  <!-- 下拉框的apend to 容器 :translate-extent="translateExtent"-->
  <VueFlow ref="vueFlowRef" :nodes="nodes" :edges="edges" :min-zoom="1" :max-zoom="1"
    :translate-extent="translateExtent" :node-extent="translateExtent" :draggable="false">
    <template #node-sheet="{ id, data }">
      <sheet-node :id="id" :data="data" @rename="onRename" @remove="onRemove" :model-type="modelType" />
    </template>
    <template #node-virtual="props">
      <virtual-node />
    </template>
    <template #edge-custom="edgeProps">
      <customEdge :id="edgeProps.id" :source-x="edgeProps.sourceX" :source-y="edgeProps.sourceY"
        :target-x="edgeProps.targetX" :target-y="edgeProps.targetY" :source-position="edgeProps.sourcePosition"
        :target-position="edgeProps.targetPosition" :marker-end="edgeProps.markerEnd" :style="edgeProps.style"
        :model-type="modelType" />
    </template>
    <template #edge-virtual="edgeProps">
      <virtualEdge :id="edgeProps.id" :source-x="edgeProps.sourceX" :source-y="edgeProps.sourceY"
        :target-x="edgeProps.targetX" :target-y="edgeProps.targetY" :source-position="edgeProps.sourcePosition"
        :target-position="edgeProps.targetPosition" :marker-end="edgeProps.markerEnd" :style="edgeProps.style" />
    </template>
    <!-- 背景 -->
    <Background />
  </VueFlow>
</template>

<script setup lang="ts">
import { uniqueId, uniqBy, cloneDeep, drop } from "lodash-es";
import { VueFlow, useVueFlow } from "@vue-flow/core";
import { Background } from "@vue-flow/background";
import sheetNode from "./sheetNode.vue";
import virtualNode from "./virtualNode.vue";
import customEdge from "./customEdge.vue";
import virtualEdge from "./virtualEdge.vue";

const {
  onNodeDragStart,
  onNodeDrag,
  onNodeDragStop,
  onNodesInitialized,
  onPaneReady,
  removeNodes: removeNodes1,
  addEdges,
  addNodes,
  updateNode,
  updateEdge,
  getIntersectingNodes,
} = useVueFlow();

const props = defineProps({
  modelType: {
    type: String,
    desc: "edit | view", // 编辑 | 预览
    default: "edit",
  },
});

// 是否是预览模式
const isViewModel = computed(() => props.modelType === "view");

const emit = defineEmits(["change", "rename"]);

// 节点 宽200px 高 36px  间距120px
let nodes = ref([]);
let edges = ref([]);

let flowInstance = null;

let translateExtent = ref([
  [0, 0],
  [1000, 500],
]) as any;

onPaneReady((instance) => {
  flowInstance = instance;
  const { clientWidth, clientHeight } = instance.vueFlowRef.value;
  // console.log(90000, clientWidth, clientHeight )
  if (clientWidth && clientHeight) {
    instance.setViewport({ x: 0, y: 0, zoom: 1 });
    translateExtent.value = [
      [0, 0],
      [clientWidth, clientHeight],
    ];
  }
});

onNodesInitialized((a) => {
  nodes.value = flowInstance.getNodes.value;
  edges.value = flowInstance.getEdges.value;
});

// 拖拽的节点和边
let dragEdges = [];
let dragNodes = [];
// 图形数据映射
let coords = {};
let coords_x = [];
let coords_y = [];
let coords_x_y = {};
// 预览节点和边的id
let preview_nId = uniqueId("n_preview_");
let preview_eId = uniqueId("e_preview_");
// 拖拽的节点和边的拷贝
let copyDragNodes = [];
let copyDragEdges = [];
// 当前拖拽的节点
let curDragNode = null;
let oldTargetNode = { id: "1" };
let newPosition: any = {};

// 目标节点是拖动节点的前置节点或者是本节点则是无效拖动
let invalidDrag = false;

let disabledDropNodeIds = [];

// 相交的节点
let intersectingNodes = ref([]);
provide("getIntersectingNodes", intersectingNodes);

function onRename(id, name) {
  emit("rename", id, name);
}

function onRemove(id) {
  const delNodes = getNodeOuts(id);
  // vueflow 图形节点和本地nodes不绑定、需要同时删除
  removeNodes([{ id }, ...delNodes]);
  // removeNodes1([id, ...curNodes]);
  nextTick(() => {
    const curNodes = flowInstance.getNodes.value;
    nodes.value = curNodes;
    const arrs = getFlowDataToArr();

    emit("change", arrs);
  })


}

// 开始拖拽
onNodeDragStart(({ event, node }) => {
  // console.log(node, "in diagram onNodeDragStart::");
  intersectingNodes.value = [];
  invalidDrag = false;
  if (node.id === "1") {
    return;
  }

  dragEdges = [];
  dragNodes = [];
  disabledDropNodeIds = [];
  // 找到该node后续连接的节点
  let outNodes = getNodeOuts(node.id);
  dragNodes = [node, ...outNodes];
  // 找到拖动node后续连接的线
  dragEdges = getNodeOutsLines(dragNodes);
  const cEdges = dragEdges.filter((item) => item.target !== node.id);
  // 复制一份节点和连线
  const { copyNodes, copyEdges, copyNodesMap } = copyDragFlow(
    dragNodes,
    cEdges
  );
  console.log(outNodes, dragNodes, cEdges, copyNodes, copyEdges, "start copyNodes, copyEdges");
  addNodes(copyNodes);
  addEdges(copyEdges);
  // 添加复制出来的节点、线 在原本的位置显示
  nextTick(() => {
    // 从 VueFlow 获取最新状态同步到本地
    nodes.value = flowInstance.getNodes.value;
    edges.value = flowInstance.getEdges.value;
  })

  console.log(
    copyNodes,
    nodes.value,
    copyEdges,
    edges.value,
    disabledDropNodeIds,
    flowInstance.getNodes.value,
    "copyNodes, copyEdges"
  );
  // 改变拖动节点的target， 连线到复制出来的节点上
  let connect = dragEdges.find((item) => item.target === node.id);
  if (connect) {
    let newEdge = {
      ...cloneDeep(connect),
      oid: connect.id,
      id: uniqueId("e_copy_"),
      target: copyNodesMap[connect.target],
    };
    edges.value = edges.value.filter((item) => item.target !== node.id);
    edges.value.push(newEdge);
  }

  // 获取节点的坐标映射
  const coordInfo = getNodesCoords();
  coords = coordInfo.coords;
  coords_x = coordInfo.coords_x;
  coords_y = coordInfo.coords_y;
  coords_x_y = {};
  Object.keys(coords).forEach((key) => {
    coords_x_y[`${key}`] = Object.keys(coords[key]) || [];
  });

  // 只存在一个节点的情况
  let preNode = flowInstance.getIncomers(node.id)[0];
  const copyNode = flowInstance.getNodes.value.find((item: any) => item.oid === node.id);
  const outIds = getNodeOuts(copyNode.id).map((cn) => cn.id);

  disabledDropNodeIds.push(preNode.id, node.id, copyNode.id, ...outIds);
  // const allEs = flowInstance.getEdges;
  // console.log(111, 'onNodeDragStart::)', copyNodes);
});
// 拖拽中
onNodeDrag(({ event, node }) => {
  if (node.id === "1") {
    return;
  }

  const { x, y } = node.position;
  // const hasNodesInYRange = getClosestXInRange(x)
  // const isSingleYNode = coords_x_y?.[hasNodesInYRange]?.length === 1
  // intersectingNodes.value = isSingleYNode && hasNodesInYRange ? getCustomIntersectingNodes(node) : getIntersectingNodes(node);
  // console.log(isSingleYNode, hasNodesInYRange, intersectingNodes, coords_x_y, x, "intersectionIds");
  const { movementX, movementY } = event as any;
  // console.log(node.id, dragNodes, nodes.value, "node drag");
  dragNodes.forEach((item) => {
    const { id, position } = item;
    if (id === node.id) {
      updateNode(id, {
        position: {
          x: position.x + (movementX),
          y: position.y + (movementY),
        },
      });
    }
  });
  const range_x_left = x - 60; //连线宽度是120，取一半
  const range_x_right = x + 200 + 60; //节点宽度是200

  const range_y_top = y - 10; //节点间的间距20，取一半
  const range_y_bottom = y + 36 + 10; //节点高度是36

  // 获取离的最接近的节点
  const targetNode = getClosestNode(
    [range_x_left, range_x_right],
    [range_y_top, range_y_bottom]
  );

  // 目标节点是拖动节点的前置节点或者是本节点则是无效拖动
  invalidDrag = disabledDropNodeIds.includes(targetNode.id);
  if (invalidDrag) {
    removeEdges([{ id: preview_eId }]);
    removeNodes([{ id: preview_nId }]);
    return;
  }
  // console.log(9544, oldTargetNode.id, targetNode.id, disabledDropNodeIds)
  if (oldTargetNode.id !== targetNode.id) {
    // 删除已添加的预览节点和线条
    oldTargetNode = targetNode;
    // console.log(9544, targetNode.id)
    newPosition = getNewNodePostion(targetNode, coords);
    if (!newPosition) {
      return false
    }
    nodes.value.push({
      id: preview_nId,
      type: "virtual",
      position: { x: newPosition.x, y: newPosition.y },
      data: { label: "预览" },
    });
    edges.value.push({
      id: preview_eId,
      type: "virtual",
      source: targetNode.id,
      target: preview_nId,
    });
    // console.log(778883, nodes.value)
  }
  // }else{
  //   restoreMoveNodesPositon();
  // }
});
// 找到node的x轴正负100范围内与coords_x_y中x轴坐标差值最小的坐标
function getClosestXInRange(nodeX, range = 100) {
  if (!coords_x_y || Object.keys(coords_x_y).length === 0) {
    return null;
  }

  // 获取所有现有的x轴坐标
  const existingXCoords = Object.keys(coords_x_y).map((x) => Number(x));

  // 筛选正负100范围内的x坐标
  const xInRange = existingXCoords.filter((x) => Math.abs(x - nodeX) <= range);

  if (xInRange.length === 0) {
    return null;
  }

  // 找到差值最小的x坐标
  let closestX = null;
  let minDistance = Infinity;

  xInRange.forEach((x) => {
    const distance = Math.abs(x - nodeX);
    if (distance < minDistance) {
      minDistance = distance;
      closestX = x;
    }
  });

  return closestX;
}
// 自定义碰撞检测函数
function getCustomIntersectingNodes(targetNode) {
  const nodes = flowInstance.getNodes.value;
  const intersecting = [];
  // 动态获取容器高度
  const containerHeight = flowInstance?.vueFlowRef?.value?.clientHeight || 500;
  const extendHeight = containerHeight / 2; // 向上扩展容器高度的一半
  nodes.forEach((node) => {
    if (node.id === targetNode.id) return;
    const { x, y } = node.position;
    // const isSingleYNode = coords_x_y?.[y]?.length === 1
    // console.log(x, y, coords_x_y, coords, isSingleYNode, "containerHeight");
    // 计算扩展的碰撞区域
    const nodeRect = {
      x: node.position.x,
      y: node.position.y, // 向上扩展动态高度
      width: 200,
      height: containerHeight, // 当前y轴只有一个元素 占满使用全屏，否则单个元素
    };

    const targetRect = {
      x: targetNode.position.x,
      y: targetNode.position.y,
      width: 200,
      height: 36,
    };

    // 检测矩形相交
    if (isRectIntersecting(nodeRect, targetRect)) {
      intersecting.push(node);
    }
  });

  return intersecting;
}

function isRectIntersecting(rect1, rect2) {
  return !(
    rect1.x + rect1.width < rect2.x ||
    rect2.x + rect2.width < rect1.x ||
    rect1.y + rect1.height < rect2.y ||
    rect2.y + rect2.height < rect1.y
  );
}

// 拖拽结束
onNodeDragStop(({ event, node }) => {
  console.log(
    node,
    dragNodes,
    nodes.value,
    edges.value,
    "in diagram onNodeDragStop::"
  );
  intersectingNodes.value = [];
  if (invalidDrag) {
    removeNodes1([{ id: preview_nId }, ...dragNodes]);
    // removeEdges(dragEdges);
    nextTick(() => {

      const curNodes = flowInstance.getNodes.value;
      const curLines = flowInstance.getEdges.value;

      // removeNodes(dragNodes);
      // 无效移动时 还原id
      curNodes.forEach((item) => {
        if (item?.id?.indexOf("n_copy_") > -1) {
          updateNode(item.id, {
            id: item.oid,
          });
        }
      });
      curLines.forEach((edge) => {
        if (edge.id.indexOf("e_copy_") > -1) {
          if (edge.target !== edge.targetNode.id) {
            edge.target = edge.targetNode.id;
          }
        }
      });
      nodes.value = curNodes;
      edges.value = curLines;
      // removeNodes([{ id: preview_nId }, ...dragNodes]);
      console.log(dragNodes, nodes.value, edges.value, curNodes, curLines, "nodes.value");
    })

    return;
  } else if (node.id === "1") {
    updateNode("1", {
      position: {
        x: 50,
        y: 50,
      },
    });
  } else {
    // 找到关联的节点后删除复制的节点和线，未找到则删除拖拽的节点和线
    // removeNodes1([{id: preview_nId}, ...copyDragNodes]);
    // 将变更的节点放到拖拽的位置，并发出change事件
    dropDragNodes(node);
    // edges.value.push
    addEdges({
      id: uniqueId("e_"),
      type: "custom",
      source: oldTargetNode.id,
      target: node.id,
    });
    // 结束后删除复制的节点
    // nextTick(() => {
    //   console.log(dragEdges, dragNodes, "dragEdges");
    //   for (let i = 0; i < nodes.value.length; i++) {
    //     if (nodes.value[i].id.indexOf("n_copy_") > -1) {
    //       removeNodes([{ id: nodes.value[i].id }]);
    //     }
    //   }
    // });
    // console.log(333, 'Node drag stop:', oldTargetNode.id, node.id, flowInstance.getNodes.value, flowInstance.getEdges.value);
    const arrs = getFlowDataToArr();
    emit("change", arrs);
  }
  invalidDrag = false;
});

// 放置拖拽的所有节点, sourceId:拖拽的节点
function dropDragNodes(dragNode) {
  const { id: dragId, position } = dragNode;
  const { x: n_x, y: n_y } = newPosition;
  let move_x = n_x - position.x;
  let move_y = n_y - position.y;
  dragNodes.forEach((item) => {
    const { x, y } = item.position;
    let drop_x = n_x;
    let drop_y = n_y;
    if (item.id !== dragId) {
      drop_x = move_x > 0 ? x + move_x : x - move_x;
      drop_y = move_y > 0 ? y + move_y : y - move_y;
    }
    updateNode(item.id, {
      position: {
        x: drop_x,
        y: drop_y,
      },
    });
  });
}

// 获取添加节点的位置
function getNewNodePostion(node, coords) {
  const { x, y } = node.position;
  let node_x = 0;
  let node_y = 0;
  const y_nodes = coords[`${x}`];
  if (!y_nodes) {
    return
    // return {
    //   x,
    //   y
    // };
  }
  const link_ys = Object.keys(y_nodes);
  const outgoers = flowInstance
    .getOutgoers(node.id)
    .filter((item) => item.type !== "virtual");
  // 1: 节点没有关联其他节点,在这个节点后面直接追加
  if (!outgoers.length) {
    node_x = x + 200 + 120;
    node_y = y;
  } else {
    if (link_ys.length > 1) {
      // 同一列有多个节点的时候, 需要将同列的节点往下移动
      let move_ys = link_ys.filter((item) => Number(item) > y);
      if (move_ys.length) {
        move_ys.forEach((my) => {
          const { id } = y_nodes[my];
          let outNodes = getNodeOuts(id);
          // 节点以及关联的节点 整体向下平移
          moveNodes([y_nodes[my], ...outNodes]);
        });
        // console.log(711, flowInstance.getNodes)
      }
      node_x = x + 200 + 120;
      node_y = y + 20 + 36;
    } else {
      let out_x = [];
      let out_y = [];
      outgoers.forEach((item) => {
        const { x, y } = item.position;
        out_x.push(x);
        out_y.push(y);
      });
      node_x = Math.min(...out_x);
      node_y = Math.max(...out_y) + 36 + 20;
    }
  }
  return { x: node_x, y: node_y };
}

// 同一列有多个节点的时候, 需要将同列的节点往下移动
function moveNodes(mvNodes) {
  // 节点以及关联的节点 整体向下平移
  mvNodes.forEach((n) => {
    const { id, position } = n;
    nodes.value.forEach((node: any) => {
      if (node.id === id) {
        // 记录历史位置，恢复位置使用
        node.oldPosition = {
          x: position.x,
          y: position.y,
        };
        node.position = {
          x: position.x,
          y: position.y + 20 + 36, //20+36=增加一个节点的空间
        };
      }
    });
  });
}

function restoreMoveNodesPositon() {
  nodes.value.forEach((node: any) => {
    const { oldPosition, position } = node;
    if (oldPosition) {
      node.position = {
        x: oldPosition.x,
        y: oldPosition.y,
      };
    }
  });
}

function getClosestNode(range_x, range_y) {
  const in_x = findClosestNumber(coords_x, range_x);
  // console.log(coords_x, range_x, range_y, "getClosestNode::")
  let cols_y = coords_x_y[`${in_x}`];
  if (cols_y.length) {
    const in_y = findClosestNumber(cols_y, range_y);
    // console.log(222, in_x, in_y);
    return coords[`${in_x}`][`${in_y}`];
  }
  return null;
}

function copyDragFlow(nodes, edges) {
  let copyNodesMap = {};
  let copyEdges = [];
  let copyNodes = [];
  copyNodes = cloneDeep(nodes).map((item) => {
    item.oid = item.id;
    item.id = uniqueId("n_copy_");
    item.data.label = item.data.label;
    item.data.table_alias = item.data.table_alias || item.data.label;
    item.draggable = true;
    item.selectable = true;
    item.type = item.type || "sheet";
    item.position = {
      x: item.position.x,
      y: item.position.y,
    };

    copyNodesMap[item.oid] = item.id;
    return item;
  });
  copyEdges = cloneDeep(edges).map((item) => {
    const { id, source, target } = item;
    item.oid = id;
    item.id = uniqueId("e_copy_");
    item.source = copyNodesMap[source];
    item.target = copyNodesMap[target];
    return item;
  });
  return {
    copyNodes,
    copyEdges,
    copyNodesMap,
  };
}
// 收集所有节点的坐标,以x轴为key，将节点分成一列一列
function getNodesCoords() {
  let coords = {};
  let coords_x = [];
  let coords_y = [];
  // const allNodes = flowInstance.getNodes;
  const curNodes = flowInstance.getNodes.value;
  // console.log(101987, allNodes.value, nodes.value);
  curNodes
    .filter((item) => item.type !== "virtual")
    .forEach((item) => {
      const { x, y } = item.position;
      const xkey = `${x}`;
      const ykey = `${y}`;
      coords_x.push(x);
      coords_y.push(y);
      if (!coords[xkey]) {
        coords[xkey] = {};
      }
      coords[xkey][ykey] = item;
    });
  return {
    coords,
    coords_x: [...new Set(coords_x)],
    coords_y: [...new Set(coords_y)],
  };
}

function getNodeOuts(nodeId) {
  function collectOuts(nodes = [], result = []) {
    for (let i = 0; i < nodes.length; i++) {
      const { id } = nodes[i];
      const outgoers = flowInstance.getOutgoers(id);
      if (outgoers.length) {
        result.push(...outgoers);
        collectOuts(outgoers, result);
      }
    }
  }

  let nodes = flowInstance.getOutgoers(nodeId);
  if (!nodes.length) {
    return [];
  }
  let finds = [...nodes];
  collectOuts(nodes, finds);
  return finds.filter((item) => item.type !== "virtual");
}

function getNodeOutsLines(nodes) {
  let result = [];
  for (let i = 0; i < nodes.length; i++) {
    const { id } = nodes[i];
    const lines = flowInstance.getConnectedEdges(id);
    result.push(...lines);
  }
  const edges = uniqBy(result, "id");
  return edges;
}

function removeNodes(delNodes) {
  // const delIds = delNodes.map((item) => item.id);
  // nodes.value = nodes.value.filter((item) => !delIds.includes(item.id));
  removeNodes1(delNodes);
  nextTick(() => {
    const curNodes = flowInstance.getNodes.value;
    nodes.value = curNodes;
  })
}

function removeEdges(delEdges) {
  const delIds = delEdges.map((item) => item.id);
  edges.value = edges.value.filter((item) => !delIds.includes(item.id));

}

function findClosestNumber(numbers, range) {
  const [min, max] = range;

  // 筛选范围内的数字
  const inRangeNumbers = numbers.find((num) => num >= min && num <= max);

  if (inRangeNumbers) {
    return inRangeNumbers; // 如果有范围内的数字，直接返回
  }

  // 如果没有范围内的数字，找到最接近的数字
  let closestNumber = null;
  let minDistance = Infinity;

  numbers.forEach((num) => {
    const distanceToMin = Math.abs(num - min);
    const distanceToMax = Math.abs(num - max);
    const distance = Math.min(distanceToMin, distanceToMax);

    if (distance < minDistance) {
      minDistance = distance;
      closestNumber = num;
    }
  });

  return closestNumber;
}

function getFlowDataToTables() {
  let tables = [];
  let tableIds = [];
  const allNodes = flowInstance.getNodes;
  const sheetNodes = allNodes.value.filter((item) => item.type !== "virtual");
  sheetNodes.forEach((node) => {
    const { id, data, join = null } = node;
    tableIds.push(data.table_id || data.id);
    // 首节点
    if (id === "1") {
      tables.push({
        nodeId: id,
        table_id: data.id,
        join: null,
        ...data,
      });
    } else {
      tables.push({
        nodeId: id,
        table_id: data.id,
        ...data,
        join: join,
      });
    }
  });
  return { tableIds, tables };
}

function getGraphDatas() {
  return {
    nodes: nodes.value,
    edges: edges.value,
  };
}

// 将图的数据深度遍历有序的放进一个数组里面
function getFlowDataToArr() {
  const allNodes = flowInstance.getNodes;
  const sheetNodes = allNodes.value.filter((item) => item.type !== "virtual");
  // let deepArr = [];
  // sheetNodes.forEach(node => {

  // });
  return sheetNodes;
}

function resetFlowChart(nodes1 = [], edges1 = []) {
  nodes.value = nodes1.map(node => ({
    ...node,
    draggable: props.modelType === "edit"
  }));
  edges.value = edges1;
  console.log(nodes.value, edges.value, "resetFlowChart");
}

defineExpose({
  getNodesCoords,
  getClosestNode,
  findClosestNumber,
  getNewNodePostion,
  getFlowDataToTables,
  getGraphDatas,
  resetFlowChart,
});
</script>

<style>
/* import the necessary styles for Vue Flow to work */
@import "@vue-flow/core/dist/style.css";

/* import the default theme, this is optional but generally recommended */
@import "@vue-flow/core/dist/theme-default.css";
</style>

<style lang="scss" scoped></style>
