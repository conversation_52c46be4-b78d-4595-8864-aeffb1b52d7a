<template>
  <div class="agent-manage-container">
    <p class="head-title">助手管理</p>
    <!-- 查询表单区域 -->
    <div class="search-container">
      <radio-group
        v-model="published"
        :options="publishedOptions"
        @change="getAgentList()"
      />
      <pre-label-select
        v-model="app_code"
        label="助手类型："
        clearable
        @change="getAgentList()"
      >
        <el-option
          v-for="item in options"
          :key="item.chat_scene"
          :label="item.scene_name"
          :value="item.chat_scene"
        />
      </pre-label-select>
      <el-input
        v-model="app_name"
        style="width: 200px"
        placeholder="输入关键字"
        :suffix-icon="Search"
        @keyup.enter="getAgentList()"
      />
      <div class="btns">
        <el-button v-permission="['agent:create']" type="primary" @click="handleAddAgent">
          <i
            class="iconfont ChatData-jiahao"
            style="font-size: 13px; margin-right: 5px"
          ></i>
          新增助手
        </el-button>
      </div>
    </div>
    <!-- 助手列表 -->
    <div class="agent-list-container">
      <el-card v-for="item in agentList" :key="item.app_code" @click="handleDropdownCommand('edit', item)">
        <!-- 头部区域 -->
        <template #header>
          <svg class="icon" aria-hidden="true" style="font-size: 35px">
            <use xlink:href="#ChatData-damoxing"></use>
          </svg>
          <div class="btns">
            <el-dropdown @command="(cmd) => handleDropdownCommand(cmd, item)">
              <el-icon v-show="isShowEvent(item)"><MoreFilled /></el-icon>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    v-if="GetPermission(['agent_permissions:publish'],item.app_code)"
                    :command="
                      item.published === 'true' ? 'unPublish' : 'Publish'
                    "
                    >{{
                      item.published === "true" ? "取消发布" : "发布"
                    }}</el-dropdown-item
                  >
                  <!-- <el-dropdown-item command="edit">编辑</el-dropdown-item> -->
                  <el-dropdown-item v-if="GetPermission(['agent_permissions:delete'],item.app_code)" command="delete" divided
                    >删除</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-tooltip content="开始对话">
              <i
                @click.stop="gotoChat(item)"
                class="iconfont ChatData-duihua start-chart"
              ></i>
            </el-tooltip>
          </div>
        </template>
        <!-- 内容区域 -->
        <div>
          <p class="title">{{ item.app_name }}</p>
          <section class="desc">
            {{ item.app_describe }}
          </section>
        </div>

        <!-- 底部区域 -->
        <template #footer>
          <div class="tags">
            <el-tag
              :class="
                item.published === 'true' ? 'publish-tag' : 'un-publish-tag'
              "
              :type="item.published === 'true' ? 'success' : 'primary'"
              >{{ item.published === "true" ? "已发布" : "未发布" }}</el-tag
            >
            <el-tag effect="plain" type="info" v-if="item.team_mode">
              {{ item.team_mode }}
            </el-tag>
            <el-tag effect="plain" type="info" v-if="!!item.language">
              {{ item.language === "zh" ? "中文" : "英文" }}
            </el-tag>
          </div>
          <span class="last-update" style="font-size: 12px">
            <span>{{ item.owner_name }}</span>
            <span style="color: #595959; margin-left: 5px">{{
              `${transferTime(item.updated_at)}更新`
            }}</span>
          </span>
        </template>
      </el-card>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, toRaw } from "vue";
import { Search, MoreFilled } from "@element-plus/icons-vue";
import preLabelSelect from "./components/preLabelSelect.vue";
import radioGroup from "./components/radioGroup.vue";
import { getAgentListApi } from "@/common/apis/chat";
import {
  getApplicationTypeApi,
  deleteAssistantApi,
  unPublishAssistantApi,
  publishAssistantApi,
} from "@/common/apis/agent";
import type { Agent } from "@/common/apis/chat/type";
import { get } from "lodash-es";
import { formatTimeAgo } from "@@/utils/datetime";
import { useRouter, useRoute } from "vue-router";
import type { ApplicationType } from "./type";
import { ElMessageBox, ElMessage } from "element-plus";
const router = useRouter();
const route = useRoute();
const published = ref<string>("all"); // 状态选择
const publishedOptions = ref<object[]>([
  {
    value: "all",
    label: "全部",
  },
  {
    value: "true",
    label: "已发布",
  },
  {
    value: "false",
    label: "未发布",
  },
]);
const app_code = ref<string>(""); // 助手类型
const app_name = ref<string>(""); // 关键字
const options = ref<ApplicationType[]>([]); // 助手类型options
const agentList = ref<Agent.TableData[]>([]); // 助手列表
const loading = ref<boolean>(false); // 加载状态

// 获取助手列表
function getAgentList(params: object = {}) {
  loading.value = true;
  agentList.value = [];
  // 拼接查询参数
  let reqParams: object = {
    app_name: app_name.value,
    app_code: app_code.value,
    published: published.value !== "all" ? published.value : "",
    workspace_id: route.query.spaceCode || "",
  };
  getAgentListApi({
    page: 1,
    pageSize: 1000,
    ...reqParams,
    ...params,
  })
    .then((res) => {
      const response = res as { data: Agent.TableData };
      agentList.value = get(response, "data.app_list", []);
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getAgentList();

// 获取应用类型
const getApplicationType = () => {
  options.value = [];
  getApplicationTypeApi().then((res: any) => {
    options.value = get(res, "data", []);
  });
};
getApplicationType();

// 转化显示时间
const transferTime = (str: string): string => {
  return formatTimeAgo(str);
};

// 新增助手
const handleAddAgent = () => {
  router.push({
    path: "/agent/agentManage",
    query: {
      spaceCode: route.query.spaceCode || "",
    },
  });
};

// 下拉菜单命令
function handleDropdownCommand(command: string, item: Agent.TableData) {
  switch (command) {
    case "unPublish":
      handleUnPublishAgent(item);
      break;
    case "Publish":
      handleUnPublishAgent(item, true);
      break;
    case "edit":
      const { app_code = "" } = item as Agent.TableData;
      router.push({
        path: "/agent/agentManage",
        query: {
          spaceCode: route.query.spaceCode || "",
          type: "edit",
          app_code,
        },
      });
      break;
    case "delete":
      handleDeleteAgent(item);
      break;
  }
}

// 删除助手
function handleDeleteAgent(item: Agent.TableData) {
  ElMessageBox.confirm("确定删除该助手吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteAssistantApi({
      app_code: item.app_code,
    })
      .then((res: any) => {
        ElMessage.success("删除成功");
        getAgentList();
      })
      .catch((err: any) => {
        ElMessage.error("删除失败");
      });
  });
}

// 取消发布助手
function handleUnPublishAgent(
  item: Agent.TableData,
  isPublish: boolean = false
) {
  ElMessageBox.confirm(`确定${isPublish ? "" : "取消"}发布该助手吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let API = isPublish ? publishAssistantApi : unPublishAssistantApi;
    API({
      app_code: item.app_code,
    })
      .then((res: any) => {
        ElMessage.success(`${isPublish ? "" : "取消"}发布成功`);
        getAgentList();
      })
      .catch((err: any) => {
        ElMessage.error("取消发布失败");
      });
  });
}

// 跳转至对话页面
const gotoChat = (item: Agent.TableData) => {
  console.log(item.app_code, toRaw(item), 999);
  const { app_code = "" } = toRaw(item) as Agent.TableData;
  router.push({
    name: "Chat",
    query: {
      spaceCode: route.query.spaceCode || "",
      initAgent: app_code,
    },
  });
};
// 根据权限判断是否显示树操作
function isShowEvent(data:any){
  let flag = true
  const { proxy } = getCurrentInstance();
  if(!(proxy.GetPermission(['agent_permissions:publish'],data.app_code)) && !(proxy.GetPermission(['agent_permissions:delete'],data.app_code))) {
    flag = false
  }
  return flag
}
</script>
<style lang="scss" scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.btns {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: end;
}
.start-chart {
  padding: 5px;
  border-radius: 8px;
  background: #ebf3fd;
  color: #005ee0;
}
.agent-manage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 150px;
  background: white;
  .head-title {
    font-size: 18px;
    color: #262626;
    line-height: 20px;
    margin: 15px 0px;
    font-weight: 600;
  }
  .search-container {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    :deep(.el-input__inner) {
      font-size: 13px;
    }
    .published-radio {
      margin-right: 10px;
      font-size: 13px;
    }
  }
  .agent-list-container {
    // margin-top: 15px;
    flex: 1 1 auto;
    margin-top: 10px;
    padding: 5px 0px;
    overflow: auto;
    width: 100%;
    display: grid;
    gap: 15px;
    grid-auto-rows: 200px;

    // 默认3列
    grid-template-columns: repeat(3, 1fr);

    // 中等屏幕4列
    // @media (min-width: 1200px) {
    //   grid-template-columns: repeat(4, 1fr);
    // }

    // 大屏幕5列
    // 2k以上分辨率5列

    @media (min-width: 2048px) {
      grid-template-columns: repeat(5, 1fr);
    }
    :deep(.el-card) {
      // height: 200px;
      // width: 30%;
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      flex-direction: column;

      box-shadow:  0px 2px 6px 0px #FAFAFA;
      &:hover {
        box-shadow: 0px 2px 6px 0px #EDEDED;
      }
      .el-card__header {
        border-bottom: none;
        box-sizing: border-box;
        height: 60px;
        display: flex;
        align-items: center;
        padding-bottom: 0px;
      }
      .el-card__body {
        flex: 1 1 auto;
        box-sizing: border-box;
        padding: 10px 20px;
        min-width: 0; // 添加这行，强制允许收缩
        overflow: hidden; // 添加这行，防止内容溢出

        .title {
          font-weight: 600;
          font-size: 16px;
          color: #262626;
          margin: 10px 0px;
        }
        .desc {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 18px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; // 显示两行
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all; // 允许在任意字符间断行
        }
      }
      .el-card__footer {
        border-top: none;
        display: flex;
        align-items: center;
        gap: 10px;
        height: 26px;
        padding: 10px 20px 20px 20px;
        // margin-bottom: 10px;
        box-sizing: content-box;
        .tags {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1 1 auto;
          box-sizing: border-box;
        }
      }
    }
  }
}
.publish-tag {
  background: #dff6eb;
  font-weight: 600;
  font-size: 12px;
  color: #2bbf79;
}
.un-publish-tag {
  background: #e5f1ff;
  font-weight: 600;
  font-size: 12px;
  color: #005ee0;
}
</style>

