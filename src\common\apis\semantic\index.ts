import { request } from "@/http/axios"
// import type * as Semantic from "./type"


/** 获取数据模型详情 */
export function getSemanticModelApi<T extends object>(id:string | number, data?: T) {
    return request({
        url: `/serve/data-models/${id}`,
        method: "get",
        data,
        version: "/api/v2"
    })
}

// 获取数据模型树
export function getSemanticModelTreeApi<T extends object>(params?: T) {
  return request({
      url: `/serve/data-models/tree`,
      method: "get",
      params,
      version: "/api/v2"
  })
}
// 创建数据主题
export function createSemanticFolderApi<T extends object>(data?: T) {
  return request({
    url: `/serve/data-models/folder`,
    method: "post",
    data,
    version: "/api/v2"
  })
}
// 获取数据模型
export function getSemanticModelListApi<T extends object>(data?: T) {
  return request({
    url: `/serve/data-models/`,
    method: "get",
    params:data,
    version: "/api/v2"
  })
}
// 创建数据模型
export function createSemanticModelApi<T extends object>(data?: T) {
  return request({
    url: `/serve/data-models/`,
    method: "post",
    data,
    version: "/api/v2"
  })
}
// 删除数据模型
export function deleteSemanticModelApi<T extends object>(id:string | number, data?: T) {
  return request({
    url: `/serve/data-models/${id}`,
    method: "delete",
    data,
    version: "/api/v2"
  })
}
// 修改数据模型
export function updateSemanticModelApi<T extends object>(id:string | number, data?: T) {
  return request({
    url: `/serve/data-models/${id}`,
    method: "put",
    data,
    version: "/api/v2"
  })
}
// 修改数据主题
export function updateSemanticFolderApi<T extends object>(id:string | number, data?: T) {
  return request({
    url: `/serve/data-models/folder/${id}`,
    method: "put",
    data,
    version: "/api/v2"
  })
}
export function updateSemanticModelAttributionApi<T extends object>(id:string | number, data?: T) {
  return request({
    url: `/serve/data-models/${id}/attribution`,
    method: "put",
    data,
    version: "/api/v2"
  })
}
// 数据模型克隆
export function cloneSemanticModelApi<T extends object>(id:string | number, data?: T) {
  return request({
    url: `/serve/data-models/${id}/clone`,
    method: "post",
    data,
    version: "/api/v2"
  })
}