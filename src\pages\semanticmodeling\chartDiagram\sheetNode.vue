<template>
  <!-- vue-flow添加nodrag类名不可拖动 -->
  <div class="sheet-node flex-row" :class="otherClass">
    <div class="name flex1 ellipsis">{{ data.table_alias || data.table_name }}--{{props.id}}</div>
    <el-popover
      width="100"
      placement="right"
      v-if="modelType === 'edit'"
    >
      <div class="pop-tool" @click="onRename">重命名</div>
      <div class="pop-tool del" @click="onRemove">移除</div>
      <template #reference>
        <i class="iconfont ChatData-danjiantouyou" style="font-size: 14px; color: #4C6F88;"></i>
      </template>
    </el-popover>
    <renameDialog ref="renameDialogRef" @sure="onSureRename"/>
    <!-- <div class="sheet-wrapper"></div> -->
  </div>
  <!-- 连接桩 -->
  <Handle type="target" :position="Position.Left" :connectable="false" style="opacity: 0"/>
  <Handle type="source" :position="Position.Right" :connectable="false" style="opacity: 0"/>
</template>

<script lang="ts" setup>
  import { Handle, Position, useVueFlow } from '@vue-flow/core';
  import renameDialog from './renameDialog.vue';
  const { updateNode } = useVueFlow();
  // 获取相交节点
  const intersectingNodes = inject<any>('getIntersectingNodes');
  const otherClass = computed(() => {
    return intersectingNodes.value.some(node => node.id === props.id) ? 'active-sheet-node' : '';
  })
  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    modelType: {
      type: String,
      default: 'edit'
    }
  })

  const emit = defineEmits(['rename', 'remove']);

  const renameDialogRef = ref(null);

  function onRename(){
    const {table_alias, table_name} = props.data;
    renameDialogRef.value.show(table_alias || table_name);
  }
  function onRemove(){
    emit('remove', props.id);
  }
  function onSureRename(name){
    updateNode(props.id, {
      data: {
        ...props.data,
        table_alias: name
      }
    })
    emit('rename', props.id, name);
  }
  // watch(intersectingNodes, (newVal) => {
  //   console.log(newVal, 'sheetNode intersectingNodes');
  // })
</script>

<style scoped lang="scss">
.sheet-node{
  width: 200px;
  height: 36px;
  gap: 10px;
  padding-left: 10px;
  padding-right: 5px;
  border-radius: 4px;
  position: relative;
  z-index: 1;
  background: #FFFFFF;
  border: 1px solid #EDEDED;
  border-left: solid 3px #5167FF;
  box-shadow: 0px 2px 6px 0px #FAFAFA;
  .ChatData-danjiantouyou{
      display: none;
    }
  &:hover{
    .ChatData-danjiantouyou{
      display: inline-block;
    }
  }
  &.active-sheet-node{
    border-color: red;
  }
}
.pop-tool{
  line-height: 26px;
  font-weight: 400;
  font-size: 13px;
  color: #222222;
  cursor: pointer;
  &.del{
    color: #FD033B;
  }
}
</style>
