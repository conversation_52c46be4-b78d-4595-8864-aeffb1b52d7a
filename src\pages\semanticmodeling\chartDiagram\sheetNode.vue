<template>
  <!-- vue-flow添加nodrag类名不可拖动 -->
  <div class="sheet-node flex-row" :class="nodeClasses">
    <div class="name flex1 ellipsis">
      {{ data.table_alias || data.table_name }}
      <span v-if="data.isPreview" class="preview-tag">预览</span>
    </div>
    <el-popover
      width="100"
      placement="right"
      v-if="modelType === 'edit'"
    >
      <div class="pop-tool" @click="onRename">重命名</div>
      <div class="pop-tool del" @click="onRemove">移除</div>
      <template #reference>
        <i class="iconfont ChatData-danjiantouyou" style="font-size: 14px; color: #4C6F88;"></i>
      </template>
    </el-popover>
    <renameDialog ref="renameDialogRef" @sure="onSureRename"/>
    <!-- <div class="sheet-wrapper"></div> -->
  </div>
  <!-- 连接桩 -->
  <Handle type="target" :position="Position.Left" :connectable="false" style="opacity: 0"/>
  <Handle type="source" :position="Position.Right" :connectable="false" style="opacity: 0"/>
</template>

<script lang="ts" setup>
  import { Handle, Position, useVueFlow } from '@vue-flow/core';
  import renameDialog from './renameDialog.vue';
  const { updateNode } = useVueFlow();
  // 获取相交节点
  const intersectingNodes = inject<any>('getIntersectingNodes');
  // 计算节点样式类名
  const nodeClasses = computed(() => {
    const classes = [];

    // 相交节点高亮
    if (intersectingNodes.value.some(node => node.id === props.id)) {
      classes.push('active-sheet-node');
    }

    // 预览节点样式
    if (props.data.isPreview) {
      classes.push('preview-node');
    }

    return classes.join(' ');
  });
  const props = defineProps({
    id: {
      type: String,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    modelType: {
      type: String,
      default: 'edit'
    }
  })

  const emit = defineEmits(['rename', 'remove']);

  const renameDialogRef = ref(null);

  function onRename(){
    const {table_alias, table_name} = props.data;
    renameDialogRef.value.show(table_alias || table_name);
  }
  function onRemove(){
    emit('remove', props.id);
  }
  function onSureRename(name){
    updateNode(props.id, {
      data: {
        ...props.data,
        table_alias: name
      }
    })
    emit('rename', props.id, name);
  }
  // watch(intersectingNodes, (newVal) => {
  //   console.log(newVal, 'sheetNode intersectingNodes');
  // })
</script>

<style scoped lang="scss">
.sheet-node{
  width: 200px;
  height: 36px;
  gap: 10px;
  padding-left: 10px;
  padding-right: 5px;
  border-radius: 4px;
  position: relative;
  z-index: 1;
  background: #FFFFFF;
  border: 1px solid #EDEDED;
  border-left: solid 3px #5167FF;
  box-shadow: 0px 2px 6px 0px #FAFAFA;
  transition: all 0.2s ease;

  .name {
    display: flex;
    align-items: center;
    gap: 8px;

    .preview-tag {
      font-size: 12px;
      color: #005EE0;
      background: rgba(0, 94, 224, 0.1);
      padding: 2px 6px;
      border-radius: 2px;
      border: 1px solid rgba(0, 94, 224, 0.3);
    }
  }

  .ChatData-danjiantouyou{
      display: none;
    }
  &:hover{
    .ChatData-danjiantouyou{
      display: inline-block;
    }
  }
  &.active-sheet-node{
    border-color: #005EE0;
    background: rgba(0, 94, 224, 0.05);
    box-shadow: 0 2px 8px rgba(0, 94, 224, 0.2);
  }

  /* 预览节点样式 */
  &.preview-node {
    opacity: 0.7;
    border: 2px dashed #005EE0 !important;
    background: rgba(0, 94, 224, 0.08) !important;
    transform: scale(0.95);
    pointer-events: none;

    .name {
      color: #005EE0;
      font-weight: 500;
    }
  }
}
.pop-tool{
  line-height: 26px;
  font-weight: 400;
  font-size: 13px;
  color: #222222;
  cursor: pointer;
  &.del{
    color: #FD033B;
  }
}
</style>
