<template>
  <div class="chart-diagram-container" v-if="!!curChartType">
    <div class="chart-diagram-container-header flex-row">
      <div class="chart-diagram-container-header-title flex1">
        {{ props.config.label }}
      </div>
      <div class="chart-type-radio-group">
        <div class="slider" :style="sliderStyle"></div>
        <el-radio-group v-model="curChartType" @change="handleChartTypeChange">
          <el-radio-button v-for="item in chartTypeCheckMap" :key="item.value" :label="item.value"
            :class="{ 'is-active': curChartType === item.value }">
            <!-- svg 通过href 方式引用,无法直接修改, 通过切换图标进行处理 -->
            <i :class="item.icon" v-show="curChartType === item.value"></i>
            <svg class="test-icon" size="4" aria-hidden="true" v-show="curChartType !== item.value">
              <use :href="`#${item.icon.replace('iconfont ', '')}`"></use>
            </svg>
          </el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <template v-if="showData.length">
      <div class="chartDiagram" ref="chartDiagram" v-show="curChartType !== 'table' && showData.length" :style="{
        width: '100%',
        height: '400px',
        position: 'relative', // 添加相对定位
      }"></div>
      <div class="table-container" v-show="curChartType === 'table' || !showData.length">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column v-for="item in tableHeader" :key="item" :label="item" :prop="item" sortable />
        </el-table>
      </div>
    </template>
    <template v-else>
      <el-empty description="暂无数据" style="width: 100%;" />
    </template>

  </div>
  <div v-else>
    {{ props.config }}
  </div>
</template>
<script setup lang="ts">
import * as echarts from "echarts";
import {
  ref,
  onMounted,
  defineProps,
  watchEffect,
  nextTick,
  toRaw,
  computed,
} from "vue";
import { cloneDeep } from "lodash-es";

const chartDiagram = ref<HTMLElement>();
const chartInstance = ref<any>();
const curChartType = ref<string>("");
const tableData = ref<any[]>([]); // 表格视图
const chartTypeCheckMap = ref<any[]>([
  {
    label: "表格",
    value: "table",
    icon: "iconfont ChatData-biaoge",
  },
  {
    label: "柱状图",
    value: "bar",
    icon: "iconfont ChatData-zhuzhuangtu",
  },
  {
    label: "饼图",
    value: "pie",
    icon: "iconfont ChatData-jichengduliang",
  },
  {
    label: "折线图",
    value: "line",
    icon: "iconfont ChatData-quxiantu",
  },
]);

const props = defineProps({
  config: {
    type: Object,
    default: () => ({}),
  },
});

// 修改数据处理函数，支持多值
function processMultiData(data: any[], xKeys: string[], yKeys: string[]) {
  const xData: string[] = [];
  const seriesData: any[] = [];

  // 初始化每个y轴的数据数组
  yKeys.forEach(() => seriesData.push([]));

  data.forEach((item) => {
    let curXkeySplit = "";
    // 处理x轴数据, 拼接x轴
    xKeys.forEach((xKey, index) => {
      if (xKey.indexOf("-") > -1) {
        let timeKey = xKey.split("-");
        timeKey.forEach((timeKeyItem, timeKeyIndex) => {
          curXkeySplit += `${item[timeKeyItem] || item.x}${timeKeyIndex === timeKey.length - 1 ? "" : "-"
            }`;
        });
      } else {
        curXkeySplit += `${item[xKey] || item.x}${index === xKeys.length - 1 ? "" : "-"
          }`;
      }
    });
    xData.push(curXkeySplit);

    // 处理y轴数据
    yKeys.forEach((yKey, index) => {
      seriesData[index].push(item[yKey] || item.y?.[index]);
    });
  });

  return { xData, seriesData };
}

// 修改柱状图配置
function convertToEChartsOption() {
  const response: any = toRaw(props.config);
  const xKeys = Array.isArray(xAxisAlias.value)
    ? xAxisAlias.value
    : [xAxisAlias.value];
  const yKeys = Array.isArray(yAxisAlias.value)
    ? yAxisAlias.value
    : [yAxisAlias.value];
  const yUnits = Array.isArray(yUnit.value) ? yUnit.value : [yUnit.value];
  const { xData, seriesData } = processMultiData(
    response.data || [],
    xKeys,
    yKeys
  );
  const primaryUnit = yUnits[0] || "";

  return {
    // title: {
    //   text: props?.config?.label,
    //   left: "center",
    // },
    tooltip: {
      trigger: "item", // 改为item触发
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "rgba(255,255,255,0.9)",
      borderColor: "#ccc",
      borderWidth: 1,
      padding: [16, 20],
      textStyle: {
        color: "#333",
      },
      formatter: (params: any) => {

        // 获取当前hover的数据索引
        const dataIndex = params.dataIndex;
        // 构建显示的结果字符串
        let result = `${params.name}<br/>`;

        // 遍历所有的y轴数据系列
        yKeys.forEach((key, index) => {
          const value = seriesData[index][dataIndex];
          const currentUnit = yUnits[index] || "";

          result += `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${params.color};"></span>`;
          result += `${key}：${value}${currentUnit}<br/>`;
        });

        return result;
      },
    },
    legend: {
      data: yKeys,
      bottom: 0,
      selectedMode: true, // 确保可以切换选中状态
    },
    xAxis: {
      type: "category",
      data: xData,
      // axisTick: {
      //   show: true,
      // },
      name: xUnit.value
    },
    yAxis: {
      type: "value",
      name: primaryUnit,
      axisLabel: {
        formatter: `{value}`,
      },
      // splitLine: {
      //   lineStyle: {
      //     type: "dashed",
      //     opacity: 0.2,
      //   },
      // },
    },
    series: yKeys.map((key, index) => ({
      name: key, // 确保name与legend.data中的值对应
      type: "bar",
      data: seriesData[index], // 使用处理后的数据
      label: {
        show: true,
        position: "top",
        formatter: `{c}${yUnits[index] || ""}`,
      },
      // itemStyle: {
      //   color: getSeriesColor(index)
      // },
      // emphasis: {
      //   // 添加强调效果
      //   focus: "series",
      //   itemStyle: {
      //     shadowBlur: 10,
      //     shadowOffsetX: 0,
      //     shadowOffsetY: 3,
      //     shadowColor: 'rgba(0, 0, 0, 0.3)'
      //   }
      // },
      select: {
        // 添加选中状态的样式
        disabled: false,
      },
    })),
    grid: {
      containLabel: true,
      left: "3%",
      right: "10%",
      bottom: "15%",
      top: "15%",
    },
  };
}

// 获取系列颜色
function getSeriesColor(index: number) {
  const colors = [
    "#2BBF79",
    "#00B7F5",
    "#5167FF",
    "#6224FF",

  ];
  return colors[index % colors.length];
}

// 修改饼图配置
function convertToEChartsPieOption() {
  const response: any = toRaw(props.config);
  const xKeys = Array.isArray(xAxisAlias.value)
    ? xAxisAlias.value
    : [xAxisAlias.value];
  const yKeys = Array.isArray(yAxisAlias.value)
    ? yAxisAlias.value
    : [yAxisAlias.value];
  const yUnits = Array.isArray(yUnit.value) ? yUnit.value : [yUnit.value]; // 添加这行获取单位数组

  // 对于饼图，如果有多个y值，创建多个饼图系列
  const series = yKeys.map((yKey, index) => {
    const data = response.data.map((item: any) => {
      let name = "";
      xKeys.forEach((xKey, index) => {
        if (xKey.indexOf("-") > -1) {
          let timeKey = xKey.split("-");
          timeKey.forEach((timeKeyItem, timeKeyIndex) => {
            name += item[timeKeyItem] + (timeKeyIndex === timeKey.length - 1 ? "" : "-");
          });
        } else {
          name += item[xKey] || item.x;
        }
      });
      return {
        name,
        value: item[yKey] || item.y?.[index],
      }
    });

    return {
      name: yKey,
      type: "pie",
      radius:
        yKeys.length > 1
          ? [`${30 - index * 10}%`, `${65 - index * 15}%`] // 多层圆环饼图
          : ["35%", "65%"], // 单层圆环饼图，内圆30%，外圆70%
      data,
      label: {
        show: true,
        formatter: `(${xUnit.value}){b}: {c}${yUnits[index] || ""}`, // 使用对应索引的单位
      },
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      },
    };
  });

  return {
    // title: {
    //   text: props?.config?.label,
    //   left: "center",
    // },
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(255,255,255,0.9)",
      borderColor: "#ccc",
      borderWidth: 1,
      padding: [16, 20],
      textStyle: {
        color: "#333",
      },
      formatter: (params: any) => {
        const unit = yUnits[0] || "";
        return `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${params.color};"></span>${params.name}: ${params.value}${unit} (${params.percent}%)`;
      },
    },
    legend: {
      orient: "horizontal",
      bottom: 0,
      left: "center",
    },
    series,
  };
}

// 修改折线图配置
function convertToEChartsLineOption() {
  const response: any = toRaw(props.config);
  const xKeys = Array.isArray(xAxisAlias.value)
    ? xAxisAlias.value
    : [xAxisAlias.value];
  const yKeys = Array.isArray(yAxisAlias.value)
    ? yAxisAlias.value
    : [yAxisAlias.value];
  const yUnits = Array.isArray(yUnit.value) ? yUnit.value : [yUnit.value];

  const { xData, seriesData } = processMultiData(
    response.data || [],
    xKeys,
    yKeys
  );

  return {
    // title: {
    //   text: props?.config?.label,
    //   left: "center",
    // },
    tooltip: {
      trigger: "item", // 改为item触发
      axisPointer: {
        type: "shadow",
      },
      backgroundColor: "rgba(255,255,255,0.9)",
      borderColor: "#ccc",
      borderWidth: 1,
      padding: [16, 20],
      textStyle: {
        color: "#333",
      },
      formatter: (params: any) => {

        // 获取当前hover的数据索引
        const dataIndex = params.dataIndex;
        // 构建显示的结果字符串
        let result = `${params.name}<br/>`;

        // 遍历所有的y轴数据系列
        yKeys.forEach((key, index) => {
          const value = seriesData[index][dataIndex];
          const currentUnit = yUnits[index] || "";

          result += `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background-color:${params.color};"></span>`;
          result += `${key}：${value}${currentUnit}<br/>`;
        });

        return result;
      },
    },
    legend: {
      data: yKeys,
      bottom: 0,
    },
    xAxis: {
      type: "category",
      data: xData,
      name: xUnit.value
    },
    yAxis: {
      type: "value",
      name: yUnits[0],
      // axisLabel: {
      //   formatter: `{value}${yUnit.value}`
      // }
    },
    series: yKeys.map((key, index) => ({
      name: key,
      type: "line",
      data: seriesData[index],
      label: {
        show: true,
        position: "top",
        formatter: `{c}${yUnits[index]}`,
      },
    })),
    grid: {
      containLabel: true,
      left: "3%",
      right: "10%",
      bottom: "10%",
    },
  };
}

// 表格类型转换
function handleTableDataChange(): any[] {
  const response: any = cloneDeep(toRaw(props.config.data));
  // console.log(response, "response");
  // 获取表格专用title 属性
  const dataHeader: string[] = tableHeader.value;
  tableData.value = [];
  // 如果存在表格专用title 属性，则进行表格类型转换 生成表头
  if (dataHeader.length > 0) {
    // 根据title 转换key，默认第一个为x轴 第二个为y轴
    tableData.value = response.map((item: any) => {

      dataHeader.forEach((key: string) => {
        // 不存在的删除，时间格式的单独处理
        if (key.indexOf("-") > -1) {
          let timeKey = key.split("-");
          item[key] = "";

          timeKey.forEach((timeKeyItem, timeKeyIndex) => {
            // item[timeKeyItem] = item[timeKeyItem] || item.x;
            item[key] += item[timeKeyItem] + (timeKeyIndex === timeKey.length - 1 ? "" : "-");
          });
        } else if (!item[key]) {
          // 不存在 删除
          delete item[key];
        }
      });
      return item;
      // Object.keys(item).forEach((key: string, index: number) => {
      //   if (!dataHeader.includes(key)) {
      //     // item[key] = item[key];
      //     delete item[key];
      //   }
      // });
      // return item;
    });
  }
  return toRaw(tableData.value);
}

// 图表类型切换事件
function handleChartTypeChange(value: string) {
  if (value && !!showData.value.length) {
    toggleChart(value);
  }
}

function toggleChart(chartType: string) {
  // 无类型或dom无获取时不处理
  if (!chartType || !chartDiagram.value) return;

  // 初始化echarts实例
  nextTick(() => {
    // 如果已存在实例，先销毁
    if (chartInstance.value) {
      chartInstance.value.dispose();
    }
    // 重新初始化实例
    chartInstance.value = echarts.init(chartDiagram.value);
    try {
      switch (chartType) {
        case "bar":
          chartInstance.value.setOption(convertToEChartsOption());
          break;
        case "pie":
          chartInstance.value.setOption(convertToEChartsPieOption());
          break;
        case "line":
          chartInstance.value.setOption(convertToEChartsLineOption());
          break;
        case "table":
          handleTableDataChange();
          // console.log(props.config, "props.config table");
          break;
      }
    } catch (error) {
      // console.log(error, "error");
    }
  });
}

// 移除 watchEffect，改用 watch 监听 props.config
watch(() => props.config, (newConfig, oldPosition) => {
  // 只在配置初始化或配置变化时触发，而不是在用户手动切换时触发
  if (newConfig?.data && newConfig?.chart_type) {
    // 判断新旧是否相同
    if (JSON.stringify(newConfig) !== JSON.stringify(oldPosition)) {
      // 初始化时设置图表类型
      curChartType.value = newConfig.chart_type;
      nextTick(() => {
        toggleChart(newConfig.chart_type);
      });
    }
  }
}, { immediate: true, deep: true, flush: 'post' });

// 计算滑块位置
const sliderStyle = computed(() => {
  const index = chartTypeCheckMap.value.findIndex(
    (item) => item.value === curChartType.value
  );
  return {
    transform: `translateX(calc(${index * 100}% + ${index <= 0 ? 2 : 0}px))`,
    width: `${100 / chartTypeCheckMap.value.length}%`,
  };
});

// 获取x单位
const xUnit = computed(() => {
  return props.config["x-unit"] || "";
});

// 获取y单位
const yUnit = computed(() => {
  return props?.config?.data ? props?.config["y-unit"]?.split(",") : [];
});

// 获取y轴的 显示值
const yDisplayValue = computed(() => {
  return props.config["y-value-field"] || "";
});

// 获取表头
const tableHeader = computed(() => {
  let xAxisAliasList = xAxisAlias.value
    ? Array.isArray(xAxisAlias.value)
      ? xAxisAlias.value
      : [xAxisAlias.value]
    : [];
  let yAxisAliasList = yAxisAlias.value
    ? Array.isArray(yAxisAlias.value)
      ? yAxisAlias.value
      : [yAxisAlias.value]
    : [];
  return [...xAxisAliasList, ...yAxisAliasList];
});

// 获取x轴别名
const xAxisAlias = computed(() => {
  return props?.config["x-title"]?.split(",");
});

// 获取y轴别名
const yAxisAlias = computed(() => {
  return props?.config["y-title"]?.split(",");
});

// 获取显示的数据
const showData = computed(() => {
  return props?.config?.data || []
})


onMounted(() => { });
onUpdated(() => {
  // curChartType.value = "";
  // console.log(props.config, "props.config");
});
</script>
<style lang="scss" scoped>
.chartDiagram {
  width: 100%;
  height: 400px;
  position: relative;
  overflow: visible; // 确保tooltip可以溢出容器
}

:deep(.chart-type-radio-group) {
  position: relative;
  display: inline-block;
  background: #ebf3fd;
  border-radius: 4px;
  padding: 2px;
  margin-bottom: 16px;

  .slider {
    position: absolute;
    top: 2px;
    left: 0px;
    height: calc(100% - 4px);
    background: #005ee0;
    border-radius: 4px;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
  }

  .el-radio-group {
    display: flex;
    position: relative;
    z-index: 2;
  }

  .el-radio-button {
    margin: 0;

    &__inner {
      border: none;
      background: transparent;
      padding: 8px 10px;
      height: 32px;
      line-height: 1;
      font-size: 14px;
      color: #606266;
      transition: all 0.3s;

      &:hover {
        color: #409eff;
      }
    }

    &.is-active {
      .el-radio-button__inner {
        color: #fff;
        background: transparent;
        box-shadow: none;
      }
    }

    &:not(:first-child)::before {
      display: none;
    }
  }
}

.chart-diagram-container {
  display: flex;
  flex-direction: column;
  align-items: end;
  // align-items: center;
  // justify-content: space-between;
}

.table-container {
  width: 100%;
}

.chart-diagram-container-header {
  width: 100%;

  &-title {
    text-align: left;
    font-weight: 600;
    font-size: 13px;
    color: #262626;
    line-height: 16px;
  }
}
</style>
