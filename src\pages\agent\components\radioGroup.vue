<template>
  <div class="radio-group-container">
    <span
      class="radio-child"
      v-for="item in visOptions"
      :class="{ 'active-child': item[props.valueAlias] === model }"
      :key="item[props.valueAlias]"
      @click="handleClick(item)"
    >
      {{ item[props.labelAlias] }}
    </span>
  </div>
</template>
<script lang="ts" setup>
import { ref, defineProps, defineModel, watchEffect, defineEmits } from "vue";
import { cloneDeep } from "lodash-es";
const props = defineProps({
  options: {
    type: Array<object>,
    default: () => [],
  },
  labelAlias: {
    type: String,
    default: "label",
  },
  valueAlias: {
    type: String,
    default: "value",
  },
});
const emit = defineEmits(["change"]);
const model = defineModel<string>();
let visOptions = ref<object[]>([]);

// 点击时修改值、触发change事件
function handleClick(item: any): void {
  model.value = item[props.valueAlias];
  emit("change", model.value);
}

watchEffect(() => {
  visOptions.value = cloneDeep(props.options);
});
</script>
<style lang="scss" scoped>
.radio-group-container {
  display: flex;
  align-items: center;
  border-radius: 6px;
  background: #f2f2f2;

  .radio-child {
    font-size: 13px;
    color: #262626;
    padding: 0px 10px;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    text-align: center;
  }
  .active-child {
    background: #005ee0;
    color: #ffffff;
    border-radius: 6px;
  }
  //   gap: 10px;
}
</style>

