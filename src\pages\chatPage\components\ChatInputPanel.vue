<template>
  <div class="chat-input-panel">
    <div class="input-container" :class="{ 'input-focused': isFocus }">
      <!-- 工具栏 -->
      <!-- <ToolsBar :ctrl="props.ctrl" ref="ToolsBarRef" /> -->
      <!-- 输入框 -->
      <!-- <el-input
        v-model="userInput"
        type="textarea"
        :rows="4"
        resize="none"
        placeholder="在这里输入消息..."
        class="chat-input"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeyDown"
        @compositionstart="handleCompositionStart"
        @compositionend="handleCompositionEnd"
        @keyup.enter="handleSubmit"
      /> -->

      <!-- 联想输入框 -->
      <div
        class="suggestion-input"
        contenteditable="true"
        ref="inputRef"
        placeholder="在这里输入消息..."
        @input="handleSuggestionInput"
        @keyup.enter="handleSubmit"
      ></div>

      <!-- 联想建议列表 -->
      <div
        v-if="suggestions.length > 0"
        class="suggestions-list"
        :class="{ 'position-bottom': suggestionPosition.top < 0 }"
        :style="suggestionStyle"
      >
        <div
          v-for="(item, index) in suggestions"
          :key="index"
          class="suggestion-item"
          :class="[
            { active: currentIndex === index },
            getSuggestionClass(item.type),
          ]"
          @mouseenter="currentIndex = index"
          @click="selectSuggestion(item)"
        >
          <el-tooltip
            :content="item?.metadata?.alias || item.enum_value || item.value"
          >
            <span class="field">{{
              item?.metadata?.alias || item.enum_value || item.value
            }}</span>
          </el-tooltip>
          <span class="field-desc">{{
            item?.metadata?.comment || item.field_name
          }}</span>
        </div>
      </div>

      <div class="tip">
        <div class="probe-container">
          <span>开启追问:</span>
          <el-switch v-model="isProbe" />
          <span>开启输入联想:</span>
          <el-switch
            v-model="isInputSuggest"
            @change="toggleInputSuggest"
            active-value="true"
            inactive-value="false"
          />
        </div>

        <span class="tip-container">内容由AI大模型生产，请仔细甄别</span>
        <!-- 内容由AI大模型生产，请仔细甄别 -->
      </div>
      <!-- 发送按钮 -->
      <el-button
        type="primary"
        class="send-button"
        :disabled="getSendDisabled"
        :loading="replyLoading"
        @click="handleSubmit"
        icon="Position"
      >
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UserChatContent, ChatHistoryResponse } from "@@/apis/chat/type";
import { computed, inject, nextTick, ref } from "vue";
import { ElButton, ElInput, ElMessage } from "element-plus";
// import ToolsBar from './ToolsBar.vue'
import { v4 as uuidv4 } from "uuid";
import { clone, throttle } from "lodash-es";
import { getSuggestionInputApi } from "@@/apis/chat";
const conv_uid = uuidv4();

interface Props {
  ctrl?: AbortController;
}

const props = withDefaults(defineProps<Props>(), {
  ctrl: undefined,
});

const ToolsBarRef = ref();
// 注入的数据和方法
const scrollRef = inject<any>("scrollRef");
const replyLoading = inject<any>("replyLoading", false);
const canAbort = inject<boolean>("canAbort", false);
const handleChat =
  inject<
    (content: UserChatContent, data?: Record<string, any>) => Promise<void>
  >("handleChat");
const appInfo = inject<any>("appInfo", {});
const currentDialogue = inject<any>("currentDialogue", {});
const temperatureValue = inject<any>("temperatureValue", 0.6);
const maxNewTokensValue = inject<any>("maxNewTokensValue", 4000);
const resourceValue = inject<any>("resourceValue");
const setResourceValue = inject<(value: any) => void>("setResourceValue");
const refreshDialogList = inject<() => void>("refreshDialogList");
const history = inject<ChatHistoryResponse[]>("history");
const selectedAgent = inject<any>("selectedAgent"); // 获取当前选中的agent
const isProbe = ref(true);
const isInputSuggest = ref("true");
// 响应式数据
const userInput = ref("");
const isFocus = ref(false);
const isZhInput = ref(false);
const submitCount = ref(0);

// 新增的响应式数据
const suggestions = ref<any[]>([]);
const inputRef = ref<HTMLDivElement | null>(null);
const currentIndex = ref(-1);
// 获取发送按钮是否禁用
const getSendDisabled = ref(true);

// 输入联想变更
const toggleInputSuggest = () => {
  window.localStorage.setItem("isInputSuggest", isInputSuggest.value);
};

// 更新发送按钮是否禁用
const updateSendDisabled = () => {
  let text = inputRef.value?.innerText;
  getSendDisabled.value = !text || !text.trim() || replyLoading.value;
};

// 获取建议项的样式类
const getSuggestionClass = (type: string) => {
  return `${type}-item`;
};

// 首先添加一个用于存储建议框位置的响应式数据
const suggestionPosition = ref({ top: 0, left: 0 });

// 添加一个计算建议框位置的函数
const calculateSuggestionPosition = () => {
  if (!inputRef.value) return;

  const selection = window.getSelection();
  const range = selection?.getRangeAt(0);

  if (range) {
    // 获取光标位置的客户端矩形
    const rect = range.getBoundingClientRect();
    // 获取输入框的位置信息
    const inputRect = inputRef.value.getBoundingClientRect();

    // 建议框的高度（可以根据实际情况调整）
    const suggestionBoxHeight =
      suggestions.value.length * 32 > 200 ? 200 : suggestions.value.length * 32;

    // 计算建议框的位置
    // 将建议框底部放在光标上方，所以用光标的top减去建议框高度
    const top = rect.top - inputRect.top - suggestionBoxHeight; // 减5px留出间距

    // 水平位置尽量与光标对齐，但不超出输入框边界
    let left = rect.left - inputRect.left;
    const maxLeft = inputRect.width - 300; // 建议框宽度
    const finalLeft = Math.min(Math.max(0, left), maxLeft);

    // 如果建议框会超出输入框顶部，则显示在光标下方
    // if (top < 0) {
    //   suggestionPosition.value = {
    //     top: rect.bottom - inputRect.top - 5, // 光标底部位置加5px间距
    //     left: finalLeft,
    //   };
    // } else {
    suggestionPosition.value = {
      top,
      left: finalLeft,
    };
    // }
  }
};

// 修改建议框的样式
const suggestionStyle: any = computed(() => ({
  position: "absolute",
  top: `${suggestionPosition.value.top}px`,
  left: `${suggestionPosition.value.left}px`,
  zIndex: 1000,
}));

// 获取数据集
const dataset_ids = computed(() => {
  let same = (selectedAgent.value?.param_need || []).find(item => item.type === "dataset");
  return same.value
})


// 使用lodash节流处理输入事件
const handleSuggestionInput = throttle(async (event: Event) => {
  const target = event.target as HTMLDivElement;
  const inputValue = target.innerHTML;
  const { app_code, } = selectedAgent.value;

  updateSendDisabled();
  if (inputValue && isInputSuggest.value === "true") {
    try {
      getSuggestionInputApi({
        user_input: processInputText(inputValue),
        dataset_ids: dataset_ids.value,
        app_code,
      }).then((res: any) => {
        suggestions.value = res?.data.suggestions;
        !!suggestions.value.length && calculateSuggestionPosition();
      });
    } catch (error) {
      console.error("获取联想建议失败:", error);
    }
  } else {
    suggestions.value = [];
  }
}, 600);

const selectSuggestion = (suggestion: any) => {
  if (!inputRef.value) return;
  let selectText =
    suggestion.metadata?.alias || suggestion.enum_value || suggestion.value;
  try {
    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);
    if (!selection || !range || !inputRef.value.textContent?.trim()) {
      // 直接添加到 HTML 内容末尾
      inputRef.value.innerHTML =
        (inputRef.value.innerHTML || "") +
        `<span data-type="${suggestion.type}">${selectText}</span>&nbsp;`;
    } else {
      // 获取光标位置的文本匹配信息
      const matchInfo = getTextMatchAtCursor(inputRef.value, range, selectText);

      if (matchInfo.hasMatch) {
        // 有匹配文本，替换匹配部分
        const beforeMatch = inputRef.value.innerHTML.substring(
          0,
          matchInfo.startPos
        );
        const afterMatch = inputRef.value.innerHTML.substring(matchInfo.endPos);

        // 如果是中间匹配，需要保留selectText中匹配部分之前的内容
        let replacementText = selectText;
        // 如果需要只替换匹配的部分，可以根据需求调整这里

        const newContent =
          beforeMatch +
          `<span data-type="${suggestion.type}">${replacementText}</span>&nbsp;` +
          afterMatch;

        inputRef.value.innerHTML = newContent;
      } else {
        // 没有匹配文本，在光标位置插入
        const cursorPos = getAbsoluteCaretPosition(inputRef.value, range);
        const beforeCursor = inputRef.value.innerHTML.substring(0, cursorPos);
        const afterCursor = inputRef.value.innerHTML.substring(cursorPos);

        const newContent =
          beforeCursor +
          `<span data-type="${suggestion.type}">${selectText}</span>&nbsp;` +
          afterCursor;

        inputRef.value.innerHTML = newContent;
      }
    }

    // 等待 DOM 更新后设置光标位置
    nextTick(() => {
      if (!inputRef.value) return;
      const spans = inputRef.value.getElementsByTagName("span");
      const lastSpan = spans[spans.length - 1];

      if (lastSpan) {
        const selection = window.getSelection();
        const range = document.createRange();

        // 确保在span后面有空格节点
        let spaceNode = lastSpan.nextSibling;
        if (!spaceNode || spaceNode.nodeType !== Node.TEXT_NODE) {
          spaceNode = document.createTextNode(" ");
          lastSpan.parentNode?.insertBefore(spaceNode, lastSpan.nextSibling);
        }

        // 设置光标位置到空格后
        range.setStart(spaceNode, 1);
        range.collapse(true);
        selection?.removeAllRanges();
        selection?.addRange(range);
        inputRef.value.focus();
      }
    });

    suggestions.value = [];
  } catch (error) {
    console.error("插入文本时发生错误:", error);
    // 降级处理
    if (inputRef.value) {
      inputRef.value.innerHTML =
        (inputRef.value.innerHTML || "") +
        `<span data-type="${suggestion.type}">${selectText}</span>&nbsp;`;

      nextTick(() => {
        if (!inputRef.value) return;
        inputRef.value.focus();
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(inputRef.value);
        range.collapse(false);
        selection?.removeAllRanges();
        selection?.addRange(range);
      });
    }
    suggestions.value = [];
  }
};

// 新增函数：获取光标位置的文本匹配信息
const getTextMatchAtCursor = (
  element: HTMLElement,
  range: Range,
  selectText: string
) => {
  // 获取光标在HTML中的绝对位置
  const cursorPos = getAbsoluteCaretPosition(element, range);

  // 获取完整的HTML内容
  const fullHTML = element.innerHTML;

  // 获取光标前的所有HTML内容
  const htmlBeforeCursor = fullHTML.substring(0, cursorPos);

  // 创建临时元素来获取纯文本内容
  const tempElement = document.createElement("div");
  tempElement.innerHTML = htmlBeforeCursor;
  const textBeforeCursor = tempElement.textContent || "";

  // 计算需要检查的文本长度（不超过selectText的长度和textBeforeCursor的长度）
  const checkLength = Math.min(selectText.length, textBeforeCursor.length);

  // 获取需要检查的文本（光标前的部分）
  const textToCheck = textBeforeCursor.slice(-checkLength);

  // 查找最长匹配，支持中间匹配
  let matchLength = 0;
  let matchedPart = ""; // 记录匹配的部分

  // 遍历textToCheck的所有后缀，检查是否在selectText中存在
  for (let len = textToCheck.length; len > 0; len--) {
    const suffix = textToCheck.substring(textToCheck.length - len);
    // 检查suffix是否是selectText的子字符串
    if (selectText.includes(suffix)) {
      matchLength = len;
      matchedPart = suffix;
      break;
    }
  }

  if (matchLength > 0) {
    // 找到匹配，需要计算在HTML中的实际位置
    const matchedText = textToCheck.slice(-matchLength);

    // 计算匹配文本在HTML中的起始位置
    let htmlPos = cursorPos;
    let textCount = 0;
    let inTag = false;

    // 从光标位置向前查找，计算纯文本字符数
    for (let i = cursorPos - 1; i >= 0; i--) {
      const char = fullHTML[i];

      // 处理HTML标签
      if (char === ">") {
        inTag = true;
      } else if (char === "<") {
        inTag = false;
        continue;
      }

      // 只统计不在标签内的字符
      if (!inTag && char !== undefined) {
        textCount++;
        // 当统计到足够的字符时，记录位置
        if (textCount === matchLength) {
          htmlPos = i;
          break;
        }
      }
    }

    return {
      hasMatch: true,
      matchText: matchedText,
      startPos: htmlPos,
      endPos: cursorPos,
      matchLength,
      matchedPart, // 返回匹配的部分
    };
  }

  return {
    hasMatch: false,
    matchText: "",
    startPos: 0,
    endPos: 0,
    matchLength: 0,
    matchedPart: "",
  };
}; // 获取光标在整个输入框中的绝对位置
const getAbsoluteCaretPosition = (
  element: HTMLElement,
  range: Range
): number => {
  // 创建一个范围，从元素开始到光标位置
  const preRange = document.createRange();
  preRange.selectNodeContents(element);
  preRange.setEnd(range.startContainer, range.startOffset);

  // 获取该范围的HTML内容
  const clonedContents = preRange.cloneContents();

  // 创建临时元素以获取准确的HTML字符串
  const temp = document.createElement("div");
  temp.appendChild(clonedContents);

  // 计算HTML字符串长度（包含所有标签）
  return temp.innerHTML.length;
};

const setInputValue = (value: string) => {
  inputRef.value.innerHTML = value;
  nextTick(() => {
    updateSendDisabled();
  });
};

// 添加一个处理点击事件的函数
const handleClickOutside = (event: MouseEvent) => {
  // 检查点击事件是否发生在建议框或输入框之外
  const target = event.target as HTMLElement;
  if (
    inputRef.value &&
    !inputRef.value.contains(target) &&
    !target.closest(".suggestions-list")
  ) {
    suggestions.value = [];
  }
};
// 输入联想建议 没有默认开启，有的话以上次设置为准
const initInputSuggest = () => {
  let isInputSuggestCache = window.localStorage.getItem("isInputSuggest");
  if (isInputSuggestCache === null) {
    window.localStorage.setItem("isInputSuggest", isInputSuggest.value);
  } else {
    isInputSuggest.value = isInputSuggestCache;
  }
};

// 在组件挂载时添加事件监听
onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  initInputSuggest();
  // console.log(selectedAgent.value, 89321893129)
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
});

defineExpose({
  userInput,
  setInputValue,
});
// 处理焦点
function handleFocus() {
  isFocus.value = true;
}

function handleBlur() {
  isFocus.value = false;
}

// 处理中文输入
function handleCompositionStart() {
  isZhInput.value = true;
}

function handleCompositionEnd() {
  isZhInput.value = false;
}

// 处理键盘事件
function handleKeyDown(event: KeyboardEvent) {
  if (event.key === "Enter") {
    if (event.shiftKey) {
      return;
    }
    if (isZhInput.value) {
      return;
    }
    event.preventDefault();
    if (!userInput.value.trim() || replyLoading) {
      return;
    }
    handleSubmit();
  }
}

// 处理中断
function handleAbort() {
  if (props.ctrl) {
    props.ctrl.abort();
  }
}

// 滚动到底部
function scrollToBottom() {
  nextTick(() => {
    if (scrollRef?.value?.scrollRef) {
      scrollRef?.value?.scrollRef.scrollTo({
        top: scrollRef?.value?.scrollRef.scrollHeight,
        behavior: "smooth",
      });
    }
  });
}
// 处理输入文本，将带data-type的span标签转换为特定格式
const processInputText = (html: string): string => {
  if (!html) return "";

  // 创建一个临时的div来解析HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = html;

  // 获取所有带data-type的span标签
  const spans = tempDiv.getElementsByTagName("span");
  let processedText = html;

  // 从后向前处理，避免替换时位置变化的问题
  for (let i = spans.length - 1; i >= 0; i--) {
    const span = spans[i];
    const dataType = span.getAttribute("data-type");
    if (dataType) {
      // 构建替换的文本格式
      const replacement = `{\"${dataType}\":\"${span.textContent}\"}`;

      // 使用正则表达式替换整个span标签
      const spanRegex = new RegExp(
        `<span[^>]*data-type=["']${dataType}["'][^>]*>${span.textContent}</span>\\s*`,
        "g"
      );
      processedText = processedText.replace(spanRegex, replacement);
    }
  }

  // 清理其他HTML标签（如div、br等）
  processedText = processedText
    .replace(/<div><br><\/div>/g, "\n") // 替换换行
    .replace(/<br\s*\/?>/g, "\n") // 替换单个br
    .replace(/<[^>]+>/g, "") // 移除所有剩余的HTML标签
    .replace(/&nbsp;/g, " ") // 替换&nbsp;为普通空格
    .trim(); // 移除首尾空白

  return processedText;
};
// 提交消息
async function handleSubmit() {
  if (!handleChat) {
    ElMessage.error("聊天功能未初始化");
    return;
  }
  // 获取用户输入的内容
  let inputText = inputRef.value?.innerText;
  inputText = inputText.trim();
  if (!inputText.trim()) {
    return;
  }
  // 获取最新的一条用户消息
  let latestHumenMsg = (history as any)?.value.at(-2);
  // 获取第一条用户消息
  let firstHumenMsg = (history as any)?.value[0];
  submitCount.value++;
  // 清空输入框内容
  inputRef.value!.innerHTML = "";
  // 滚动到底部
  scrollToBottom();
  try {
    // 处理资源值
    let newUserInput: UserChatContent;
    if (resourceValue.value && resourceValue.value.length > 0) {
      // 如果不是Excel场景，清空资源值
      if (currentDialogue.chat_mode !== "chat_excel") {
        setResourceValue?.(null);
      }

      const messages = [...resourceValue.value];
      messages.push({
        type: "text",
        text: inputText,
      });

      newUserInput = {
        role: "user",
        content: messages,
      };
    } else {
      newUserInput = inputText;
    }
    // console.log(currentDialogue.value, latestHumenMsg?.conv_uid, firstHumenMsg?.dialog_id, 'input ')
    // 构建参数
    const chatData: Record<string, any> = {
      app_code: appInfo.app_code || "",
      temperature: temperatureValue.value,
      max_new_tokens: maxNewTokensValue.value,
      // model_name: "Qwen2.5-72B-Instruct-AWQ",
      // conv_uid: ToolsBarRef.value?.isProbe ? crypto.randomUUID() : ''
      // conv_uid:crypto.randomUUID()
      // model_name: ToolsBarRef.value?.selectedModel || "qwen2.5-72b-instruct",
      // 如果是追问模式，默认以当前dialog中的conv_uid为准，否则以最新的一条用户消息的conv_uid为准
      // 非追问模式下 每次默认为最新的一条用户消息的conv_uid
      conv_uid: isProbe.value
        ? currentDialogue?.value?.conv_uid ||
          latestHumenMsg?.conv_uid ||
          uuidv4() ||
          ""
        : uuidv4(),
      dialog_id:
        currentDialogue?.value?.dialog_id ||
        firstHumenMsg?.dialog_id ||
        uuidv4() ||
        "",
    };

    if (resourceValue.value) {
      chatData.select_param =
        typeof resourceValue.value === "string"
          ? resourceValue
          : JSON.stringify(resourceValue.value) || currentDialogue.select_param;
    }

    await handleChat(newUserInput, chatData);

    // 如果是第一次对话，刷新对话列表
    if (submitCount.value === 1) {
      refreshDialogList?.();
    }
  } catch (error) {
    // console.error("发送消息失败:", error)
    ElMessage.error("发送消息失败，请重试");
  }
}
</script>

<style lang="scss" scoped>
.chat-input-panel {
  display: flex;
  width: 100%;
  flex-direction: column;
  // width: 83.333333%;
  /* w-5/6 */
  // margin: 0 auto;
  padding: 1rem calc(10rem + 50px);
  background: transparent;
  position: relative;
}

.input-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  background: #fcfcfc;
  padding: 1rem 1.25rem;
  padding-top: 0.5rem;
  padding-bottom: 0.75rem;
  border-radius: 0.75rem;
  position: relative;
  border: 1px solid #e5e7eb;
  transition: border-color 0.2s;

  &.input-focused {
    border-color: #0c75fc;
  }

  .dark & {
    background: rgba(255, 255, 255, 0.16);
    border-color: rgba(255, 255, 255, 0.6);
  }
  .tip {
    display: flex;
    align-items: center;
    margin-right: 50px;
    .probe-container {
      display: flex;
      align-items: center;
      gap: 10px;
      flex: 1;
    }
    .tip-container {
      color: #ccc;
    }
  }
}

.chat-input {
  :deep(.el-textarea__inner) {
    border: none;
    padding: 0;
    box-shadow: none;
    background: transparent;
    resize: none;
    &::placeholder {
      color: #ccc;
    }
    &:focus {
      box-shadow: none;
    }
  }
}

.send-button {
  position: absolute;
  right: 1rem;
  bottom: 0.75rem;
  width: 32px;
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  color: #fff;
  background: linear-gradient(136deg, #0072eb, #0031fb);
  border-radius: 8px;
  opacity: 1;
  padding-left: 14px;
  &.is-disabled,
  &:hover {
    background: linear-gradient(136deg, #0072eb, #0031fb);
    opacity: 0.4;
  }
}

.suggestion-input {
  min-height: 100px;
  width: 100%;
  // border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px 5px;
  margin-bottom: 10px;
  outline: none;
  position: relative;
  &:empty::before {
    content: attr(placeholder);
    color: #999;
    position: absolute;
    // left: 5px;
    // top: 10px;
    pointer-events: none; // 确保不会影响输入
  }
  &:focus {
    border-color: #409eff;
  }
  :deep([data-type]) {
    user-select: all;
    -webkit-user-modify: read-only;
    -moz-user-modify: read-only;
    cursor: default;
    display: inline-block;
    pointer-events: none;
  }

  :deep([data-type="indicator"]) {
    color: #2bbf79;
  }

  :deep([data-type="enum"]) {
    color: #00b7f5;
  }

  :deep([data-type="history"]) {
    color: #ff9602;
  }
}

.suggestions-list {
  position: absolute; // 改为absolute定位
  width: 340px;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-sizing: border-box;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.2s ease-in-out;

  // 添加一个小三角形指示器
  &::after {
    content: "";
    position: absolute;
    bottom: -6px; // 当建议框在上方时
    left: 20px;
    width: 12px;
    height: 12px;
    background: white;
    border: 1px solid #dcdfe6;
    border-width: 0 1px 1px 0;
    transform: rotate(45deg);
  }

  // 当建议框显示在下方时，三角形朝上
  &.position-bottom::after {
    bottom: auto;
    top: -6px;
    border-width: 1px 0 0 1px;
  }
}

.suggestion-item {
  padding: 8px 12px;
  cursor: pointer;
  display: grid;
  grid-template-columns: 2fr 4fr; // 第一列最小1fr最大30%，第二列最小3fr最大70%
  gap: 10px;
  &:hover {
    background-color: #f5f7fa;
  }
  .field {
    font-size: 13px;
    font-weight: 600;
    grid-column: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    text-wrap: nowrap;
  }
  .field-desc {
    font-size: 13px;
    grid-column: 2;
  }
}
.indicator-item {
  .field {
    color: #2bbf79;
  }
}
.enum-item {
  .field {
    color: #00b7f5;
  }
}
.history-item {
  .field {
    color: #ff9602;
  }
}
</style>


