<template>
  <div class="semantic-edit-page">
    <div class="edit-header">
      <div class="edit-header-left">
        <div class="edit-header-title">{{ currentNode?.name }}</div>
        <el-tooltip content="编辑数据模型">
          <i class="iconfont ChatData-bianji" @click="onEditRelated"></i>
        </el-tooltip>
        <JoinMethodSelector v-model="joinMethod" class="mr5" :disabled="false" />
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handleCancel">取消</el-button>
      </div>
      <div class="edit-header-desc">
        {{ currentNode?.description || "暂无描述" }}
      </div>
    </div>
    <div class="edit-main" @drop="onDrop">
      <!-- 左侧数据连接和原始表 -->
      <div class="edit-sidebar">
        <div class="sidebar-section">
          <div class="sidebar-title flex-row">
            <span class="flex1">数据连接</span>
            <el-popover placement="bottom-start" :width="240" trigger="click">
              <div class="add-pop-title">添加数据连接</div>
              <el-input
                placeholder="输入关键字..."
                prefix-icon="Search"
                clearable
                v-model="keyword"
                @change="onSearch"
              />
              <div class="datasource-list">
                <div
                  class="item ellipsis"
                  v-for="(item, idx) in connectionOptions"
                  :key="idx"
                >
                  {{ item.type }}
                </div>
              </div>
              <template #reference>
                <el-button
                  :disabled="connList.length ? true : false"
                  class="add-btn"
                  type="primary"
                  :icon="Plus"
                  circle
                />
              </template>
            </el-popover>
          </div>
          <el-menu
            :default-active="activeConn"
            @select="handleConnSelect"
            class="conn-menu"
          >
            <el-menu-item
              v-for="conn in connList"
              :key="conn.id"
              :index="conn.id"
              >{{ conn.params.database }}</el-menu-item
            >
          </el-menu>
        </div>
        <div class="sidebar-section">
          <div class="sidebar-title">原始表</div>
          <el-input v-model="sourceTableSearch" prefix-icon="Search" class="gray-inp-bg" placeholder="输入关键字..." style="padding: 0px 0px 10px 10px;" />
          <!-- <el-menu class="table-menu">
            <el-menu-item
              v-for="table in tableList"
              :key="table.table_id"
              :index="table.table_id"
              draggable="true"
              @dragstart="handleTableDragStart(table, $event)"
            >
              {{ table.table_name }}
            </el-menu-item>
          </el-menu> -->
          <div
            class="drag-sheet-node ellipsis"
            v-for="table in sourceTableFilterData"
            :key="table.table_id"
            :draggable="true"
            @dragstart="onDragStart($event, table)"
          >
            {{ table.table_name }}
          </div>
        </div>
      </div>
      <div class="edit-content">
        <!-- 右侧图表编排区 -->
        <div
          class="edit-diagram-area"
          @dragover.prevent
          :style="{ flex: `1 1 auto`, minHeight: '80px' }"
        >
          <ChartDiagram
            ref="chartDiagramRef"
            @dragover="throttledOnDragOver"
            @dragleave="onDragLeave"
            @rename="onRename"
            @change="onDiagramChange"
            @paneMouseLeave="onPaneMouseLeave"
          />
          <div
            v-if="!!action"
            class="drop-actions flex-row"
            :style="dropActionsStyle"
          >
            <span class="item" :class="{ active: action === 'merge' }">
              <i class="iconfont ChatData-hebing"></i>合并
            </span>
            <span class="item" :class="{ active: action === 'link' }">
              <i class="iconfont ChatData-guanlian-neibu"></i>关联
            </span>
          </div>
        </div>
        <div class="drag-bar" @mousedown="handleDragStart">
          <span class="drag-arrow" @click.stop="toggleFieldListHeight">
            <el-icon v-if="fieldListHeight < 1000"><ArrowUpBold /></el-icon>
            <el-icon v-else><ArrowDownBold /></el-icon>
          </span>
        </div>
        <FieldList
          class="edit-field-list"
          :style="{ height: fieldListHeight + 'px' }"
          :dimension="dimensionData"
          :measure="measureData"
          editType="edit"
          ref="fieldListRef"
        />
      </div>
    </div>
  </div>
  <ModelDrawer
      v-if="modelDrawerVisible"
      v-model:visible="modelDrawerVisible"
      :type="modelDrawerType"
      :fromId="fromId"
      :treeData="treeData"
      :ModelData="ModelData"
      @successModel="handleDrawerSuccess"
    />
  <relatedDialog ref="relatedDialogRef" />
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, provide, onUnmounted } from "vue";
// import ChartDiagram from "./components/ChartDiagram.vue";
import FieldList from "./components/FieldList.vue";
import { useRouter, useRoute } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import relatedDialog from "./components/relatedDialog.vue";
import JoinMethodSelector from "./components/JoinMethodSelector.vue";
import { getSemanticModelApi,getSemanticModelTreeApi } from "@/common/apis/semantic";
import {
  getDatasourcesListApi,
  getDatasourceTableListApi,
} from "@/common/apis/llm";
import type { Database } from "@/pages/llm/dataSourceManage/typs";
import { cloneDeep, throttle, uniqueId } from "lodash-es";
import { transformFieldData } from "./config";
// 拖拽相关-start
// import { v4 as uuidv4 } from "uuid";
import ChartDiagram from './chartDiagram/index.vue';
import { useVueFlow } from '@vue-flow/core';
import { updateSemanticModelApi } from "@/common/apis/semantic";
import ModelDrawer from "./components/ModelDrawer.vue";
import { get } from "lodash-es";

const { addNodes, addEdges, removeNodes, removeEdges, screenToFlowCoordinate, onNodesInitialized, updateNode, getViewport } = useVueFlow()
const chartDiagramRef = ref(null);
// 原始表搜索
const sourceTableSearch = ref("");
// 原始表过滤数据
const sourceTableFilterData = computed(() => {
  return tableList.value.filter((item) => item.table_name.includes(sourceTableSearch.value))
})
let dropActionsStyle = ref({
  left: "-200px",
  top: "-200px",
  transform: `scale(1)`
});
let coords = {};
let coords_x = [];
let coords_y = [];
let coords_x_y = {};
let preview_nId = uniqueId("n_preview_");
let preview_eId = uniqueId("e_preview_");
let isDragging = false;
let curDragItem = null;
let isNoFlowNode = false;

function onPaneMouseLeave(){
  isDragging = false;
}
function onDragStart(event, dragItem) {
  isDragging = true;
  curDragItem = dragItem;
  const coordInfo = chartDiagramRef.value.getNodesCoords();
  coords = coordInfo.coords;
  coords_x = coordInfo.coords_x;
  coords_y = coordInfo.coords_y;
  coords_x_y = {};
  Object.keys(coords).forEach((key) => {
    coords_x_y[`${key}`] = Object.keys(coords[key]) || [];
  });
  isNoFlowNode = !coords_x.length;
  console.log(1666, "onDragStart::", coordInfo);
}

let oldTargetNode = { id: "" };
let action = ref("");
let targetNode: any = {};
let newPosition = { x: 0, y: 0 };
function onDragOver(event) {

  event.preventDefault();
  // 获取当前鼠标位置 转化为画布位置
  const position = screenToFlowCoordinate({
    x: event.clientX,
    y: event.clientY,
  });
  console.log(event, position, "onDragOver::")
  const { x, y } = position;
  // 没有节点或  拖拽结束后再触发 不执行
  if (isNoFlowNode || !isDragging) {
    console.log(37733, "拖拽结束, isNoFlowNode::", isNoFlowNode);
    return;
  }

  const range_x_left = x - 60; //连线宽度是120，取一半
  const range_x_right = x + 100; //节点宽度是200

  const range_y_top = y - 10; //节点间的间距20，取一半
  const range_y_bottom = y + 36 + 10; //节点高度是36
  // 获取离的最接近的节点
  targetNode = getClosestNode(
    [range_x_left, range_x_right],
    [range_y_top, range_y_bottom]
  );
  const { x: t_x, y: t_y } = targetNode.position;
  const { x:v_x, y:v_y, zoom:v_zoom } = getViewport();
  let d_l = 0;
  let d_t = 0;
  // 1倍x=50 y=100
  if(v_zoom === 1){
    d_l = t_x + v_x;
    d_t = t_y + v_y - 30;
  }else if(v_zoom > 1){
    d_l = t_x + v_x + 15 * v_zoom;
    d_t = t_y + v_y + 15 * v_zoom;
  }else {
    d_l = v_x;
    d_t = v_y;
  }
  // 2: 192 130
  // 0.5 1.5 125
  dropActionsStyle.value = {
    left: d_l + "px",
    top: d_t + "px",
    transform: `scale(${v_zoom})`
  };
  // 一个节点的范围是x-60,x+200+60,中间点 （x-60+x+200+60）/2= (x+200)/2
  // 合并,关联概览大一点，设置偏移量
  const mid_x = Math.floor((t_x + 200) / 2) - 50;
  if (x < mid_x) {
    action.value = "merge";
  } else {
    // 关联
    action.value = "link";
  }
  if (
    action.value === "link" &&
    targetNode &&
    oldTargetNode.id !== targetNode.id
  ) {
    oldTargetNode = targetNode;
    newPosition = chartDiagramRef.value.getNewNodePostion(targetNode, coords);
    addPreview();
  }
  if (action.value === "merge") {
    removeNodes(preview_nId);
    removeEdges(preview_eId);
  }

}
function onDragLeave(event) {
  event.preventDefault();
  const position = screenToFlowCoordinate({
    x: event.clientX,
    y: event.clientY,
  });
  console.log(1888, "onDragLeave::", position );
  if(position.x < 0){
    removeNodes(preview_nId);
    removeEdges(preview_eId);
    isDragging = false;
    action.value = "";
    oldTargetNode = { id: "" };
    curDragItem = null;
  }

}

function onDrop(event) {
  console.log(1999, "onDrop::", action.value, curDragItem);
  isDragging = false;
  if(isNoFlowNode){
    addNodes({
      id: "1",
      type: "sheet",
      position: { x: 50, y: 50 },
      data: {
        label: curDragItem.table_alias || curDragItem.table_name,
        ...curDragItem,
      },
    });
    fieldListRef.value.addTable(curDragItem.id, "1");
    return;
  }
  if (action.value === "link") {
    const nodeId = uniqueId("n_");
    const edgeId = uniqueId("e_");
    // console.log(29999, "onDrop::", newPosition);
    const newNode = {
      id: nodeId,
      type: "sheet",
      position: {
        x: newPosition.x,
        y: newPosition.y,
      },
      data: {
        label: curDragItem.table_alias || curDragItem.table_name,
        ...curDragItem,
      },
    };
    const newEdge = {
      id: edgeId,
      type: "custom",
      source: targetNode.id,
      target: nodeId,
    };
    addNodes(newNode);
    nextTick(() => {
      fieldListRef.value.addTable(newNode?.data.id, newNode?.id, targetNode.id);
      // console.log(newNode, targetNode.id, 'render new Node')
    })
    addEdges(newEdge);
  }
  removeNodes(preview_nId);
  removeEdges(preview_eId);
  action.value = "";
  curDragItem = null;
  oldTargetNode = { id: "" };
}

function addPreview() {
  addNodes({
    id: preview_nId,
    type: "virtual",
    position: { x: newPosition.x, y: newPosition.y },
    data: { label: "预览" },
  });
  addEdges({
    id: preview_eId,
    type: "virtual",
    source: targetNode.id,
    target: preview_nId,
  });
}

function getClosestNode(range_x, range_y) {
  const in_x = chartDiagramRef.value.findClosestNumber(coords_x, range_x);
  let cols_y = coords_x_y[`${in_x}`];
  if (cols_y.length) {
    const in_y = chartDiagramRef.value.findClosestNumber(cols_y, range_y);
    // console.log(222, in_x, in_y);
    return coords[`${in_x}`][`${in_y}`];
  }
  return null;
}

const throttledOnDragOver = throttle(onDragOver, 200);


// 拖拽相关-end

const router = useRouter();
const route = useRoute();

const connectionOptions = ref([]);
const connList = ref([]);
const activeConn = ref("");
const tableList = ref([]);
const diagramNodes = ref([]);
const currentNode = ref<any>({});
const fieldListHeight = ref(300); // px
const dragging = ref(false);
const joinMethod = ref('explicit'); // 关联方式，默认为显式关联
const startY = ref(0);
const startHeight = ref(0);
const dimensionData = ref([]); // 维度数据
const measureData = ref([]); // 度量数据
const minHeight = 300;
const maxHeight = 1000;
let keyword = ref("");
const curentEditModelData = ref(null);
const relatedDialogRef = ref(null);
// 字段列表ref
const fieldListRef = ref(null);

function handleConnSelect(id) {
  activeConn.value = id;
  // TODO: 根据id请求表数据
  // tableList.value = ...
}
function handleTableDragStart(table, event) {
  event.dataTransfer.setData("table", JSON.stringify(table));
  // nextTick(() => {
  //   fieldListRef.value.removeTable(["2"]);
  // })
  console.log(table, "table start");
}
function handleDrop(event) {
  const table = JSON.parse(event.dataTransfer.getData("table"));
  // TODO: 将表节点添加到diagramNodes
  diagramNodes.value.push({ ...table, x: 100, y: 100 }); // 示例
}
function handleSave() {
  const { getFlowDataToTables,  getGraphDatas } = chartDiagramRef.value;
  let {dimensions, measures} = fieldListRef.value.getFieldData();
  // console.log(fieldListData, "edit save");
  // handleCancel();
  let {tables, tableIds} = getFlowDataToTables()
  let graphDatas = getGraphDatas()
  // 重新拼接参数
  let requestData = {
    ...cloneDeep(currentNode.value),
    tables,
    tableIds,
    graph: graphDatas,
    dimensions,
    measures,
    association_method: joinMethod.value, // 添加关联方式
  }
  console.log(requestData, "edit save");
  updateSemanticModelApi(Number(route.query.id), requestData).then((res: any) => {
    handleCancel();
  })

}
function handleCancel() {
  //跳转到语义建模页面
  router.push({
    path: "SemanticModeling",
    query: route.query,
  });
}

function getModelData() {
  getSemanticModelApi(Number(route.query.id)).then((res: any) => {
    currentNode.value = res.data;
    const { dimensions, measures, tableIds = [], tables = [], graph, association_method } = res.data || {};
    curentEditModelData.value = res.data;
    dimensionData.value = [...transformFieldData(dimensions, tables)];
    measureData.value = [...transformFieldData(measures, tables)];
    // 设置关联方式，如果没有则默认为显式关联
    joinMethod.value = association_method || 'explicit';
    nextTick(() => {
      let {nodes = [], edges = []} = graph;
      // if (!!nodes && !!edges) {
        chartDiagramRef.value.resetFlowChart(nodes, edges)
      // }
    })

    // tableList.value = res.data.tables || []
    console.log(8833, res.data);

    getDatasetLis(); // 获取数据源列表
  });
}
function getDatasetLis() {
  getDatasourcesListApi().then((res) => {
    const response = res as { data: Database[] };
    connectionOptions.value = response?.data || [];
    connList.value =
      (currentNode.value.dataConnectionIds &&
        connectionOptions.value.filter((item) =>
          currentNode.value.dataConnectionIds.includes(item.id)
        )) ||
      [];
    activeConn.value = connList.value[0]?.id || "";
  });
}
onMounted(() => {
  getModelData();
});
function handleDragStart(e) {
  dragging.value = true;
  startY.value = e.clientY;
  startHeight.value = fieldListHeight.value;
  document.body.style.cursor = "ns-resize";
  window.addEventListener("mousemove", handleDragMove);
  window.addEventListener("mouseup", handleDragEnd);
}
function handleDragMove(e) {
  if (!dragging.value) return;
  let newHeight = startHeight.value + (startY.value - e.clientY);
  newHeight = Math.max(minHeight, Math.min(maxHeight, newHeight));
  fieldListHeight.value = newHeight;
}
function handleDragEnd() {
  dragging.value = false;
  document.body.style.cursor = "";
  window.removeEventListener("mousemove", handleDragMove);
  window.removeEventListener("mouseup", handleDragEnd);
}
function toggleFieldListHeight() {
  if (fieldListHeight.value < 1000) {
    fieldListHeight.value = 1000;
  } else {
    fieldListHeight.value = 300;
  }
}

function onSearch() {}

const modelDrawerVisible = ref(false);
const modelDrawerType = ref<"add" | "edit">("add");
const fromId = ref<any>('')
const ModelData = ref<any>({});
const treeData = ref<any>([])
//编辑数据模型
async function onEditRelated() {
  // 编辑数据模型
  const res = await getSemanticModelTreeApi({ workspace_id: route.query.spaceCode || "",});
  treeData.value = get(res, "data", []);
  modelDrawerType.value = "edit";
  modelDrawerVisible.value = true;
  ModelData.value = currentNode.value;
  // relatedDialogRef.value.show();
}
async function handleDrawerSuccess(form: any) {

    await updateSemanticModelApi(ModelData.value.id, form).then((res: any) => {
      if (res.success) {
        ElMessage.success("编辑成功");
      }
    });
  getModelData();
}

function onRename(id, name) {
  console.log(id, name, "onRename::");
  nextTick(() => {
    fieldListRef.value.updateTableData(id, {
      label: name,
      alias: name,
      table_alias: name,
    });
  })
}

function onDiagramChange(data:any[]) {
  nextTick(() => {
    fieldListRef.value.updateTableFieldIndex(data)
  })
}
watch(activeConn, (newVal) => {
  tableList.value = [];
  if (!!newVal) {
    getDatasourceTableListApi(Number(newVal)).then((res: any) => {
      tableList.value = res.data || [];
      nextTick(() => {
        const {tableIds, tables} = currentNode.value;
        // 判断当前图中是否有节点，无节点时。默认新增一个id为1的首节点
        if (!tableIds.length || !tables.length) {
          const item = tableList.value[0];
          addNodes({
            id: "1",
            type: "sheet",
            position: { x: 50, y: 50 },
            data: {
              label: item.table_alias || item.table_name,
              ...item,
            },
          });
          // nextTick(() => {
            fieldListRef.value.addTable(item.id, "1");
          // });
        }
      });
    });
  }
});

provide("curentEditModelData", curentEditModelData);
</script>

<style scoped lang="scss">
.semantic-edit-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.edit-header {
  padding: 10px 20px;
  background: #fff;
  border-bottom: 1px solid #eee;
  .edit-header-left {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    .edit-header-title {
      flex: 1;
      font-size: 16px;
      font-weight: bold;
    }

  }
  .edit-header-desc {
    font-size: 12px;
    color: #999;
  }
  .ChatData-bianji {
    font-size: 16px;
    margin-right: 20px;
    cursor: pointer;
    color: #4c6f88;
  }
}

.edit-main {
  flex: 1;
  display: flex;
  min-height: 0;
}

.edit-sidebar {
  width: 220px;
  background: #fcfcfc;
  border-right: 1px solid #eee;
  /* padding: 12px 0; */
  padding: 0 10px;
  display: flex;
  flex-direction: column;
}

.sidebar-section {
  margin-bottom: 24px;
  :deep(.el-menu-item) {
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
  }
}

.sidebar-title {
  font-weight: bold;
  font-size: 16px;
  padding: 10px;
  .add-btn {
    width: 20px;
    height: 20px;
  }
}

.drag-sheet-node {
  height: 32px;
  line-height: 32px;
  padding: 0 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  background: #F7F7F7;
  border-left:solid 3px #5167FF;
  cursor: move;
}

.conn-menu,
.table-menu {
  background: transparent;
  border: none;
}
.edit-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.edit-diagram-area {
  background: #f7f8fa;
  position: relative;
  min-width: 0;
  flex: 1 1 auto;
  min-height: 80px;
  .drop-actions {
    gap: 10px;
    position: absolute;
    left: -200px;
    top: -200px;
    .item {
      padding: 4px;
      border-radius: 4px;
      background: #ededed;
      color: #262626;
      &.active {
        background: #ebf3fd;
        color: #005ee0;
        border: solid 1px #005ee0;
      }
      .iconfont {
        font-size: 14px;
        margin-right: 2px;
      }
    }
  }
}

.edit-field-list {
  background: #fff;
  border-top: 1px solid #eee;
  padding: 16px 24px;
  position: relative;
}
.drag-bar {
  height: 8px;
  background: #f0f0f0;
  cursor: ns-resize;
  z-index: 10;
  position: relative;
  .drag-arrow {
    position: absolute;
    right: 16px;
    top: 35px;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #888;
    font-size: 18px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
    padding: 2px;
    transition: background 0.2s;
    &:hover {
      background: #e6f0ff;
    }
  }
}

.add-pop-title {
  padding-bottom: 10px;
  font-weight: 500;
  font-size: 16px;
}
.datasource-list {
  margin-top: 10px;
  max-height: 300px;
  overflow-y: auto;
  .item {
    line-height: 32px;
    cursor: pointer;
    &:hover {
      color: #005ee0;
    }
  }
}
</style>
