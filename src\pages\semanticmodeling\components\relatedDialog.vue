<template>
<el-dialog
  v-model="visible"
  title="关联关系"
  width="500"
>
  <el-form :model="form" :rules="rules" label-position="top">
    <div class="flex-row" style="gap: 10px;">
      <el-form-item class="flex1" label="数据连接" prop="name">
        <el-select v-model="form.name" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item class="flex1" label="数据连接" prop="name">
        <el-select v-model="form.name" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </div>
    <el-form-item label="数据模型名称" prop="name">
      <el-input v-model="form.name" placeholder="请输入" clearable />
    </el-form-item>
    <el-form-item label="描述" prop="desc">
      <el-input v-model="form.desc" type="textarea" :rows="4" resize="none" placeholder="定义描述，以便快速理解" />
    </el-form-item>
  </el-form>
  <template #footer>
    <div class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="visible = false">确定</el-button>
    </div>
  </template>
</el-dialog>
</template>

<script setup lang="ts">

let visible = ref(false);
let form = ref({
  name: '',
  desc: '',
});
let rules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
  ],
}
let options = []

function show() {
  visible.value = true;
}

defineExpose({
  show
})
</script>