export interface ModelList {
  model_name: string;
  worker_type: string;
  [key: string]: any;
}

export interface ApplicationType {
  chat_scene: string;
  scene_name: string;
  param_need: object[];
  [key: string]: any;
}

/**
 * @params
 * status
 *  pending        待定        
    enabled        启用        
    disabled        禁用
 * 
 */
export interface questListItem {
  id: string;
  question: string;
  status: "pending" | "enabled" | "disabled";
  created_at: string;
  [key: string]: any;
}