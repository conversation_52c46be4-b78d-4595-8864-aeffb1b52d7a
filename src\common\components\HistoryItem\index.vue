<script setup lang="ts">
import { Star } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'

interface Props {
  name: string
  active?: boolean
  showStar?: boolean
  showDel?: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  click: []
  del: []
}>()

async function handleDel() {
  try {
    await ElMessageBox.confirm(
      '确定要删除该历史记录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        draggable: true,
        closeOnClickModal: false,
        customClass: 'delete-confirm-dialog'
      }
    )

    emit('del')

  } catch {
    // 用户取消删除，不做任何操作
  }
}
</script>

<template>
  <div class="history-item click-item" :class="{ active: active }" @click="emit('click')">
    <span class="symbol">
      <span class="mark"></span>
    </span>
    <span class="content">{{ name }}</span>
    <el-icon v-if="showStar" class="star-icon">
      <Star />
    </el-icon>
    <slot></slot>
    <i v-if="showDel" class="iconfont ChatData-shanchu del-icon" @click.stop="handleDel"></i>
  </div>
</template>

<style scoped lang="scss">
.history-item {
  height: 36px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  &.active {
    background: #EBF3FD;
    color: #005EE0;

    .mark {
      display: block;
    }
  }

  &:hover {
    background: #EBF3FD;
    color: #005EE0;
  }

  .symbol {
    width: 10px;
  }

  .mark {
    display: none;
    width: 3px;
    height: 18px;
    background: #005EE0;
    border-radius: 0px 100px 100px 0px;
  }

  .content {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .star-icon {
    color: #FFD700;
    margin-left: 4px;
  }
  .del-icon {
    cursor: pointer;
    color: #005EE0;
    font-size: 13px;
    margin-right: 5px;
    display: none;
  }
  &:hover {
    .del-icon {
      display: block;
    }
  }
}
</style>