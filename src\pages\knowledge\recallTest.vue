<script setup lang="ts">
import { getRecallFn, recallTest } from "@/common/apis/knowledge"

interface InitOptions {
  question?: string
  recall_top_k?: number
  recall_retrievers?: string[]
  recall_score_threshold?: number
}

const props = defineProps<{
  name: string
}>()

const formRef = ref() // 添加表单引用
const visible = ref(false)
const resultContent = ref("") // 结果展示
// 表单数据
const formData = ref<InitOptions>({
  question: "",
  recall_top_k: null,
  recall_retrievers: [],
  recall_score_threshold: null
})
// 校验规则
const rules = ref<any>({
  question: [
    {
      required: true,
      message: "请输入问题",
      trigger: "blur"
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (!value || value.trim() === "") {
          callback(new Error("请输入问题"))
        } else {
          callback()
        }
      },
      trigger: "blur"
    }
  ],
  recall_top_k: [
    {
      required: true,
      message: "请输入Topk"
    },
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入正整数",
      trigger: 'blur'
    }
  ]
})
// 方法数据
const fnList = ref<any[]>([])
// 获取回调方法
function handleGetRecallFn() {
  getRecallFn(props.name).then((res: any) => {
    fnList.value = res?.data || []
    formData.value.recall_retrievers = res?.data || []
  })
}
// 开始测试
function handleTest() {
  // console.log("当前表单数据:", formData.value)
  // console.log("question值:", formData.value.question)
  // console.log("question类型:", typeof formData.value.question)
  // console.log("question长度:", formData.value.question?.length)

  // 先进行表单校验
  formRef.value.validate((valid: boolean) => {
    console.log("校验结果:", valid)
    if (valid) {
      // 校验通过后再调用API
      recallTest(props.name, formData.value)
        .then((res: any) => {
          // console.log(res, 777)
          resultContent.value = ""
          res.data.forEach((item: any) => {
            item.metadata.source
            && (resultContent.value += `${item.metadata.source}<br /><br />`)
            item.score
            && (resultContent.value += `score：${item.score}<br /><br />`)
            item.content && (resultContent.value += `${item.content}<br /><br />`)
          })
          // 这里可以处理API返回的结果
        })
        .catch((error) => {
          console.error("API调用失败:", error)
        })
    } else {
      console.log("表单校验失败")
    }
  })
}

function show(initOptions: InitOptions = {}) {
  visible.value = true
  formData.value = {
    question: "",
    recall_top_k: null,
    recall_retrievers: [],
    recall_score_threshold: null,
    ...initOptions
  }
  resultContent.value = ""
  handleGetRecallFn()
}

defineExpose({
  show
})
</script>

<template>
  <el-drawer
    v-model="visible"
    :show-close="false"
    size="100%"
    class="recall-test-drawer"
  >
    <!-- 头部 -->
    <template #header="{ close }">
      <div class="recall-test-drawer-header">
        <div class="back-icon-wrapper" @click="close">
          <i class="ChatData-fanhui iconfont"></i>
        </div>
        <span class="title">召回测试</span>
        <el-button @click="close">
          取消
        </el-button>
      </div>
    </template>
    <!-- 测试配置 -->
    <el-form
      ref="formRef"
      class="recall-test-drawer-body"
      label-position="left"
      :model="formData"
      :rules="rules"
      label-width="95"
    >
      <div class="split-item">
        <span class="label">召回配置</span>
        <el-form-item
          label-position="top"
          class="quest-form-item"
          prop="question"
        >
          <template #label>
            <div class="flex-row" style="flex: 1">
              <span style="flex: 1">测试问题</span>
              <el-space class="config-btn">
                <i class="iconfont ChatData-shezhi" />
                <el-popover title="向量检索设置" width="360" trigger="click">
                  <el-form-item label="Topk" prop="recall_top_k">
                    <template #label>
                      <el-space>
                        <span>Topk</span>
                        <el-tooltip
                          content="用于筛选与用户问题相似度最高的文本片段"
                        >
                          <i
                            class="iconfont ChatData-zhushi"
                            style="color: #4c6f88; cursor: pointer"
                          />
                        </el-tooltip>
                      </el-space>
                    </template>
                    <el-input
                      v-model="formData.recall_top_k"
                      placeholder="请输入Topk"
                    />
                  </el-form-item>
                  <el-form-item label="召回方法：" prop="recall_retrievers">
                    <el-select
                      v-model="formData.recall_retrievers"
                      multiple
                      clearable
                      collapse-tags
                      disabled
                    >
                      <el-option
                        v-for="item in fnList"
                        :key="item"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="Score阈值：">
                    <el-input
                      v-model="formData.recall_score_threshold"
                      placeholder="请输入Score阈值"
                    />
                  </el-form-item>
                  <template #reference>
                    <span>向量检索设置</span>
                  </template>
                </el-popover>
              </el-space>
            </div>
          </template>
          <div class="flex-row" style="gap: 20px; width: 100%">
            <el-input
              style="flex: 1"
              placeholder="请输入测试问题"
              clearable
              v-model="formData.question"
            />
            <el-button type="primary" @click="handleTest">
              开始测试
            </el-button>
          </div>
        </el-form-item>
      </div>
      <div class="split-item">
        <span class="label">召回结果</span>
        <div class="result-content" v-html="resultContent" />
      </div>
    </el-form>
  </el-drawer>
</template>

<style lang="scss" scoped>
.back-icon-wrapper {
  padding: 5px;
  border-radius: 8px;
  width: max-content;
  /* font-weight: bold; */
  background: #ebf3fd;
  display: flex;
  align-items: center;

  cursor: pointer;
  .iconfont {
    color:#005ee0
  }
}
.recall-test-drawer-header {
  display: flex;
  align-items: center;
  gap: 15px;
  .title {
    flex: 1;
    font-weight: bold;
    font-size: 16px;
    color: #262626;
    line-height: 22px;
  }
}
.recall-test-drawer-body {
  width: 70%;
  margin-left: 15%;
  height: 100%;
  display: flex;
  flex-direction: column;
  // 第二个split-item
  > .split-item:nth-child(2) {
    flex: 1;
    overflow: hidden;
    display: flex
;
    flex-direction: column;
  }
  //   border: 1px solid #ebebeb;
  .result-content {
    max-height: 100%;
    min-height: 340px;
    height: max-content;
    background: #fcfcfc;
    border-radius: 4px;
    border: 1px solid #f2f2f2;
    padding: 15px;
    line-height: 20px;
    color: #595959;
    font-size: 13px;
    overflow-y: auto;
  }
  .quest-form-item {
    :deep(.el-form-item__label) {
      width: 100%;
      padding-right: 0px;
      display: flex;
    }
  }

  .config-btn {
    font-weight: 600;
    font-size: 12px;
    color: #005ee0;
    line-height: 14px;
    cursor: pointer;
  }
}
.split-item {
  display: flex;
  flex-direction: column;
  gap: 10px;
  .label {
    display: flex;
    align-items: center;
    font-weight: bold;
    font-size: 14px;
    color: #262626;
    line-height: 24px;
    &::before {
      content: "";
      height: 12px;
      width: 3px;
      display: inline-block;
      background: #005ee0;
      margin-right: 8px;
    }
  }
}
</style>
