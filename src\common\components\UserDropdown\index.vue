<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { UserFilled } from '@element-plus/icons-vue'
import { useUserStore } from '@/pinia/stores/user'
import { useRouter, useRoute } from 'vue-router'
import { useWorkspaceStore } from '@/pinia/stores/workspace'

const userStore = useUserStore()
const router = useRouter()
const route = useRoute()
const workspaceStore = useWorkspaceStore()
const spaces = ref<any[]>([])
const currentSpace = ref<any>({})
const search = ref('')
const spacePopoverVisible = ref(false)
watch(() => workspaceStore.spaces, (v:any,oval:any) => {
  if(v.length && oval.length && v !== oval) {
    ensureSpaces()
  }
});
watch(() => route.query.spaceCode , (v:any,o:any) => {
  if(v && o && v !== o) {
    currentSpace.value = spaces.value.find(s => s.id == route.query.spaceCode) || {}
  }
});
function logout() {
  userStore.logout()
  router.push('/login')
}

function goProfile() {
  router.push({
    path: '/llm/systemConfig/workspaceManage',
    query: {
      spaceCode: route.query.spaceCode || ''
    }
  })
}
function goSettings() {
  router.push({
    path: '/llm/systemConfig/workspaceManage',
    query: {
      spaceCode: route.query.spaceCode || ''
    }
  })
}

async function ensureSpaces() {
  if (workspaceStore.spaces.length === 0) {
    spaces.value = await workspaceStore.fetchSpaces()
  } else {
    spaces.value = workspaceStore.spaces
  }
  currentSpace.value = spaces.value.find(s => s.id == route.query.spaceCode) || {}
  if (!route.query.spaceCode || !currentSpace.value.id) {
    currentSpace.value = spaces.value.find(s => s.is_default) || spaces.value[0]
    router.push({
      name: route.name as string,
      query: {
        ...route.query,
        spaceCode: currentSpace.value.id
      }
    })
    setTimeout(() => window.location.reload(), 100)
  } else {
    currentSpace.value = spaces.value.find(s => s.id == route.query.spaceCode) || {}
  }
}

function changeSpace(space: any) {
  if (space.id !== currentSpace.value?.id) {
    router.push({
      name: route.name as string,
      query: {
        ...route.query,
        spaceCode: space.id
      }
    })
    setTimeout(() => window.location.reload(), 100)
  }
  spacePopoverVisible.value = false
}
function gotoSpaceConfig() {
  router.push({
    path: '/llm/systemConfig/workspaceManage',
    query: {
      spaceCode: route.query.spaceCode || ''
    }
  })
  spacePopoverVisible.value = false
}

const filteredSpaces = computed(() => {
  if (!search.value) return spaces.value
  return spaces.value.filter(s => s.workspace_name.toLowerCase().includes(search.value.toLowerCase()))
})

onMounted(ensureSpaces)
</script>

<template>
  <el-dropdown trigger="click" class="user-dropdown">
    <div class="user-info">
      <el-avatar :icon="UserFilled" :size="28" />
      <div class="meta">
        <div class="username">{{ userStore.username }}</div>
      </div>
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <div class="dropdown-baseInfor">
          <el-avatar :icon="UserFilled" :size="36" />
          <div class="txt">
            <b>{{ userStore.username }}</b>
            <span>{{ userStore.username }}</span>
          </div>
        </div>
        <div class="dropdown-space">
          <div class="title">工作空间</div>
          <el-popover v-model:visible="spacePopoverVisible" placement="left" width="220" popper-class="space-popover" trigger="click">
            <template #reference>
              <div class="curSpace">
                <span>
                  <!-- <el-icon><CreditCard /></el-icon> -->
                  {{ currentSpace && currentSpace.workspace_name }}
                </span>
                <el-icon><ArrowRightBold /></el-icon>
              </div>
            </template>
            <div class="drop-space-list">
              <el-input v-model="search" class="task-search-inp" placeholder="搜索" clearable size="small">
                <template #prefix>
                  <i class="el-input__icon el-icon-search"></i>
                </template>
              </el-input>
              <div class="list-wrap mone-scrollbar">
                <div
                  class="space-item"
                  :class="{ curClass: item.id === currentSpace.id }"
                  v-for="item in filteredSpaces"
                  :key="item.id"
                  @click="changeSpace(item)"
                >
                  <!-- <el-icon><CreditCard /></el-icon> -->
                  <span class="name">{{ item.workspace_name }}</span>
                  <el-icon v-if="item.id === currentSpace.id"><Check /></el-icon>
                </div>
              </div>
              <div class="all-space" @click.stop="gotoSpaceConfig">
                <span style="display: flex; align-items: center">
                  <span style="margin-left: 5px">全部空间</span>
                </span>
              </div>
            </div>
          </el-popover>
        </div>
        <el-dropdown-item @click="goProfile">个人中心</el-dropdown-item>
        <el-dropdown-item @click="goSettings">设置</el-dropdown-item>
        <el-dropdown-item divided @click="logout">退出登录</el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<style scoped lang="scss">
.user-dropdown {
  cursor: pointer;
}
.user-info {
  display: flex;
  align-items: center;
}
.meta {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}
.username {
  font-size: 16px;
  color: #fff;
  font-weight: bold;
}
.space-row {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #409eff;
  cursor: pointer;
  margin-top: 2px;
}
.space-name {
  font-weight: 500;
}
.space-search {
  margin-bottom: 10px;
}
.space-list {
  max-height: 300px;
  overflow-y: auto;
}
.space-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s;
}
.space-item.active, .space-item:hover, .space-item.curClass {
  background: #f0f7ff;
}
.empty {
  text-align: center;
  color: #aaa;
  padding: 20px 0;
}
.dropdown-baseInfor {
  display: flex;
  align-items: center;
  padding: 10px 16px 6px 16px;
  border-bottom: 1px solid #f0f0f0;
}
.dropdown-baseInfor .txt {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
}
.dropdown-baseInfor b {
  font-size: 16px;
  color: #222;
  font-weight: bold;
}
.dropdown-baseInfor span {
  font-size: 13px;
  color: #888;
}
.dropdown-space {
  padding: 10px 16px 8px 16px;
}
.dropdown-space .title {
  font-size: 13px;
  color: #888;
  margin-bottom: 6px;
}
.curSpace {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-size: 14px;
  color: #000;
  font-weight: bold;
  white-space: nowrap;
  background: #f0f7ff;
  padding: 4px 10px;
  .el-icon {
    margin-left: 15px;
  }
}
.list-wrap {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 8px;
}
.all-space {
  margin-top: 8px;
  text-align: center;
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
}
</style>
