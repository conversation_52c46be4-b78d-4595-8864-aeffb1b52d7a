<script lang="ts" setup>
import { Delete, Edit, Plus, Search } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { computed, ref } from "vue";
import { getTagList, addTag, editTag, delTag } from "@/common/apis/knowledge";
interface Tag {
  id: string;
  name: string;
  [key: string]: any;
}

const emits = defineEmits(["update"]);

// 使用 defineModel 简化双向绑定
const visible = defineModel<boolean>("visible", { default: false });
const tags = ref<Tag[]>([]);

// 搜索关键词
const searchKeyword = ref("");

// 过滤后的标签列表
const filteredTags = computed(() => {
  if (!searchKeyword.value) return tags.value;
  return tags.value.filter((tag) =>
    tag.label_name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 编辑相关
const editingTagId = ref<string>("");
const editingTagName = ref("");

// 新增相关
const showAddForm = ref(false);
const newTagName = ref("");
const continueAdd = ref(false);

const loading = ref<boolean>(false);
// 开始编辑
function startEdit(tag: Tag) {
  editingTagId.value = tag.id;
  editingTagName.value = tag.label_name;
}

// 确认编辑
function confirmEdit() {
  if (!editingTagName.value.trim()) {
    ElMessage.warning("标签名称不能为空");
    return;
  }
  editTag({
    id: editingTagId.value,
    label_name: editingTagName.value,
  }).then(res => {
    cancelEdit();
    getAllTags();
  });
  // const index = tags.value.findIndex((t) => t.id === editingTagId.value);
  // if (index !== -1) {
  //   tags.value[index].name = editingTagName.value.trim();
  //   ElMessage.success("编辑成功");
  // }

  // cancelEdit();
}

// 取消编辑
function cancelEdit() {
  editingTagId.value = "";
  editingTagName.value = "";
}

// 删除标签
async function handleDelete(tag: Tag) {
  try {
    await ElMessageBox.confirm(
      `确定要删除标签"${tag.label_name}"吗？`,
      "确认删除",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );
    delTag(tag.id).then((res) => getAllTags());
  } catch {
    // 用户取消删除
  }
}

// 新增标签
function handleAddTag() {
  if (!newTagName.value.trim()) {
    ElMessage.warning("请输入标签名称");
    return;
  }

  // const newTag: Tag = {
  //   id: Date.now().toString(),
  //   name: newTagName.value.trim(),
  // };

  // tags.value.unshift(newTag);
  // ElMessage.success("添加成功");
  loading.value = true;
  addTag({
    label_name: newTagName.value.trim(),
  })
    .then((res) => {
      loading.value = false;
      if (continueAdd.value) {
        newTagName.value = "";
      } else {
        cancelAdd();
      }
      getAllTags();
    })
    .catch(() => (loading.value = false));
}

// 取消新增
function cancelAdd() {
  showAddForm.value = false;
  newTagName.value = "";
  continueAdd.value = false;
}

// 确认
function handleConfirm() {
  visible.value = false;
}

// 关闭抽屉
function handleClose() {
  let clonseConfirm = editingTagId.value
    ? ElMessageBox.confirm("有未保存的编辑，确定要关闭吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
    : Promise.resolve();
  clonseConfirm.then(() => {
    cancelEdit();
    showAddForm.value = false;
    searchKeyword.value = "";
    visible.value = false;
    emits('update')
  });
  // if (editingTagId.value) {
  //   ElMessageBox.confirm("有未保存的编辑，确定要关闭吗？", "提示", {
  //     confirmButtonText: "确定",
  //     cancelButtonText: "取消",
  //     type: "warning",
  //   })
  //     .then(() => {
  //       cancelEdit();
  //       showAddForm.value = false;
  //       searchKeyword.value = "";
  //       visible.value = false;
  //     })
  //     .catch(() => {
  //       // 用户取消关闭
  //     });
  // } else {
  //   showAddForm.value = false;
  //   searchKeyword.value = "";
  //   visible.value = false;
  // }
}

// 获取所有标签
function getAllTags() {
  tags.value = [];
  loading.value = true;
  // 获取所有标签的逻辑
  getTagList({
    page: 1,
    page_size: 99999,
  })
    .then((res: any) => {
      loading.value = false;
      tags.value = res?.data?.data || [];
    })
    .catch(() => (loading.value = false));
}

watch(
  () => visible.value,
  (newVal) => {
    if (newVal) {
      getAllTags();
    }
  }
);
</script>

<template>
  <el-drawer
    v-model="visible"
    title="标签管理"
    direction="rtl"
    destroy-on-close
    size="280px"
    class="tag-manage-drawer"
    :before-close="handleClose"
  >
    <div class="tag-management-container" v-loading="loading">
      <!-- 搜索栏 -->
      <div class="search-section">
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="关键字"
            :prefix-icon="Search"
            clearable
          />
          <el-popconfirm
            placement="left-start"
            :width="250"
            popper-class="add-tag-popconfirm"
            title="新增标签"
            :teleported="false"
            hide-icon
            :visible="showAddForm"
          >
            <template #reference>
              <el-button
                type="primary"
                circle
                @click="showAddForm = true"
                size="small"
                style="width: 20px; height: 20px"
              >
                <template #icon>
                  <el-icon size="10" color="#fff">
                    <Plus />
                  </el-icon>
                </template>
              </el-button>
            </template>
            <template #actions>
              <div class="add-tag-form">
                <el-input
                  v-model="newTagName"
                  placeholder="输入标签名称"
                  @keyup.enter="handleAddTag"
                />

                <div class="form-actions">
                  <el-button type="primary" size="small" @click="handleAddTag">
                    确认
                  </el-button>
                  <el-button size="small" @click="cancelAdd"> 取消 </el-button>
                  <div class="continue-add">
                    <el-switch v-model="continueAdd" />
                    <span>继续新增</span>
                  </div>
                </div>
              </div>
            </template>
          </el-popconfirm>
        </div>
      </div>

      <!-- 标签列表 -->
      <div class="tag-list-section">
        <div class="tag-list">
          <div v-for="tag in filteredTags" :key="tag.id" class="tag-item">
            <!-- 正常显示模式 -->
            <div v-if="editingTagId !== tag.id" class="tag-content">
              <span class="tag-name">{{ tag.label_name }}</span>
              <div class="tag-actions">
                <i class="iconfont ChatData-bianji" @click="startEdit(tag)"></i>
                <i
                  class="iconfont ChatData-shanchu"
                  @click="handleDelete(tag)"
                ></i>
                <!-- <el-button type="text" :icon="Edit" @click="startEdit(tag)" />
                <el-button
                  type="text"
                  :icon="Delete"
                  @click="handleDelete(tag)"
                /> -->
              </div>
            </div>

            <!-- 编辑模式 -->
            <div v-else class="tag-edit">
              <el-input
                v-model="editingTagName"
                size="small"
                @keyup.enter="confirmEdit"
                @keyup.esc="cancelEdit"
              />
              <div class="edit-actions">
                <el-button type="primary" size="small" @click="confirmEdit">
                  确定
                </el-button>
                <el-button size="small" @click="cancelEdit"> 取消 </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑模式提示 -->
        <!-- <div v-if="editingTagId" class="edit-mode-label">编辑模式</div> -->
      </div>
    </div>

    <!-- 抽屉底部按钮 -->
    <!-- <template #footer>
      <div class="drawer-footer">
        <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
        <el-button @click="visible = false"> 取消 </el-button>
      </div>
    </template> -->
  </el-drawer>
</template>

<style lang="scss" scoped>
.tag-management-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 13px;

  .search-bar {
    display: flex;
    align-items: center;
    gap: 10px;

    .el-input {
      flex: 1;
      :deep(.el-input__wrapper) {
        background: #f2f2f2;
      }
    }
  }
}

.tag-list-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .tag-list {
    flex: 1;
    overflow-y: auto;

    .tag-item {
      padding: 5px 0;
      // border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .tag-content {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tag-name {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .tag-actions {
          display: flex;
          align-items: center;
          gap: 10px;
          i {
            color: #4c6f88;
            font-size: 11px;
            cursor: pointer;
          }
        }
      }

      .tag-edit {
        display: flex;
        align-items: center;
        gap: 5px;
        border: 1px solid #dbdbdb;
        border-radius: 4px;
        padding: 5px 2px;
        .el-input {
          flex: 1;
          :deep(.el-input__wrapper) {
            box-shadow: none;
          }
        }

        .edit-actions {
          display: flex;
          align-items: center;
          gap: 5px;
          .el-button {
            margin-left: 0px;
          }
        }
      }
    }
  }

  .edit-mode-label {
    margin-top: 10px;
    padding: 8px 12px;
    background: #f5f7fa;
    border-radius: 4px;
    font-size: 12px;
    color: #666;
    text-align: center;
  }
}

.add-tag-form {
  .form-actions {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 0px;

    .continue-add {
      margin-left: auto;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #666;
    }
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-start;
  gap: 0px;
  // padding: 20px;
  // border-top: 1px solid #f0f0f0;
}

// :deep(.el-drawer__header) {
//   margin-bottom: 0;
//   padding: 20px 20px 0 20px;
// }

// :deep(.el-drawer__body) {
//   padding: 0;
//   height: calc(100% - 120px);
// }
:deep(.add-tag-popconfirm) {
  .el-popconfirm__main {
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    padding: 10px 0px;
    line-height: 20px;
  }
}
</style>
