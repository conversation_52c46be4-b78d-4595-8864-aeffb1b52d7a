<script setup lang="ts">
import { batchAddChunk, downDocumentTemp } from "@/common/apis/knowledge";
const props = defineProps({
  name: {
    type: String,
    default: "",
  },
  doc: {
    type: Object,
    default: () => {},
  },
});
const emit = defineEmits(["addChunk", "update"]);
// 批量添加分片popover vis
const batchAddChunkVisible = ref<boolean>(false);
// 上传ref
const uploadChunksRef = ref();
// 是否批量添加中
const isBatchAddChunk = ref<boolean>(false);
// 文件列表
const fileList = ref([]);
// 新增单个
function singleAddChunk() {
  emit("addChunk");
}

// 开始上传
function handleUpload() {
  if (!uploadChunksRef.value) {
    ElMessage.warning("上传组件未初始化");
    return;
  }
  // 获取上传的文件列表
  const uploadFiles = fileList.value;

  // 检查是否有文件需要上传
  if (!uploadFiles || uploadFiles.length === 0) {
    ElMessage.warning("请先选择要上传的文件");
    return;
  }

  // 验证文件格式（可选）
  const validFiles = uploadFiles.every((file) => {
    const fileName = file.name.toLowerCase();
    return fileName.endsWith(".csv");
  });

  if (!validFiles) {
    ElMessage.error("请上传CSV格式文件");
    return;
  }

  // 设置上传状态
  isBatchAddChunk.value = true;

  try {
    batchAddChunkVisible.value = false;
    let file = uploadFiles[0];
    // console.log(file, props.name, props.doc)
    const formDataObj = new FormData();
    formDataObj.append("csv_file", file.raw); // file.raw 兼容 el-upload 组件
    formDataObj.append("document_id", props.doc.id);
    // formDataObj.append("doc_type", formData.doc_type);
    // if (!basicData.id && !isFileAdd.value) {
    //   // 使用临时空间创建
    //   formDataObj.append("is_temp_space", "true");
    // }
    batchAddChunk(props.name, formDataObj).then((res) => {
      ElMessage.success("文件上传成功");
      batchAddChunkVisible.value = false;
      // 清空已上传文件列表
      uploadChunksRef.value.clearFiles();
      isBatchAddChunk.value = false;
      emit("update");
    });
  } catch (error) {
    ElMessage.error("文件上传失败");
    isBatchAddChunk.value = false;
  }
}

// 下载模板
function downTemp() {
  downDocumentTemp().then((res: any) => {
    const { download_url, filename } = res.data || {};
    if (download_url) {
      downloadFile(download_url, filename);
    }
  });
}

// 可选的：使用 fetch 下载文件的方法
function downloadFile(url: string, filename: string) {
  const link = document.createElement("a");
  link.href = url;
  link.download = filename || "document";
  link.target = "_blank";
  link.style.display = "none"; // 隐藏链接

  // 添加到 DOM 并触发点击
  document.body.appendChild(link);
  link.click();

  // 清理 DOM
  document.body.removeChild(link);
}
</script>

<template>
  <!-- 添加/批量添加 分片 -->
  <el-popover :visible="batchAddChunkVisible" placement="bottom" width="350">
    <!-- 批量添加分片上传内容 -->
    <div class="batch-add-chunk-pop">
      <div class="flex-row">
        <span style="flex: 1" class="pop-title">批量添加分片</span>
        <el-icon @click="batchAddChunkVisible = false" style="cursor: pointer">
          <Close />
        </el-icon>
      </div>
      <el-upload
        class="upload-chunks"
        drag
        v-model:file-list="fileList"
        :auto-upload="false"
        :limit="1"
        ref="uploadChunksRef"
      >
        <!-- <el-icon class="el-icon--upload"><upload-filled /></el-icon> -->
        <div class="el-upload__text">
          <el-space>
            <i
              class="iconfont ChatData-csv"
              style="font-size: 24px; color: #2bbf79"
            />
            csv格式文件拖放到此处或 <em>点击上传</em>
          </el-space>
        </div>
      </el-upload>
      <span class="upload-tip"
        >csv格式必须符合以下结构
        <span class="down-tmp" style="cursor: pointer;" @click="downTemp">点击下载模板</span></span
      >
      <table class="csv-template-table">
        <thead>
          <tr>
            <th style="text-align: left">分片内容</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>内容1</td>
          </tr>
          <tr>
            <td>内容2</td>
          </tr>
        </tbody>
      </table>
      <div class="imp-btn">
        <el-button type="primary" size="small" @click="handleUpload">
          导入
        </el-button>
        <el-button size="small" @click="batchAddChunkVisible = false">
          取消
        </el-button>
      </div>
    </div>
    <template #reference>
      <el-button
        type="primary"
        plain
        size="small"
        style="height: 28px"
        :loading="isBatchAddChunk"
      >
        <template v-if="isBatchAddChunk">
          <span>批量加载中</span>
        </template>
        <el-space @click.prevent="singleAddChunk" v-else>
          <el-icon>
            <Plus size="14" color="#005EE0" />
          </el-icon>
          <span style="font-weight: bold">添加分片</span>
          <el-dropdown
            @command="(cmd) => (batchAddChunkVisible = true)"
            @click.stop
          >
            <el-icon>
              <ArrowDown size="14" color="#005EE0" />
            </el-icon>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item class="batch">
                  批量添加分片
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-space>
      </el-button>
    </template>
  </el-popover>
</template>

<style lang="scss" scoped>
.pop-title {
  font-weight: 500;
  font-size: 16px;
  color: #262626;
  line-height: 20px;
}
.batch-add-chunk-pop {
  display: flex;
  flex-direction: column;
  gap: 15px;
  font-size: 13px;
  em {
    color: #005ee0 !important;
  }
  .upload-tip {
    font-size: 12px;
    color: #595959;
  }
  .down-tmp {
    color: #005ee0;
  }
  .csv-template-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid #dcdfe6;

    th,
    td {
      border: 1px solid #dcdfe6;
      padding: 8px 12px;
      font-size: 12px;
    }

    th {
      background-color: #f5f7fa;
      font-weight: 500;
      color: #606266;
    }

    td {
      color: #909399;
    }
  }
}
</style>
