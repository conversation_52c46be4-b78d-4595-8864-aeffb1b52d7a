<script lang="ts" setup>
import {
  ArrowDown,
  ArrowRightBold,
  More,
  Plus,
  Search,
  Select,
} from "@element-plus/icons-vue";
import { cloneDeep } from "lodash-es";
import { useRoute, useRouter } from "vue-router";
import {
  getArgumentsList<PERSON>pi,
  getChunkList<PERSON>pi,
  getDocumentList<PERSON>pi,
  postArgumentApi,
  addChunk,
  editChunk,
  editDocumentApi,
  delDocumentApi,
  delChunk,
  downDocument,
  downDocumentTemp,
} from "@/common/apis/knowledge";
import customPage from "@/pages/agent/components/customPage.vue";
import addChunksBtn from "./components/addChunksBtn.vue";
import chunksManage from "./components/chunksManageDrawer.vue";
import recallTest from "./recallTest.vue";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import { number } from "echarts";
// 扩展 dayjs 以支持 utc 方法
dayjs.extend(utc);

const props = defineProps({
  canEdit: {
    type: Boolean,
    default: true,
  },
  id: {
    type: [String, Number],
    default: "",
  },
});
const router = useRouter();
const route = useRoute();
const knowledgeDetail = inject<any>("knowledgeDetail");

// 获取实例
const chunksManageRef = ref();
const recallTestRef = ref();

// 文件列表搜索
const filename = ref("");
// 文件列表
const fileList = ref<any[]>([]);
// 当前选中的文件
const curSelectFile = ref<any>({});
// arguments映射
const argumentsMap = ref<Record<string, any>>({
  topk: {
    tip: "用于筛选与用户问题相似度最高的文本片段。",
    label: "TopK",
    required: true,
  },
  recall_score: {
    tip: "用于设置文本片段筛选的相似度阈值。",
    label: "召回分数",
    required: true,
  },
  recall_type: {
    tip: "召回类型",
    label: "召回类型",
    required: true,
  },
  model: {
    tip: "用于创建文本或其他数据的矢量表示的模型",
    label: "模型",
    required: true,
  },
  chunk_size: {
    tip: "处理中使用的数据块的大小",
    label: "块大小",
    required: true,
  },
  chunk_overlap: {
    tip: "相邻数据块之间的重叠量",
    label: "块重叠",
    required: true,
  },
});

// arguments表单
const argumentsData = ref<any>({});
// arguments 校验规则
const getArgumentsRules = computed(() => {
  const rules: any = {};
  for (const key in argumentsMap.value) {
    const item = argumentsMap.value[key];
    if (item.required) {
      rules[key] = [
        {
          required: true,
          message: `${item.label}为必填项`,
        },
      ];
    }
  }
  return rules;
});

// 重命名pop ref
const renamePopVisible = ref<boolean>(false);
// 重命名text
const renameText = ref<string>("");
// 问题搜索pop 显示
const filterPopVisible = ref<boolean>(false);
// 分片 page params
const chunkPageParams = ref({
  page: 1,
  pageSize: 10,
  total: 50,
});
// 分片列表
const chunksList = ref<any[]>([]);
// 是否展开全部分片
const isExpandAllChunkItem = ref<boolean>(false);
// 分片checked 记录
const chunkCheckLogs = ref<(string | number)[]>([]);
// 分片搜索
const chunkSearch = ref<string>("");
// 选择所有分片
const isCheckAllChunk = ref<boolean>(false);
// 知识库名称
const spaceName = computed(() => {
  return knowledgeDetail?.value?.name;
});

// 分片列表loading
const chunkListLoading = ref<boolean>(false);

// ---------------------------------------

// 保存arguments
function handleSaveArguments() {
  filterPopVisible.value = false;
  const {
    topk,
    recall_score,
    recall_type,
    model,
    chunk_size,
    chunk_overlap,
    max_token,
    scene,
    template,
    max_iteration,
    concurrency_limit,
  } = argumentsData.value;
  const reqData: any = {
    embedding: {
      topk,
      recall_score,
      recall_type,
      model,
      chunk_size,
      chunk_overlap,
    },
    prompt: {
      max_token,
      scene,
      template,
    },
    summary: {
      max_iteration,
      concurrency_limit,
    },
  };
  postArgumentApi(spaceName.value, {
    argument: JSON.stringify(reqData),
  }).then((res: any) => {
    ElMessage.success("保存成功");
    initArgumentsData();
  });
}

// 初始化文件列表
function initFileList(isInit = true) {
  nextTick(() => {
    getDocumentListApi(spaceName.value, {
      page: 1,
      page_size: 999999,
      filename: filename.value,
    }).then((res: any) => {
      fileList.value = res?.data?.data || [];
      // 默认选中第一条
      if (isInit) {
        handleSelectFile(fileList.value[0]);
        // curSelectFile.value = fileList.value[0];
      }
    });
  });
}

// 开始召回测试
function handleRecallTest() {
  nextTick(() => {
    recallTestRef.value.show({
      recall_top_k: argumentsData.value.topk || 1,
    });
  });
}

// 新增 / 编辑分片
function handleChunksManage(type: string, data?: any, origin?: any) {
  nextTick(() => {
    chunksManageRef.value.show(
      type,
      data,
      type === "add" ? { index: chunkPageParams.value.total + 1 } : origin
    );
  });
}

// 详情头部右侧 更多操作 cmd
function detailHeaderCmd(cmd) {
  if (cmd === "rename") {
    renamePopVisible.value = true;
    renameText.value = curSelectFile?.value?.doc_name ?? "";
  } else if (cmd === "download") {
    downDocument(spaceName.value, curSelectFile.value.id).then((res: any) => {
      const { download_url, filename } = res.data || {};
      if (download_url) {
        downloadFile(download_url, filename);
      }
    });
    // window.open(curSelectFile?.value?.url, "_blank");
  } else if (cmd === "delete") {
    ElMessageBox.confirm("确定删除该文件吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(() => {
      delDocumentApi(spaceName.value, {
        doc_name: curSelectFile?.value?.doc_name,
      }).then((res: any) => {
        initFileList();
      });
    });
  }
}

// 可选的：使用 fetch 下载文件的方法
function downloadFile(url: string, filename: string) {
  const link = document.createElement("a");
  link.href = url;
  link.download = filename || curSelectFile.value.doc_name || "document";
  link.target = "_blank";
  link.style.display = "none"; // 隐藏链接

  // 添加到 DOM 并触发点击
  document.body.appendChild(link);
  link.click();

  // 清理 DOM
  document.body.removeChild(link);

  // fetch(url)
  //   .then((response) => {
  //     if (!response.ok) {
  //       throw new Error("网络响应错误");
  //     }
  //     return response.blob();
  //   })
  //   .then((blob) => {
  //     const link = document.createElement("a");
  //     link.href = URL.createObjectURL(blob);
  //     link.download = filename || "document";
  //     document.body.appendChild(link);
  //     link.click();
  //     document.body.removeChild(link);
  //     URL.revokeObjectURL(link.href);
  //   })
  //   .catch((error) => {
  //     console.error("下载文件失败:", error);
  //     ElMessage.error("下载文件失败");
  //   });
}

// 选择文件
function handleSelectFile(item: any) {
  chunkCheckLogs.value = [];
  // 查询充值
  chunkPageParams.value = {
    page: 1,
    pageSize: 10,
    total: 0,
  };
  chunkSearch.value = "";
  curSelectFile.value = {
    ...cloneDeep(item),
  };
  getChunkList();
  // console.log(curSelectFile.value, 8989);
}

function handleFilterChunk() {
  chunkPageParams.value.page = 1;
  getChunkList();
}

// 获取分片列表
function getChunkList() {
  chunkListLoading.value = true;
  getChunkListApi(spaceName.value, {
    document_id: curSelectFile.value.id,
    page: chunkPageParams.value.page,
    page_size: chunkPageParams.value.pageSize,
    content: chunkSearch.value,
  })
    .then((res: any) => {
      chunkListLoading.value = false;
      chunksList.value = (res?.data?.data || []).map((item, index) => {
        item.index =
          index +
          1 +
          (chunkPageParams.value.page - 1) * chunkPageParams.value.pageSize;
        // 本身勾选过取本身记录， 否则以全局 | false
        item.isChecked = chunkCheckLogs.value.includes(item.id)
          ? true
          : isCheckAllChunk.value || false;
        return item;
      });
      chunkPageParams.value.total = res?.data?.total || 0;
    })
    .catch(() => {
      chunkListLoading.value = false;
    });
}

// 初始化argumentsdata数据
function initArgumentsData() {
  for (let key in argumentsMap.value) {
    argumentsData.value[key] = "";
  }
  getArgumentsListApi(spaceName.value).then((res: any) => {
    let { embedding = {}, prompt = {}, summary = {} } = res.data;
    argumentsData.value = {
      ...embedding,
      ...prompt,
      ...summary,
    };
  });
}
// 获取chunk path字段
function getChunkPath(item: any) {
  try {
    let { meta_info } = item;
    if (!meta_info || typeof meta_info !== "string") {
      return "";
    }

    let meta;
    // 首先尝试标准 JSON 解析
    try {
      meta = JSON.parse(meta_info);
    } catch (jsonError) {
      // 如果失败，尝试修复格式后再解析
      try {
        // 替换单引号为双引号（简单处理）
        let fixedJson = meta_info.replace(/'/g, '"');
        meta = JSON.parse(fixedJson);
      } catch (fixError) {
        // 如果仍然失败，使用 Function 方式
        meta = new Function(`return ${meta_info}`)();
      }
    }

    return meta.source || "";
  } catch (error) {
    console.warn("解析 meta_info 失败:", error, item.meta_info);
    return "";
  }
}
// chunk 新增 编辑回调
function chunkMangeCallback(obj: any) {
  let { type, data, originData } = obj;
  let API = type === "add" ? addChunk : editChunk;
  // console.log(curSelectFile.value);
  let reqData =
    type === "add"
      ? {
          document_id: curSelectFile?.value?.id,
          content: data,
        }
      : {
          chunk_id: originData.id,
          content: data,
          questions: originData.questions,
        };
  API(spaceName.value, reqData).then(async (res) => {
    ElMessage.success("操作成功");
    await initFileList(false);
    await getChunkList();
  });
}
// 新增文件
function addFile() {
  router.push({
    path: "/knowledge/createKnowledge",
    query: {
      spaceCode: route.query.spaceCode || "",
      knowledge_id: knowledgeDetail.value.id,
      knowledge_name: knowledgeDetail.value.name,
    },
  });
}

// 文件重命名
function handleRename() {
  console.log(curSelectFile.value, renameText.value);
  let { id, questions } = curSelectFile.value;

  editDocumentApi(spaceName.value, {
    questions,
    doc_id: id,
    doc_name: renameText.value,
  }).then((res) => {
    // 关闭弹窗
    renamePopVisible.value = false;
    ElMessage.success("操作成功");
    // 重置列表
    initFileList(false);
    // 用新的详情去查询
    curSelectFile.value = {
      ...curSelectFile.value,
      doc_name: renameText.value,
    };
    renameText.value = "";
    handleSelectFile(curSelectFile.value);
  });
}
// 删除分片
function handleDelChunk(item: any) {
  ElMessageBox.confirm("确定删除该分片吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delChunk(spaceName.value, item.id).then(async (res) => {
      // 只剩一条删除后返回第一页
      if (chunksList?.value?.length === 1) {
        chunkPageParams.value.page = 1;
      }
      await initFileList(false);
      await getChunkList();
    });
  });
}

function formatTime(time: string | Date, format?: string) {
  return time
    ? dayjs
        .utc(time)
        .local()
        .format(format || "YYYY-MM-DD HH:mm:ss")
    : "-";
}

onUnmounted(() => {
  // 销毁时重置状态
  filterPopVisible.value = false;
});
watch(
  () => props.id,
  (newVal) => {
    if (newVal) {
      chunkPageParams.value = {
        page: 1,
        pageSize: 10,
        total: 50,
      };
      initFileList();
      initArgumentsData();
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>

<template>
  <div class="detail-manage">
    <!-- 左侧文件列表 -->
    <div class="file-content">
      <div class="title">
        <span class="text">文件列表</span>
        <div class="btns">
          <el-space :size="10">
            <el-tooltip content="召回测试">
              <i class="iconfont ChatData-hundun" @click="handleRecallTest" />
            </el-tooltip>
            <el-popover
              class="box-item"
              :visible="filterPopVisible"
              placement="right-end"
              @show="initArgumentsData"
              width="480"
            >
              <template #reference>
                <div>
                  <el-tooltip content="Arguments配置">
                    <i
                      class="iconfont ChatData-gongju filter-icon"
                      @click.stop="filterPopVisible = !filterPopVisible"
                    />
                  </el-tooltip>
                </div>
              </template>
              <div class="file-filter-header">
                <p class="title">Arguments</p>
                <el-icon
                  @click="filterPopVisible = false"
                  style="cursor: pointer"
                >
                  <Close />
                </el-icon>
              </div>
              <el-form
                :model="argumentsData"
                :rules="getArgumentsRules"
                class="file-filter-data"
                label-width="120"
                label-position="top"
              >
                <el-form-item
                  :key="key"
                  v-for="(item, key) in argumentsMap"
                  :prop="item.path || key"
                >
                  <template #label>
                    <el-space :size="16">
                      <span>{{ item.label }}</span>
                      <el-tooltip :content="item.tip">
                        <i
                          class="iconfont ChatData-zhushi"
                          style="color: #4c6f88; cursor: pointer"
                        />
                      </el-tooltip>
                    </el-space>
                  </template>
                  <el-input v-model="argumentsData[key]" />
                </el-form-item>
                <div class="arguments-btn">
                  <el-button
                    size="small"
                    type="primary"
                    @click="handleSaveArguments"
                  >
                    保存
                  </el-button>
                  <el-button
                    size="small"
                    type="info"
                    @click="filterPopVisible = false"
                  >
                    取消
                  </el-button>
                </div>
              </el-form>
            </el-popover>
            <el-tooltip content="新增文件">
              <el-button
                type="primary"
                circle
                size="small"
                @click="addFile"
                style="width: 18px; height: 18px"
              >
                <template #icon>
                  <el-icon size="10" color="#fff">
                    <Plus />
                  </el-icon>
                </template>
              </el-button>
            </el-tooltip>
          </el-space>
        </div>
      </div>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-input
          placeholder="关键字..."
          :prefix-icon="Search"
          v-model="filename"
          @keydown.enter="initFileList(false)"
        />
      </div>
      <!-- 问题列表区域 -->
      <div class="file-list">
        <div
          class="ques-item"
          :class="[curSelectFile?.id === item.id && 'active-ques-item']"
          v-for="item in fileList"
          :key="item.id"
          @click="handleSelectFile(item)"
        >
          <el-tooltip class="question" :content="item.doc_name">
            <div class="question">
              {{ item.doc_name }}
            </div>
          </el-tooltip>
          <div class="ques-item-bottom">
            <span class="chunks"> 分片: {{ item.chunk_size }} </span>
            <span class="create-time"
              >更新时间：{{ formatTime(item.gmt_modified) }}</span
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧详情区域 -->
    <div class="file-detail" v-if="!!curSelectFile.id">
      <!-- 头部 -->
      <div class="header">
        <p class="title">
          {{ curSelectFile?.doc_name }}
        </p>
        <div class="options">
          <!-- 添加/批量添加 分片 -->
          <addChunksBtn
            @add-chunk="handleChunksManage('add')"
            :name="spaceName"
            :doc="curSelectFile"
            @update="handleFilterChunk"
          />

          <!-- 操作 --><!-- 重命名 -->

          <el-popover ref="popoverRef" :visible="renamePopVisible" width="328">
            <!-- 重命名 -->
            <el-space>
              <el-input v-model="renameText" />
              <el-button type="primary" small @click="handleRename">
                保存
              </el-button>
              <el-button small @click="renamePopVisible = false">
                取消
              </el-button>
            </el-space>
            <template #reference>
              <!-- 操作菜单 -->
              <el-dropdown @command="detailHeaderCmd">
                <span class="el-dropdown-link">
                  <el-icon
                    class="el-icon--right"
                    style="vertical-align: middle; cursor: pointer"
                  >
                    <More />
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="rename">
                      重命名
                    </el-dropdown-item>
                    <el-dropdown-item command="download">
                      下载原文件
                    </el-dropdown-item>
                    <el-dropdown-item divided command="delete">
                      <span style="color: #fd033b">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-popover>
        </div>
        <div class="desc-info">
          <el-space :size="60">
            <span>更新时间：{{ formatTime(curSelectFile?.gmt_modified) }}</span>
            <span>新增时间：{{ formatTime(curSelectFile?.gmt_created) }}</span>
            <span>字符数：{{ curSelectFile?.chunk_size }}</span>
          </el-space>
        </div>
      </div>
      <!-- 详情内容 -->
      <div class="chunks-content flex-colum" v-loadng="chunkListLoading">
        <!-- 详情头部 -->
        <div class="chunk-content-header">
          <!-- <el-space class="select-all-chunk" :size="15">
            <el-checkbox v-model="isCheckAllChunk"></el-checkbox>
            <span>{{chunkPageParams.total}}分片</span>
          </el-space> -->
          <el-input
            v-model="chunkSearch"
            :prefix-icon="Search"
            style="width: 180px"
            @keydown.enter="handleFilterChunk"
          />
          <el-tooltip :content="!isExpandAllChunkItem ? '展开' : '收缩'">
            <i
              class="iconfont expand-icon"
              :class="[
                !isExpandAllChunkItem ? 'ChatData-zhankai' : 'ChatData-shousuo',
              ]"
              @click="isExpandAllChunkItem = !isExpandAllChunkItem"
            />
          </el-tooltip>
        </div>
        <!-- 分片列表 -->
        <div class="chunks-list">
          <div
            class="chunk-wrapper"
            v-for="chunk in chunksList"
            :key="chunk.id"
            @click="handleChunksManage('edit', chunk.content, chunk)"
          >
            <!-- <el-checkbox
              v-model="chunk.isChecked"
              class="chunk-check"
            ></el-checkbox> -->
            <div class="chunk-item">
              <div class="chunk-item-header">
                <el-space :size="48">
                  <span class="title">
                    <el-space :size="5">
                      <svg class="icon svg-icon">
                        <use xlink:href="#ChatData-fenpian" />
                      </svg>
                      分片-{{ chunk.index }}
                    </el-space>
                  </span>
                  <span class="count">{{ chunk?.char_count }}字符</span>
                  <i
                    class="iconfont ChatData-shanchu del-chunk"
                    @click.stop="handleDelChunk(chunk)"
                  />
                </el-space>
              </div>
              <div
                class="chunk-item-body"
                :class="isExpandAllChunkItem && 'expand-chunk-item-body'"
              >
                <p>path: {{ getChunkPath(chunk) }}</p>
                <div class="chunk-content">
                  {{ chunk.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 分页 -->
        <customPage
          v-model:current-page="chunkPageParams.page"
          v-model:page-size="chunkPageParams.pageSize"
          :total="chunkPageParams.total"
          @current-change="getChunkList"
          @size-change="getChunkList"
          show-page-size
        />
      </div>
    </div>
  </div>
  <!-- 分片操作 -->
  <chunksManage ref="chunksManageRef" @save="chunkMangeCallback" />
  <!-- 召回测试 -->
  <recallTest ref="recallTestRef" :name="spaceName" />
</template>

<style lang="scss" scoped>
@mixin ques-item-highlight {
  background: #ebf3fd;
  border-radius: 4px;

  .question {
    color: #005ee0 !important;
  }
}

.detail-manage {
  height: 100%;
  overflow: hidden;
  display: flex;

  .file-content {
    width: 380px;
    box-shadow: 1px 0px 0px 0px #ededed;
    padding: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    gap: 15px;

    .title {
      display: flex;
      align-items: center;

      .text {
        font-weight: 600;
        font-size: 14px;
        color: #262626;
        line-height: 18px;
        margin: 0px;
        display: flex;
        flex: 1;
      }

      .btns {
        .iconfont {
          font-size: 14px;
          color: #4c6f88;
          cursor: pointer;
        }
      }
    }

    .file-list {
      display: flex;
      flex-direction: column;
      gap: 10px;
      flex: 1;
      overflow: auto;

      .active-ques-item {
        @include ques-item-highlight;

        .question {
          font-weight: bold;
        }
      }

      .ques-item {
        height: 70px;
        box-sizing: border-box;
        padding: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;

        cursor: pointer;

        &:hover {
          @include ques-item-highlight;
        }

        .question {
          font-size: 13px;
          color: #262626;
          line-height: 18px;
          overflow: hidden;
          text-overflow: ellipsis;
          text-wrap: nowrap;
        }

        .ques-item-bottom {
          display: flex;
          align-items: center;
          gap: 25px;

          .create-time,
          .chunks {
            font-size: 12px;
            color: #8c8c8c;
            line-height: 14px;
          }
        }
      }
    }
  }

  .file-detail {
    flex: 1;
    overflow-y: auto;
    padding: 20px 20px 15px 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    gap: 15px;

    .header {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;
      border-bottom: 1px solid #ededed;

      .title {
        margin: 0px;
        font-weight: bold;
        font-size: 14px;
        color: #262626;
        line-height: 18px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        text-wrap: nowrap;
      }

      .options {
        display: flex;
        align-items: center;
        gap: 15px;

        .status {
          display: flex;
          align-items: center;
          gap: 5px;
        }

        .create-time {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 14px;
        }
      }

      .desc-info {
        width: 100%;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 14px;
        padding: 15px 0px;
      }
    }

    .chunks-content {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: 10px;

      .chunk-content-header {
        display: flex;
        align-items: center;
        gap: 10px;
        justify-content: flex-end;

        .expand-icon {
          cursor: pointer;
          color: #4c6f88;
        }

        .select-all-chunk {
          flex: 1;
          text-align: left;
        }
      }

      .chunks-list {
        display: flex;
        flex: 1;
        overflow: auto;
        flex-direction: column;
        gap: 10px;

        .chunk-wrapper {
          display: grid;
          grid-template-columns: auto;
          column-gap: 5px;
        }

        .chunk-item {
          display: flex;
          flex-direction: column;
          gap: 10px;
          cursor: pointer;
          padding: 10px;
          overflow: hidden;
          &:hover {
            background: #fcfcfc;

            .del-chunk {
              display: block !important;
            }
          }

          .chunk-item-header {
            font-size: 12px;
            color: #595959;
            line-height: 14px;
            position: relative;

            .del-chunk {
              cursor: pointer;
              font-size: 12px;
              color: #4c6f88;
              display: none;
              position: absolute;
              right: 0px;
            }
          }

          .chunk-item-body {
            font-size: 13px;
            color: #595959;
            line-height: 20px;
            height: 70px;
            &.expand-chunk-item-body {
              height: max-content;
              .chunk-content {
                text-wrap: wrap !important;
                text-overflow: ellipsis;
                height: max-content;
                overflow: auto;
                word-break: break-all;
              }
            }
            .chunk-content {
              overflow: hidden;
              text-overflow: ellipsis;
              text-wrap: nowrap;
            }
          }

          .svg-icon {
            width: 12px;
            height: 12px;
          }
        }
      }
    }
  }
}

.file-filter-data {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0px 15px;
}

.file-filter-header {
  display: flex;
  align-items: center;

  .title {
    font-weight: 500;
    font-size: 16px;
    color: #262626;
    line-height: 20px;
    flex: 1;
  }
}

.pop-title {
  font-weight: 500;
  font-size: 16px;
  color: #262626;
  line-height: 20px;
}
</style>
