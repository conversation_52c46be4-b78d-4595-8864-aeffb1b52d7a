<script lang="ts" setup>
import { useSettingsStore } from "@/pinia/stores/settings"
import { useTagsViewStore } from "@/pinia/stores/tags-view"
import { Footer,SideMenu } from "../index"
import { useRoute } from "vue-router"
import { computed } from "vue"
import { getUserPermissionApi } from "@/pages/login/apis/index"
import { useUserStoreOutside } from '@/pinia/stores/user'
import { useWorkspaceStore } from '@/pinia/stores/workspace'

const route = useRoute()
const tagsViewStore = useTagsViewStore()
const settingsStore = useSettingsStore()
const workspaceStore = useWorkspaceStore()
const showSildeMenu = computed(() => route.meta.showSildeMenu)
const spaceCode = computed(()=>  route.query.spaceCode)
watch(() => spaceCode.value, (val:string,oval:string) => {
  if(val && oval && val !== oval) {
    getUserPermission()
  }
});
onMounted(() => {
  spaceCode && getUserPermission()
})
// 获取空间用户权限
function getUserPermission(){
  let curSpace = workspaceStore.spaces.find(val=>val.id == spaceCode.value)
  curSpace && getUserPermissionApi({workspace_id:spaceCode.value}).then((res) => {
    const response = res as { data: any }
    // 将数据存储到 pinia 的 user store privileges 中
    console.log(response.data,'空间权限数据')
    const userStore = useUserStoreOutside()
    userStore.setPrivileges(response.data)
  })
}
</script>

<template>
  <section class="app-main">
    <!-- 侧边导航 -->
    <SideMenu v-if="showSildeMenu" />
    <div class="app-scrollbar">
      <!-- key 采用 route.path 和 route.fullPath 有着不同的效果，大多数时候 path 更通用 -->
      <router-view v-slot="{ Component, route }">
        <transition name="el-fade-in" mode="out-in">
          <keep-alive :include="tagsViewStore.cachedViews">
            <component :is="Component" :key="route.path" class="app-container-grow" />
          </keep-alive>
        </transition>
      </router-view>
      <!-- 页脚 -->
      <Footer v-if="settingsStore.showFooter" />
    </div>
    <!-- 返回顶部 -->
    <el-backtop />
    <!-- 返回顶部（固定 Header 情况下） -->
    <el-backtop target=".app-scrollbar" />
  </section>
</template>

<style lang="scss" scoped>
@import "@@/assets/styles/mixins.scss";

.app-main {
  width: 100%;
  display: flex;
}

.app-scrollbar {
  flex-grow: 1;
  overflow: auto;
  @extend %scrollbar;
  display: flex;
  flex-direction: column;
  .app-container-grow {
    flex-grow: 1;
  }
}
</style>
