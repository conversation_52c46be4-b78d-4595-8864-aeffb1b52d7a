<template>
  <div class="database-list-container">
    <div class="database-list-header">
      <h2>{{ $route.meta.title }}</h2>
      <div class="database-list-actions">
        <pre-label-select
          v-model="filterType"
          label="数据库类型："
          clearable
          @change="onSearch()"
        >
          <el-option
            v-for="item in databaseTypes"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </pre-label-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索数据库..."
          class="search-input"
          @input="onSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <div class="flex1"></div>
        <el-button type="primary" @click="addEvent">
          <el-icon><Plus /></el-icon>
          新增数据源
        </el-button>
      </div>
    </div>

    <el-row :gutter="20" class="database-cards">
      <el-col
        v-for="(database,idex) in filteredDatabases"
        :key="idex"
        :xs="12"
        :sm="6"
        :md="6"
        :lg="6"
        :xl="6"
      >
        <el-card class="database-card" shadow="hover" >
          <template #header>
            <div class="card-header">
              <div class="database-type-icon">
                <svg v-if="database.type === 'mysql'" class="mone-svg-icon" aria-hidden="true" >
                  <use xlink:href="#ChatData-ziyuan"></use>
                </svg>
                <svg v-else-if="database.type === 'postgresql'" class="mone-svg-icon"  style="width:30px;" aria-hidden="true">
                  <use xlink:href="#ChatData-postgresql"></use>
                </svg>
                <svg v-else class="mone-svg-icon" aria-hidden="true" style="width:30px;">
                  <use xlink:href="#ChatData-Hive"></use>
                </svg>
              </div>
              <span class="database-name" @click="TableListEvent(database)">{{ database.params?.database }}</span>
              <el-dropdown trigger="hover" @command="(command) => handleCommand(command, database)" :teleported="false" :tabindex="9999">
                <span class="el-dropdown-link">
                  <el-icon><MoreFilled /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="tableList">
                      表信息
                    </el-dropdown-item>
                    <el-dropdown-item command="edit">
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <span style="color: red">删除</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <div class="database-description">
            {{ database.description || '暂无描述' }}
          </div>

          <div class="database-details">
            <el-row>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="detail-label">host:</span>
                  <span class="detail-value">{{ database.params.host }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="detail-item">
                  <span class="detail-label">user:</span>
                  <span class="detail-value">{{ database.params.user }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="9">
                <div class="detail-item">
                  <span class="detail-label">port:</span>
                  <span class="detail-value">{{ database.params.port }}</span>
                </div>
              </el-col>
              <el-col :span="15">
                <div class="detail-item">
                  <span class="detail-label">database:</span>
                  <span class="detail-value">{{ getDatabaseTypeName(database.type) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
    </div>

    <el-empty v-if="!loading && filteredDatabases.length === 0" description="暂无数据" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import {
  Search,
  Plus,
  Edit,
  Delete,
  MoreFilled,
  Grid
} from '@element-plus/icons-vue'
import type { Database } from '@/pages/llm/dataSourceManage/typs'
import { getDatasourcesListApi, deleteDatasourceApi } from "@/common/apis/llm"
import mysql from "@@/assets/images/docs/mysql.png?url"
import preLabelSelect from "@/pages/agent/components/preLabelSelect.vue";

// 状态
const loading = ref(false)
// 模拟数据
const databases = ref<Database[]>([])
const route = useRoute()
const router = useRouter()
// 状态
const searchKeyword = ref('')
const filterType = ref('')

// 数据库类型选项
const databaseTypes = [
  { value: 'mysql', label: 'MySQL' },
  { value: 'postgresql', label: 'PostgreSQL' },
  { value: 'oracle', label: 'Oracle' },
  { value: 'sqlserver', label: 'SQL Server' },
  { value: 'mongodb', label: 'MongoDB' }
]

// 计算属性
const filteredDatabases = computed(() => {
  return databases.value.filter(db => {
    const matchKeyword = !searchKeyword.value ||
      db.params.database.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      db.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())

    const matchType = !filterType.value || db.type === filterType.value

    return matchKeyword && matchType
  })
})
onMounted(() => {
  getDatasourcesList()
})
function getDatasourcesList() {
  loading.value = true
  getDatasourcesListApi().then((res) => {
    const response = res as { data: Database[] }
    databases.value = response?.data || []
  }).finally(() => {
    loading.value = false
  })
}
const handleCommand = (command: string, database: Database) => {
  if (command === 'edit') {
    EditEvent(database)
  } else if (command === 'delete') {
    DeleteEvent(database)
  } else if (command === 'tableList') {
    TableListEvent(database)
  }
}

const onSearch = () => {
  // searchKeyword.value = filterType.value
  // 实际应用中可能需要调用API进行搜索
  // 这里简单实现前端过滤

}
// 新增
const addEvent = () => {
  router.push({
    path: 'dataSourceManageEdit',
    query: {
      spaceCode:route.query.spaceCode || '',
      mode: 'edit'
    }
  })
}
// 编辑
const EditEvent = (database: Database) => {
  router.push({
    path: 'dataSourceManageEdit',
    query: {
      spaceCode:route.query.spaceCode || '',
      mode: 'edit',
      id: database.id.toString()
    }
  })
}
// 表信息
const TableListEvent = (database: Database) => {
  router.push({
    path: 'dataSourceManageTableList',
    query: {
      spaceCode:route.query.spaceCode || '',
      id: database.id.toString()
    }
  })
}
const DeleteEvent=(database: Database) =>{
  ElMessageBox.confirm(
    `确定要删除数据库 "${database.params.database}" 吗？`,
    {
      title: '警告',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    deleteDatasourceApi(database.id)
    databases.value = databases.value.filter(item => item.id !== database.id)
    ElMessage.success('删除成功')
  }).catch(() => {
    // 用户取消删除
  })
}

const getDatabaseTypeName = (type: string): string => {
  const found = databaseTypes.find(item => item.value === type)
  return found ? found.label : type
}
</script>

<style lang="scss" scoped>
.database-list-container {
  padding:0 20px;
  background: #fff;
}

.database-list-header {
  h2 {
    margin: 0;
    font-size: 1.125rem;
    height: 48px;
    line-height: 48px;
  }
  .database-list-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    margin: 10px 0 15px;
    .el-select {
      width: 330px;
    }
    .search-input {
      width: 240px;
    }
  }
  .flex1 {
    flex:1
  }
}

.database-cards {
  // height: 100%;
  cursor: pointer;
  :deep(.el-card__header) {
    padding: 10px 20px;
    border-bottom: none;
  }
  :deep(.el-card__body) {
    padding: 0 20px 10px;
  }
  .database-card {
    margin-bottom: 20px;
    overflow: visible;
    &:hover {
      .el-dropdown-link {
        visibility: visible;
      }
    }
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.database-type-icon {
  margin-right: 8px;
  font-size: 18px;
  .mone-svg-icon{
    width:40px;
    height:40px;
  }
}

.database-name {
  flex: 1;
  font-weight: 500;
  font-size: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.el-dropdown-link {
  visibility: hidden;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.database-description {
  color: #999;
  margin-bottom: 5px;
  height: 22px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.database-details {
  font-size: 14px;
}

.detail-item {
  display: flex;
  margin-bottom: 6px;
  height: 20px;
  overflow: hidden;
}

.detail-label {
  color: #909399;
  margin-right:5px;
  flex-shrink: 0;
}

.detail-value {
  color: #606266;
  // word-break: break-all;
}

.loading-container {
  padding: 20px;
}
</style>
