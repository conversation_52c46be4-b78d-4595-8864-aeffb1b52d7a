<template>
  <div class="markdown-content">
    <!-- 按顺序展示 Markdown 和 SQL -->
    <template v-for="(block, index) in contentBlocks" :key="index">
      <div
        v-if="block.type === 'markdown'"
        v-html="renderMarkdown(block.content)"
      ></div>
      <div v-else-if="block.type === 'sql'" class="code-container">
        <pre><code class="sql" v-html="highlightSql(block.content)"></code></pre>
        <el-button
          class="copy-btn"
          :icon="CopyDocument"
          circle
          @click="() => handleCopy(block.content)"
          @mouseenter="copyTip = '复制'"
          @mouseleave="copyTip = ''"
        />
        <span class="copy-tip" v-if="copyTip">{{ copyTip }}</span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from "vue";
import MarkdownIt from "markdown-it";
import hljs from "highlight.js/lib/core";
import sql from "highlight.js/lib/languages/sql";
import "highlight.js/styles/github.css";
import { CopyDocument } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

hljs.registerLanguage("sql", sql);

const md = new MarkdownIt({
  html: true,
});

const props = withDefaults(
  defineProps<{
    content: string;
  }>(),
  {
    content: "",
  }
);

const copyTip = ref('')

// 解析内容块
const contentBlocks = computed(() => {
  const blocks: { type: "markdown" | "sql"; content: string }[] = [];
  let content = props.content.replace(/\\n/g, "\n").replace(/^"|"$/g, ""); // 移除首尾的引号

  // 分割内容
  const parts = content.split(/(```sql[\s\S]*?```)/g);

  parts.forEach((part) => {
    if (part.trim()) {
      if (part.startsWith("```sql")) {
        // SQL 块，移除引号和标记
        const sqlContent = part
          .replace(/```sql\n?/, "") // 移除开始标记
          .replace(/```$/, "") // 移除结束标记
          .replace(/^"|"|$/g, "") // 移除SQL内容中的引号
          .trim();

        blocks.push({ type: "sql", content: sqlContent });
      } else {
        // Markdown 块
        blocks.push({
          type: "markdown",
          content: part.trim().replace(/^"|"|$/g, ""),
        });
      }
    }
  });

  return blocks;
});

// 渲染 Markdown
const renderMarkdown = (content: string) => {
  return md.render(content);
};

// SQL 美化函数简化版
function formatSQL(sql: string) {
  return (sql || '')
    .replace(/\s+/g, ' ')  // 合并多个空格
    .replace(/\s*SELECT\s+/gi, 'SELECT ')
    .replace(/\s*,\s*/g, '\n        , ')  // 逗号开头的行缩进8个空格
    .replace(/\s*FROM\s+/gi, '\nFROM ')
    .replace(/\s*WHERE\s+/gi, '\nWHERE ')
    .replace(/\s*GROUP BY\s+/gi, '\nGROUP BY ')
    .replace(/\s*HAVING\s+/gi, '\nHAVING ')
    .replace(/\s*ORDER BY\s+/gi, '\nORDER BY ')
    .replace(/\s*AND\s+/gi, '\n        AND ')  // AND 开头的行缩进8个空格
    .replace(/\s*OR\s+/gi, '\n        OR ')    // OR 开头的行缩进8个空格
    .replace(/\(\s*SELECT/gi, '(\n                SELECT')  // 子查询缩进16个空格
    .trim();
}

// 高亮 SQL
const highlightSql = (content: string) => {
  let formattedSql = formatSQL(content);
  return hljs.highlight(formattedSql, { language: "sql" }).value;
};

const handleCopy = async (code: string) => {
  try {
    // 优先使用现代API
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(code)
    } else {
      // 降级到传统方法
      fallbackCopyTextToClipboard(code)
    }
    ElMessage.success('复制成功')
    copyTip.value = '已复制!'
    setTimeout(() => {
      copyTip.value = ''
    }, 1500)
  } catch (err) {
    // 如果现代API失败，尝试降级方案
    try {
      fallbackCopyTextToClipboard(code)
      ElMessage.success('复制成功')
      copyTip.value = '已复制!'
      setTimeout(() => {
        copyTip.value = ''
      }, 1500)
    } catch (fallbackErr) {
      ElMessage.error('复制失败')
      copyTip.value = '复制失败'
    }
  }
}

// 降级复制方案
const fallbackCopyTextToClipboard = (text: string) => {
  const textArea = document.createElement('textarea')
  textArea.value = text

  // 避免在移动设备上弹出键盘
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  textArea.style.opacity = '0'
  textArea.style.pointerEvents = 'none'
  textArea.setAttribute('readonly', '')

  document.body.appendChild(textArea)

  // 兼容iOS
  if (navigator.userAgent.match(/ipad|iphone/i)) {
    const range = document.createRange()
    range.selectNodeContents(textArea)
    const selection = window.getSelection()
    selection?.removeAllRanges()
    selection?.addRange(range)
    textArea.setSelectionRange(0, 999999)
  } else {
    textArea.select()
  }

  const successful = document.execCommand('copy')
  document.body.removeChild(textArea)

  if (!successful) {
    throw new Error('降级复制方案失败')
  }
}
</script>

<style scoped lang="scss">
.markdown-content {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial,
    sans-serif;
  padding: 16px;
  border: 1px solid #e1e8ed;
  border-radius: 8px;
  margin: 8px 0px;
  background: #ffffff;
  color: #24292e;
}

:deep(h2) {
  font-size: 1.5em;
  margin: 16px 0;
  font-weight: 600;
  line-height: 1.25;
}

:deep(p) {
  margin: 12px 0;
  line-height: 1.6;
}

:deep(pre) {
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  background: #f6f8fa;
  border: 1px solid #e1e8ed;
  margin: 16px 0;
}

:deep(code) {
  font-family: "Monaco", "Consolas", monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #24292e;
}

:deep(.sql) {
  display: block;
  overflow-x: hidden;
  padding: 0;
  background: transparent;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.code-container {
  position: relative;
  margin: 16px 0;

  pre {
    padding: 16px;
    padding-right: 45px;
    margin: 0;
  }
}

.copy-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 6px;
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e8ed;
  color: #586069;
  opacity: 0.8;
  transition: all 0.3s;

  &:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.1);
    color: #24292e;
  }
}

.copy-tip {
  position: absolute;
  top: 8px;
  right: 45px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.3s;
}
</style>
