# Vue Flow 拖拽预览功能说明

## 功能概述

这个组件实现了一个增强的表关联关系图，支持拖拽时显示当前节点及其后续节点的预览效果。

## 主要特性

### 1. 拖拽预览
- **节点组拖拽**: 拖拽一个节点时，会同时移动该节点及其所有后续连接的节点
- **预览副本**: 在原位置显示半透明的预览副本，让用户清楚看到拖拽的内容
- **放置预览**: 在可放置位置显示虚线预览框，指示放置后的位置

### 2. 视觉效果
- **半透明预览**: 预览节点使用 50% 透明度和缩放效果
- **虚线连接**: 预览连线使用虚线样式
- **动画效果**: 放置预览带有脉冲动画
- **高亮提示**: 拖拽中的节点有阴影高亮效果

### 3. 交互逻辑
- **碰撞检测**: 智能检测可放置的目标位置
- **无效拖拽**: 禁止拖拽到前置节点或自身
- **自动布局**: 放置时自动计算合适的位置

## 核心函数

### `createDragPreview(nodes, edges)`
创建拖拽预览副本
```javascript
// 创建半透明预览节点
const copyNodes = nodes.map(node => ({
  ...node,
  id: uniqueId("n_preview_"),
  style: { opacity: 0.5, pointerEvents: 'none' },
  class: 'drag-preview-node'
}));
```

### `updateDragNodesPosition(draggedNode, movementX, movementY)`
同步更新拖拽节点组位置
```javascript
// 保持相对位置关系移动所有节点
dragNodes.forEach(node => {
  const relativeX = node.position.x - draggedNode.position.x;
  const relativeY = node.position.y - draggedNode.position.y;
  // 更新位置...
});
```

### `updateDropPreview(targetNode)`
更新放置预览
```javascript
// 显示放置位置预览
nodes.value.push({
  id: preview_nId,
  type: "virtual",
  position: newPosition,
  style: { opacity: 0.7, border: '2px dashed #005EE0' }
});
```

## 使用方法

1. **基本拖拽**: 直接拖拽任意节点（除根节点外）
2. **查看预览**: 拖拽时观察原位置的半透明预览
3. **放置节点**: 拖拽到目标位置看到虚线预览框时释放

## 样式定制

### CSS 类名
- `.drag-preview-node`: 拖拽预览节点
- `.drag-preview-edge`: 拖拽预览连线
- `.preview-node`: 预览节点状态
- `.active-sheet-node`: 激活/相交节点

### 自定义样式
```scss
.drag-preview-node {
  opacity: 0.6 !important;
  transform: scale(0.95);
  border: 2px dashed #005EE0 !important;
  background: rgba(0, 94, 224, 0.1) !important;
}
```

## 注意事项

1. **性能优化**: 预览节点设置了 `pointer-events: none` 避免事件冲突
2. **内存管理**: 拖拽结束后会自动清理预览节点
3. **兼容性**: 保留了原有的 `copyDragFlow` 函数确保向后兼容
4. **类型安全**: 建议为函数参数添加 TypeScript 类型定义

## 扩展建议

1. **添加动画**: 可以为节点移动添加过渡动画
2. **多选拖拽**: 支持选择多个节点同时拖拽
3. **吸附对齐**: 添加网格吸附和自动对齐功能
4. **撤销重做**: 集成操作历史记录功能
