<script lang="ts" setup>
import { MoreFilled, Search } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { get } from "lodash-es";
import { ref, toRaw } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  delKnowledgeApi,
  getKnowledgeListApi,
  getTagList,
  updateKnowledgeTag,
} from "@/common/apis/knowledge";

import preLabelSelect from "@/pages/agent/components/preLabelSelect.vue";
import tagSelect from "./components/tagSelect/tagSelect.vue";

const router = useRouter();
const route = useRoute();

const tagsVal = ref<(string | number)[]>([]); // 知识库类型
const search_name = ref<string>(""); // 关键字
const knowledgeList = ref<any[]>([]); // 知识库列表
const loading = ref<boolean>(false); // 加载状态
const tagOptions = ref<any>([]); // 标签列表

// 获取知识库列表
function getKnowledgeList(params: object = {}) {
  loading.value = true;
  knowledgeList.value = [];
  // 拼接查询参数
  let reqParams: object = {
    keyword: search_name.value,
    labels: tagsVal.value
    // workspace_id: route.query.spaceCode || "",
  };
  getKnowledgeListApi({
    page: 1,
    pageSize: 1000,
    ...reqParams,
    ...params,
  })
    .then((res) => {
      const response = res as { data: any };
      knowledgeList.value = get(response, "data", []).map((item) => {
        item.labelIds = item.labels.map((label: any) => label.id);
        return item;
      });
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
getKnowledgeList();

// 新增知识库
function handleAddKnowledge() {
  router.push({
    path: "/knowledge/createKnowledge",
    query: {
      spaceCode: route.query.spaceCode || "",
    },
  });
}

// 下拉菜单命令
function handleDropdownCommand(command: string, item: any) {
  switch (command) {
    case "detail":
      const { id = "" } = item as any;
      router.push({
        name: "knowledgeDetail",
        query: {
          spaceCode: route.query.spaceCode || "",
          type: "edit",
          knowledge_id: id,
        },
      });
      break;
    case "delete":
      handleDeleteKnowledge(item);
      break;
  }
}

// 删除知识库
function handleDeleteKnowledge(item: any) {
  ElMessageBox.confirm("确定删除该知识库吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    delKnowledgeApi({
      name: item.name,
    })
      .then((res: any) => {
        ElMessage.success("删除成功");
        getKnowledgeList();
      })
      .catch(() => {
        ElMessage.error("删除失败");
      });
  });
}

function initTagOptions() {
  getTagList({
    page: 1,
    page_size: 99999,
  }).then((res: any) => {
    tagOptions.value = res?.data?.data || [];
  });
}
initTagOptions();

function changeItemTag(val: any, item: any) {
  loading.value = true;
  updateKnowledgeTag({
    space_id: item.id,
    label_ids: val,
  })
    .then((res: any) => {
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
      getKnowledgeList();
    });
}
</script>

<template>
  <div class="knowledge-manage-container">
    <p class="head-title">知识库</p>
    <!-- <template> -->
      <div class="search-container">
        <pre-label-select
          v-model="tagsVal"
          label="标签："
          clearable
          multiple
          @change="getKnowledgeList()"
        >
          <el-option
            v-for="item in tagOptions"
            :key="item.label_name"
            :label="item.label_name"
            :value="item.label_name"
          />
        </pre-label-select>
        <el-input
          v-model="search_name"
          style="width: 200px"
          placeholder="输入关键字"
          :suffix-icon="Search"
          @keyup.enter="getKnowledgeList()"
        />
        <div class="btns">
          <el-button type="primary" @click="handleAddKnowledge">
            <i
              class="iconfont ChatData-jiahao"
              style="font-size: 13px; margin-right: 5px"
            />
            新增知识库
          </el-button>
        </div>
      </div>
      <!-- 知识库列表 -->
      <div
        class="knowledge-list-container"
        v-loading="loading"
        v-if="knowledgeList && knowledgeList.length"
      >
        <el-card v-for="item in knowledgeList" :key="item.id">
          <!-- 头部区域 -->
          <template #header>
            <div class="left">
              <i class="iconfont ChatData-yingyong" />
              <p class="title">
                {{ item.name }}
              </p>
            </div>
            <div class="btns">
              <el-dropdown @command="(cmd) => handleDropdownCommand(cmd, item)"  :teleported="false" :tabindex="9999">
                <el-icon><MoreFilled /></el-icon>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="detail"> 详情 </el-dropdown-item>
                    <!-- <el-dropdown-item command="edit"> 编辑 </el-dropdown-item> -->
                    <el-dropdown-item
                      command="delete"
                      divided
                      style="color: red"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
          <!-- 内容区域 -->
          <div>
            <section class="desc">
              {{ item.desc }}
            </section>
          </div>

          <!-- 底部区域 -->
          <template #footer>
            <div class="tags">
              <tagSelect
                v-model="item.labelIds"
                :options="tagOptions"
                placeholder="点击选择标签"
                collapse-tags-tooltip
                :clearable="false"
                collapse-tags
                simple-show
                suffix-icon=""
                append-to="body"
                @update="initTagOptions"
                @change="(val) => changeItemTag(val, item)"
              />
            </div>
          </template>
        </el-card>
      </div>
      <div class="empty" v-else-if="!knowledgeList.length && !loading">
        <el-empty description="暂无知识库数据" />
        <el-button type="primary" @click="handleAddKnowledge">
          + 新增知识库
        </el-button>
      </div>
    <!-- </template> -->
  </div>
</template>

<style lang="scss" scoped>
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
.btns {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: end;
  .el-dropdown {
    visibility: hidden;
  }
}
.el-card {
  &:hover {
    .el-dropdown {
      visibility: visible;
    }
  }
}
.start-chart {
  padding: 5px;
  border-radius: 8px;
  background: #ebf3fd;
  color: #005ee0;
}
.knowledge-manage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 150px;
  background: white;
  .head-title {
    font-size: 18px;
    color: #262626;
    line-height: 20px;
    margin: 15px 0px;
    font-weight: 600;
  }
  .search-container {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
    :deep(.el-input__inner) {
      font-size: 13px;
    }
  }
  .knowledge-list-container {
    // margin-top: 15px;
    flex: 1 1 auto;
    margin-top: 10px;
    padding: 5px 0px;
    overflow: auto;
    width: 100%;
    display: grid;
    gap: 15px;
    grid-auto-rows: 160px;
    // 默认4列
    grid-template-columns: repeat(4, 1fr);

    // 中等屏幕4列
    // @media (min-width: 1200px) {
    //   grid-template-columns: repeat(4, 1fr);
    // }

    // 大屏幕5列
    // 2k以上分辨率5列

    @media (min-width: 2048px) {
      grid-template-columns: repeat(5, 1fr);
    }
    :deep(.el-card) {
      // height: 200px;
      // width: 30%;
      box-sizing: border-box;
      cursor: pointer;
      display: flex;
      flex-direction: column;

      box-shadow: 0px 2px 6px 0px #fafafa;
      &:hover {
        box-shadow: 0px 2px 6px 0px #ededed;
      }
      .el-card__header {
        border-bottom: none;
        box-sizing: border-box;
        height: 50px;
        display: flex;
        align-items: center;
        padding-bottom: 0px;
        .left {
          display: flex;
          align-items: center;
          .iconfont {
            font-size: 20px;
            font-weight: bold;
            color: #005ee0;
          }
          .title {
            font-weight: 600;
            font-size: 16px;
            color: #262626;
            margin: 10px 0px;
            margin-left: 10px;
          }
        }
      }
      .el-card__body {
        flex: 1 1 auto;
        box-sizing: border-box;
        padding: 5px 20px;
        min-width: 0; // 添加这行，强制允许收缩
        overflow: hidden; // 添加这行，防止内容溢出

        .desc {
          font-size: 12px;
          color: #8c8c8c;
          line-height: 18px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; // 显示两行
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all; // 允许在任意字符间断行
        }
      }
      .el-card__footer {
        border-top: none;
        display: flex;
        align-items: center;
        gap: 10px;
        height: 26px;
        padding: 10px 20px 20px 20px;
        // margin-bottom: 10px;
        box-sizing: content-box;
        .tags {
          display: flex;
          align-items: center;
          gap: 10px;
          flex: 1 1 auto;
          box-sizing: border-box;
        }
      }
    }
  }
}

.empty {
  text-align: center;
  margin: 100px auto;
}
</style>
