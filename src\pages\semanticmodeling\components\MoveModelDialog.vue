<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { Check } from '@element-plus/icons-vue';

const props = defineProps({
  visible: Boolean,
  treeData: {
    type: Array,
    default: () => []
  },
  currentNode: {
    type: Object,
    default: () => ({})
  }
});
const emit = defineEmits(['update:visible', 'success']);

const selectedFolderId = ref<number | null>(null);

// 过滤出文件夹（数据主题分组）
const folderOptions = ref<any[]>([]);

watch(() => props.visible, (val) => {
  if (val) {
    // 过滤出文件夹选项（parent_id为0或null的为文件夹）
    folderOptions.value = props.treeData
      .filter((folder: any) => !folder.parent_id) // 文件夹类型
      .map((folder: any) => ({
        id: folder.id,
        name: folder.name,
      }));

    // 重置选择
    console.log(props.currentNode,'11')
    selectedFolderId.value = props.currentNode.parent_id;
  }
});

function handleClose() {
  emit('update:visible', false);
}

function handleSubmit() {
  if (!selectedFolderId.value) {
    ElMessage.warning('请选择要移动到的数据主题分组');
    return;
  }

  emit('success', {
    targetFolderId: selectedFolderId.value,
    currentNode: props.currentNode
  });
  handleClose();
}

function handleFolderSelect(folderId: number) {
  selectedFolderId.value = folderId;
}
</script>

<template>
  <el-dialog
    :model-value="props.visible"
    title="移动到"
    width="400"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="move-dialog-content">
      <!-- <div class="current-item">
        <span class="label">当前项目：</span>
        <span class="name">{{ currentNode?.name }}</span>
      </div> -->

      <div class="folder-list">
        <div class="list-title">选择目标数据主题分组：</div>
        <div class="folder-options">
          <div
            v-for="folder in folderOptions"
            :key="folder.id"
            class="folder-item"
            :class="{ 'selected': selectedFolderId === folder.id }"
            @click="handleFolderSelect(folder.id)"
          >
            <i class="ChatData-zhuti iconfont"></i>
            <span class="folder-name">{{ folder.name }}</span>
            <el-icon v-if="selectedFolderId === folder.id" class="check-icon">
              <Check />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定移动</el-button>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.move-dialog-content {
  .current-item {
    margin-bottom: 16px;
    padding: 12px;
    background: #f5f7fa;
    border-radius: 6px;

    .label {
      color: #606266;
      font-size: 14px;
    }

    .name {
      color: #303133;
      font-weight: 500;
      margin-left: 8px;
    }
  }

  .folder-list {
    .list-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 12px;
    }

    .folder-options {
      max-height: 320px;
      overflow-y: auto;

      .folder-item {
        display: flex;
        align-items: center;
        padding: 10px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 8px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          border-color: #005EE0;
          background: #EBF3FD;
        }

        &.selected {
          border-color: #005EE0;
          background: #EBF3FD;
        }

        .iconfont {
          margin-right: 8px;
          font-size: 14px;
        }

        .folder-name {
          flex: 1;
          color: #303133;
        }

        .check-icon {
          color: #005EE0;
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
