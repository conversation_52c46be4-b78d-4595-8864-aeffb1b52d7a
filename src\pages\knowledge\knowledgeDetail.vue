<script setup lang="ts">
import { ElMessage } from "element-plus"
import { cloneDeep, get } from "lodash-es"
import { useRoute, useRouter } from "vue-router"
import { delKnowledgeApi, getKnowledgeListApi } from "@/common/apis/knowledge"
import backSelect from "@/pages/agent/components/backSelect.vue"
import detailContent from "./knowledgeDetailManage.vue"

const router = useRouter()
const route = useRoute()
const knowledgeList = ref<any[]>([]) // 知识库列表
const curEditAgent = ref<string>((route?.query?.knowledge_id ?? "") as string) // 当前编辑助手
const detailData = ref<any>({}) // 编辑详情

// 是否为编辑
const isEdit = computed<boolean>(() => {
  return route?.query?.type === "edit"
})
// 是否为查看
const isView = computed<boolean>(() => {
  return route?.query?.type === "view"
  // return true;
})

const loading = ref<boolean>(false)

provide("knowledgeDetail", detailData)
// ---------------------------
async function handleAgentChange() {
  await getKnowledgeDetail()
}

// 下拉菜单命令
function handleDropdownCommand(command: string) {
  switch (command) {
    case "delete":
      handleDeleteAgent()
      break
  }
}
// 删除知识库
function handleDeleteAgent() {
  ElMessageBox.confirm("确定删除该知识库吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    delKnowledgeApi({
      name: detailData?.value?.name
    })
      .then(async (res: any) => {
        ElMessage.success("删除成功")
        // 删除后重置
        await getKnowledgeList()
        // 重新设置当前选项 默认为第一个
        curEditAgent.value = String(knowledgeList.value[0]?.id)
        getKnowledgeDetail()
      })
      .catch(() => {
        ElMessage.error("删除失败")
      })
  })
}

// 后退到助手首页
function goBack(): void {
  router.push({
    name: "knowledge",
    query: {
      spaceCode: route.query.spaceCode || ""
    }
  })
}

// 获取知识库详情
function getKnowledgeDetail(customAppCode?: string) {
  loading.value = true
  try {
    const same = knowledgeList.value.find((item: any) => item.id == curEditAgent.value)
    detailData.value = cloneDeep(same)

    loading.value = false
  } catch (error) {
    detailData.value = {}
    loading.value = false
  }
}

// 获取知识库列表
async function getKnowledgeList(params: object = {}) {
  loading.value = true
  knowledgeList.value = []
  // 拼接查询参数
  let reqParams: object = {
    // app_name: app_name.value,
    // app_code: app_code.value,
    // published: published.value !== "all" ? published.value : "",
    // workspace_id: route.query.spaceCode || "",
  }
  await getKnowledgeListApi({
    page: 1,
    pageSize: 1000,
    ...reqParams,
    ...params
  })
    .then((res) => {
      const response = res as { data: any }
      knowledgeList.value = get(response, "data", [])
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}
onMounted(async () => {
  // 如果是编辑，则获取助手详情进行数据初始化
  // if (isEdit.value || isView.value) {
  await getKnowledgeList()
  // 初始化选中、获取助手列表
  await getKnowledgeDetail()
  // }
})
</script>

<template>
  <div class="knowledge-manage-container" v-loading="loading">
    <div class="head">
      <div class="title">
        <!-- 切换知识库 -->
        <back-select
          v-model="curEditAgent"
          :data="knowledgeList"
          label="name"
          :key="curEditAgent"
          value="id"
          @change="handleAgentChange"
          @back="goBack"
        />
      </div>
      <!-- 头部操作 -->
      <div class="btns">
        <!-- 编辑模式下 -->
        <el-dropdown @command="(cmd) => handleDropdownCommand(cmd)">
          <el-icon><MoreFilled /></el-icon>

          <template #dropdown>
            <!-- <el-dropdown-menu>
              <el-dropdown-item command="edit" divided>编辑</el-dropdown-item>
            </el-dropdown-menu> -->
            <el-dropdown-menu>
              <el-dropdown-item command="delete" divided>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <!-- 底部描述 -->
      <div class="head-desc">
        <span class="desc">
          {{ detailData?.desc || "--" }}
        </span>
        <span class="other-text">
          <el-space alignment="start" :size="48">
            <span>存储类型：{{ detailData?.vector_type }}</span>
            <span>领域类型：{{ detailData?.domain_type }}</span>
            <!-- <span>知识库类型：{{detailData?.knowledge_type }}</span> -->
          </el-space>
        </span>
      </div>
    </div>
    <div class="config" v-if="detailData.id">
      <detailContent :can-edit="!isView" :id="detailData.id" />
    </div>
  </div>
</template>

  <style lang="scss" scoped>
.btns {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: end;
  * {
    margin: 0px;
  }
}
.knowledge-manage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .head {
    width: 100%;
    height: 70px;
    padding: 5px 20px;
    // gap: 15px;
    box-sizing: border-box;
    box-shadow: 0px 1px 0px 0px #ededed;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    .title {
      display: flex;
      align-items: center;
      gap: 15px;
      flex: 1;
      .back-icon-wrapper {
        padding: 5px;
        border-radius: 8px;
        color: #005ee0;
        /* font-weight: bold; */
        background: #ebf3fd;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
    .head-desc {
      width: 100%;
      // margin-top: 12px;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #8c8c8c;
      .desc {
        flex: 1;
        margin-left: 43px;
      }
    }
  }
  .config {
    flex: 1;
    overflow: hidden;

    padding-bottom: 10px;
  }
}
</style>
