<template>
  <div class="edit-sql-container">
    <!-- 非编辑状态 -->
    <p v-if="!actionEdit" style="display: flex; align-items: center; gap: 6px">
      <span class="un-edit-text">{{ unEditText }}</span>
      <i
        class="iconfont ChatData-bianji edit-icon"
        v-if="showEdit"
        @click="handleEdit"
      ></i>
    </p>
    <!-- 编辑状态 -->
    <template v-else>
      <div class="options" v-loading="editContentLoading">
        <!-- 已保存的过滤条件 -->
        <div class="exit-filter">
          <el-tag
            type="info"
            class="exit-filter-item"
            v-for="(item, index) in filterList"
            :key="index + item.left"
            closable
            @close="handleCloseConditionItem(item, index)"
          >
            <!-- 查询条件展示 -->
            <template v-if="item.type === 'search'">
              <span>{{ getFieldEscape(item.left) }}:</span>

              <!-- 字符串 -->
              <template v-if="typeof item.right === 'string'">
                <span>{{ getOperatorEscape(item.operator) }}</span>
                <span>{{ item.right }}</span>
              </template>
              <!-- 范围\配置 -->
              <template v-else-if="item?.right?.showText">
                <span>为</span>
                <span>{{ item?.right?.showText }}</span>
              </template>
            </template>
            <!-- 列展示 -->
            <template v-else-if="item.type === 'column'">
              <span>分组：{{ getFieldEscape(item.left) }}</span>
            </template>
          </el-tag>
        </div>
        <span
          class="add-filter"
          ref="addConditionBtnRef"
          @click="handleAddCondition"
        >
          <i class="iconfont ChatData-jiahao"></i>
        </span>

        <span class="confirm-btn" @click="saveCondition">确定</span>
        <span class="cancel-btn" @click="handleClose">取消</span>
      </div>
    </template>
    <!-- 条件配置区域 -->
    <el-popover
      popper-class="filter-list-container"
      popper-style="padding: 20px;"
      :virtual-ref="addConditionBtnRef"
      :visible="conditionPopoverVisible"
      width="700"
      :hide-after="0"
      :persistent="true"
    >
      <div class="filter-list">
        <!-- 过滤条件配置 -->
        <div
          class="filter-item"
          v-for="(item, index) in cacheFilterList"
          :key="item.id"
        >
          <span class="filter-index">
            {{ index + 1 }}
          </span>
          <!-- 条件配置 -->
          <sql-edit-item :data="item" style="flex: 1" />

          <!-- 右侧操作按钮 -->
          <div class="filter-item-tools">
            <i
              class="iconfont ChatData-shanchu"
              @click="handleDeleteItem(index)"
            ></i>
          </div>
        </div>
      </div>
      <!-- 底部操作按钮 -->
      <div class="filter-bottom-tools">
        <span
          class="add-search-condition"
          @click.stop="handleAddSearchCondition"
        >
          <i class="iconfont ChatData-jiahao"></i>
          添加查询条件
        </span>
        <span class="add-column" @click.stop="handleAddColumn">
          <i class="iconfont ChatData-jiahao"></i>
          添加列
        </span>
        <div class="toggle-condition-btn">
          <el-button type="primary" size="small" @click="updateCondition">
            保存
          </el-button>
          <el-button size="small" @click="setConditionPopoverVisible(false)">
            取消
          </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts" async>
import { ref, computed, defineProps, inject, nextTick } from "vue";
import type { ElPopover } from "element-plus";
import { get, cloneDeep, omit } from "lodash-es";
import { getSqlToJsonApi } from "@/common/apis/chat";
import sqlEditItem from "./sqlEditItem.vue";
import * as config from "./config";

// 过滤条件每一item格式
interface FilterItem {
  type: string;
  [key: string]: any;
}
// 是否开启编辑
const actionEdit = ref(false);
// 过滤条件数据
const filterList = ref<FilterItem[]>([]);
// 缓存编辑前的数据
const cacheFilterList = ref<FilterItem[]>([]);

// 添加条件按钮Ref
const addConditionBtnRef = ref<HTMLElement>();
// 条件配置区域popover是否显示
const conditionPopoverVisible = ref(false);
// 编辑区域loading
const editContentLoading = ref(false);

// props定义
const props = defineProps({
  // 当前sql数据
  data: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  // 当前编辑的下标
  index: {
    type: Number,
    default: 0,
  },
  // 是否显示编辑
  showEdit: {
    type: Boolean,
    default: false,
  },
});
// inject
const resetSqlExecution: Function = inject("resetSqlExecution");
const fieldList: any = inject("fieldList");

// 获取fieldList Map
const fieldListMap = computed(() => {
  return fieldList.value.reduce(
    (acc: Record<string, any>, item: Record<string, any>) => {
      acc[item.field] = item;
      return acc;
    },
    {}
  );
});

// 获取最后执行的sql
const lastExecutionSql = computed(() => {
  return get(props.data, "context.sqlData.lastExecutionSql.data", "");
});
// 获取sql_edit数据
const sqlEditData = computed(() => {
  return get(props.data, "context.sqlData.sql_edit", {});
});
// 未编辑时显示的文本
const unEditText = computed(() => {
  const {
    where = {},
    columns = [],
    having = {},
    groupBy = [],
  } = sqlEditData.value.data || {};
  // 优先以originConfig 为准
  let conditions = where.conditions || [];
  let str = `先筛选`;
  conditions.forEach((item: Record<string, any>, index: number) => {
    let label = "";
    let right = "";
    let operator = "";
    if (
      item.right?.max?.value ||
      item.right?.min?.value ||
      typeof item.right === "object"
    ) {
      label = getFieldEscape(item.left);
      operator = "为";
      right = get(
        item,
        "right.showText",
        getRangeRightText(item.right, item.leftType || fieldListMap.value[item.left]?.data_type)
      );
    } else {
      label = getFieldEscape(item.left);
      operator = getOperatorEscape(item.operator);
      // 空/非空 不显示right
      if (item.operator !== "is" && item.operator !== "is not") {
        right = item.right;
      } else {
        right = "";
      }
    }
    str += `${label} ${operator} ${right}${
      index < conditions.length - 1 ? `、` : ``
    }`;
  });
  str += `的数据`;
  // 生成groupBy
  str += `，再分组`;
  (groupBy || []).forEach((item: string, index: number) => {
    str += `${getFieldEscape(item) || item}${index < groupBy.length - 1 ? "、" : "，"}`;
  });
  str += `最后计算`;
  (columns || []).forEach((item: Record<string, any>, index: number) => {
    const { alias, expression } = item;
    !groupBy.includes(expression) && (str += `${alias || expression || ""}${index < columns.length - 1 ? "、" : "。"}`);
  });
  return str;
});

// -------------------------------------------------

// 获取范围的right文本
function getRangeRightText(right: Record<string, any>, leftType?: string) {
  let str = "";
  if (leftType === "Datetime") {
    str = right.showText || `${right.min.value} - ${right.max.value}`;
  } else {
    if (right?.min?.value && right?.max?.value) {
      str = `${right.min.include ? "大于等于" : "大于"}${right.min.value} 且 ${
        right.max.include ? "小于等于" : "小于"
      }${right.max.value}`;
    } else if (!!right?.min?.value && !right?.max?.value) {
      str = `${right.min.include ? "大于等于" : "大于"}${right.min.value}`;
    } else if (!right?.min?.value && !!right?.max?.value) {
      str = `${right.max.include ? "小于等于" : "小于"}${right.max.value}`;
    }
  }

  return str;
}

// 开始添加/编辑条件
function handleAddCondition() {
  cacheFilterList.value = cloneDeep(filterList.value || []);
  nextTick(() => {
    setConditionPopoverVisible(true);
  });
}

// 设置条件配置区域popover是否显示
function setConditionPopoverVisible(visible: boolean = false) {
  conditionPopoverVisible.value = visible;
}

// popover 内添加一条查询条件
function handleAddSearchCondition() {
  cacheFilterList.value.push({
    type: "search",
    left: "",
    operator: "",
    right: "",
  });
}

// popover 内添加一条列
function handleAddColumn() {
  cacheFilterList.value.push({
    type: "column",
    left: "",
    alias: "",
    expression: "",
    operator: "",
    right: {},
  });
}

// popover 内删除一项条件
function handleDeleteItem(index: number) {
  cacheFilterList.value.splice(index, 1);
}

// 删除已保存的一项条件
function handleCloseConditionItem(item: Record<string, any>, index: number) {
  filterList.value.splice(index, 1);
}

// 更新条件 至当前已有的过滤存储
function updateCondition() {
  console.log(
    "updateCondition",
    cacheFilterList.value,
    filterList.value,
    props.index
  );
  // 更新条件
  filterList.value = [...cacheFilterList.value];
  // 关闭弹窗
  setConditionPopoverVisible(false);
}

// 保存条件至历史记录
function saveCondition() {
  console.log(
    "saveCondition",
    cacheFilterList.value,
    filterList.value,
    props.index
  );
  setConditionPopoverVisible(false);
  // 重新更新历史记录
  if (resetSqlExecution) {
    // console.log(renderSqlList(filterList.value), "renderSqlList");
    resetSqlExecution(
      renderSqlList(filterList.value),
      cloneDeep(filterList.value),
      props.index
    );
    nextTick(() => {
      actionEdit.value = false;
    });
  }
}

// 开始编辑
async function handleEdit() {
  actionEdit.value = true;
  editContentLoading.value = true;
  try {
    // 获取sql_edit数据， 从历史中取或重新查询
    let sql_edit =
      sqlEditData.value ||
      (await getSqlToJsonApi({
        sql: lastExecutionSql.value,
        action: "sql2json",
        option: {
          dialect: "mysql",
        },
        db_type: "sql",
      }));
    //  根据最后执行的sql 获取 sql的json格式化对象便于编辑
    editContentLoading.value = false;
    const {
      columns = [],
      where = {},
      originConfig,
      groupBy,
    } = sql_edit.data || {};
    // 有originConfig 时 直接用originConfig
    if (originConfig && originConfig.length) {
      filterList.value = cloneDeep(originConfig);
      cacheFilterList.value = cloneDeep(originConfig);
    } else {
      // 没有originConfig 时,识别为初始生成 根据sql_edit 重新生成 sql json可视化结构
      let conditions = []; // 存储所有条件

      conditions = cloneDeep(where.conditions || []).reduce((pre, item) => {
        // where 内的默认为search
        item.type = "search";
        // 初始化 字段类型
        item.leftType = get(fieldListMap.value, item.left, {}).data_type;
        // 嵌套区域的情况，要重新设置
        if (typeof item.right !== "string") {
          let newItem = renderSqlEditFormat(item);
          // 区域范围, 重新生成right结构
          pre.push({
            ...item,
            right: { ...newItem.right },
          });
        } else {
          // 字符串 直接push
          pre.push({
            ...item,
          });
        }
        return pre;
      }, []);
      // 根据groupby 分组
      groupBy.forEach((item: string) => {
        conditions.push({
          type: "column",
          left: item,
          operator: "",
          right: {
            group: "group",
          },
          // expression: item,
        });
      });
      // 编辑展示处、编辑popover 用相同格式数据,便于多次展开关闭时操作
      cacheFilterList.value = cloneDeep(conditions || []);
      filterList.value = cloneDeep(conditions || []);
    }
    console.log(cacheFilterList.value, filterList.value, "handle editr");
  } catch (err) {
    // .catch((err) => {
    editContentLoading.value = false;
    actionEdit.value = false;
    // });
  }
}

// 渲染sql编辑格式   后端返回的sql 转化为 编辑格式的sql
function renderSqlEditFormat(obj: Record<string, any>) {
  // 配置模板
  let newItem = {
    type: "search", // 类型
    left: obj.left,
    operator: obj.operator,
    right: {}, // 配置
  };
  // 获取现有的
  let { right } = obj || {};
  // 区域配置
  if (!!right.min || !!right.max) {
    // 数值区域配置
    if (config.numberTypeMap.includes(obj.leftType)) {
      right.showText = getRangeRightText(right, obj.leftType);
      // min max 都存在为区间
      if (!!right.min.value && !!right.max.value) {
        newItem.right = {
          rangeType: "range",
          min: { ...right.min },
          max: { ...right.max },
        };
      } else if (!!right.min.value && !right.max.value) {
        // 只有min 为最小值
        newItem.right = {
          rangeType: "min",
          min: { ...right.min },
        };
      } else if (!right.min.value && !!right.max.value) {
        // 只有max 为最大值
        newItem.right = {
          rangeType: "max",
          max: { ...right.max },
        };
      }
    } else if (obj.leftType === "Datetime") {
      let { min, max } = right;
      right.showText = `${min.value} - ${max.value}`;
    }
  }
  return newItem;
}

// 将编辑格式转换为sql list  编辑格式的sql 转化为后端sql
function renderSqlList(list: Record<string, any>[] = []) {
  if (!!list.length) {
    let newData = {
      conditions: [],
      columns: [],
      groupBy: [],
    };
    let { columns: originColumns = [], groupBy: originGroupBy = [] } =
      cloneDeep(toRaw(sqlEditData.value)).data || {};
    // 根据originColumns 和 originGroupBy 来获取初始生成的计算字段
    originColumns.forEach((item: Record<string, any>) => {
      let { alias, expression } = item;
      !originGroupBy.includes(expression) && newData.columns.push(item);
    });
    cloneDeep(list).map((item: Record<string, any>) => {
      const { right, operator, left, type } = item;
      // 生成过滤条件
      if (type === "search") {
        // 普通结构直接push
        if (typeof right === "string") {
          let otherPramas = {};
          // 不包含、不为空添加处理
          if (operator === "not contain") {
            otherPramas = {
              not: true,
              func: "FIND_IN_SET",
            };
          } else if (operator === "is") {
            otherPramas = {
              right: "NOT NULL",
            };
          } else if (operator === "is not") {
            otherPramas = {
              right: "NULL",
            };
          }
          newData.conditions.push({
            ...item,
            right: wrapWithSingleQuotes(right),
            ...otherPramas,
          });
          // 区域配置
        } else if (!!right?.max?.value || !!right?.min?.value) {
          let newItem: any = {
            left: left,
            operator: "scope",
            right: {},
          };
          if (right.min.value) {
            newItem.right.min = {
              ...right.min,
            };
          }
          if (right.max.value) {
            newItem.right.max = { ...right.max };
          }
          newData.conditions.push(newItem);
        } else {
          newData.conditions.push(item);
        }
      } else if (type === "column") {
        // 生成列配置
        const { alias, expression } = item;
        newData.columns.unshift({
          alias,
          expression: expression || item.left,
        });
        // 生成groupBy
        newData.groupBy.push(expression || item.left);
      }
    });
    return newData;
  }
}

// 处理字符串的单引号
function wrapWithSingleQuotes(str: string) {
  // 如果开头和结尾都已经有单引号，直接返回
  if (str.startsWith("'") && str.endsWith("'")) {
    return str;
  }
  // 否则添加单引号
  return `'${str}'`;
}

// 取消编辑
function handleClose() {
  setConditionPopoverVisible(false);
  actionEdit.value = false;
}

// 获取字段的转义
function getFieldEscape(field: string) {
  return (
    fieldListMap.value[field]?.alias || fieldListMap.value[field]?.description
  );
}

// 获取操作符的转义
function getOperatorEscape(operator: string) {
  return config.operatorListMap[operator]?.label || operator;
}
</script>

<style lang="scss" scoped>
.edit-sql-container {
  margin: 15px 0px;
  .un-edit-text {
    flex: 1;
  }
}
.edit-icon {
  font-size: 12px;
  color: #4c6f88;
  cursor: pointer;
  margin-left: 5px;
}
.options {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
  :deep(.exit-filter) {
    display: flex;
    align-items: center;
    gap: 6px;
    flex-wrap: wrap;
    .exit-filter-item .el-tag__content {
      display: flex;
      align-items: center;
      gap: 5px;

      cursor: pointer;
    }
  }

  .add-filter {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    background: #ebf3fd;
    color: #005ee0;
    border-radius: 4px;
    cursor: pointer;
    // font-size: 10px;
    i {
      font-size: 10px;
    }
  }
  .confirm-btn,
  .cancel-btn {
    font-weight: 600;
    font-size: 13px;
    color: #595959;
    cursor: pointer;
  }

  .confirm-btn {
    color: #005ee0;
  }
}
.filter-list-container {
  padding: 20px;

  .filter-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
    min-height: 50px;
    max-height: 400px;
    overflow-y: auto;
    .filter-item {
      display: flex;
      align-items: center;
      // justify-content: space-between;
      gap: 10px;
      .filter-index {
        font-size: 13px;
        width: 20px;
        text-align: center;
        color: #8c8c8c;
      }
      .filter-item-tools {
        i {
          font-size: 14px;
          cursor: pointer;
          color: #005ee0;
        }
      }
    }
  }
  .filter-bottom-tools {
    display: flex;
    align-items: center;
    margin-top: 10px;
    .add-search-condition,
    .add-column {
      // flex: 1;
      cursor: pointer;
      font-weight: bold;
      font-size: 13px;
      color: #005ee0;
      display: flex;
      align-items: center;
      gap: 5px;
      i {
        font-size: 10px;
      }
    }
    .add-column {
      margin-left: 10px;
    }
    .toggle-condition-btn {
      flex: 1;
      text-align: right;
    }
  }
}
</style>