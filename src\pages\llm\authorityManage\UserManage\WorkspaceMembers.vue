<template>
  <div class="members-section">
    <div class="members-header">
      <h3>{{ fromType === 'author' ? '用户管理' : '空间成员'}}</h3>
      <div class="members-actions">
        <el-input
          v-model="memberSearchKeyword"
          placeholder="输入关键字"
          class="member-search"
          clearable
        >
          <template #suffix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-popover
          placement="bottom"
          :visible="popoverVisibleBatchUpdate"
          width="300"
          :teleported="false"
          @after-leave="handleBatchUpdateCancel"
        >
          <div class="batch-update-content">
            <p>把已选择 {{ selectedMembers.length }} 成员的角色变更到</p>
            <el-select v-model="batchUpdateRole" placeholder="请选择角色">
              <el-option label="空间管理员" value="space_admin" />
              <el-option label="开发者" value="developer" />
              <el-option label="访问者" value="visitor" />
            </el-select>
            <div class="dialog-footer" style="margin-top: 16px;">
              <el-button size="small" type="primary" @click="handleConfirmBatchUpdate">确定</el-button>
              <el-button size="small" @click="handleBatchUpdateCancel">取消</el-button>
            </div>
          </div>
          <template #reference>
            <el-button v-permission="['member:set_role']" @click="handleBatchUpdate" :disabled="selectedMembers.length === 0" class="mr5">批量变更</el-button>
          </template>
        </el-popover>
        <el-popover
          placement="bottom"
          :visible="popoverVisibleBatchDelete"
          width="300"
          :teleported="false"
          @after-leave="handleBatchDeleteCancel"
        >
          <div class="batch-delete-content">
            <p>删除已选择 {{ selectedMembers.length }} 成员</p>
            <div class="dialog-footer" style="margin-top: 16px;">
              <el-button size="small" type="primary" @click="handleConfirmBatchDelete">确定</el-button>
              <el-button size="small" @click="handleBatchDeleteCancel">取消</el-button>
            </div>
          </div>
          <template #reference>
            <el-button  v-permission="['member:delete']" @click="handleBatchRemove" :disabled="selectedMembers.length === 0" class="mr5">批量移除</el-button>
          </template>
        </el-popover>
        <el-button type="primary" @click="handleAddMember" v-permission="['member:add']">
          <el-icon><Plus /></el-icon>
          添加成员
        </el-button>
      </div>
    </div>
    <el-table
      :data="filteredMembers"
      class="members-table"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="成员">
        <template #default="{ row }">
          <span>{{ row.name }}</span><span v-if="fromType === 'author'" style="color: #999;font-size: 12px; margin-left: 5px;">{{ row.email }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="账号" />
      <el-table-column prop="role" label="空间角色">
        <template #default="{ row }">
          <el-select v-model="row.role" @change="changeMemberRole(row)" :disabled="!is_super_admin && (!GetPermission(['member:set_role']) || (row.role === 'space_admin' ? true : false))">
            <el-option label="空间管理员" value="space_admin" />
            <el-option label="开发者" value="developer" />
            <el-option label="访问者" value="visitor" />
          </el-select>
        </template>
      </el-table-column>
      <el-table-column prop="resource_count" label="资源权限" align="center" v-if="fromType === 'author'">
        <template #default="{ row }">
          <span v-if="row.resource_count == 0 ">{{ row.resource_count }}</span>
          <el-popover
            v-else
            placement="bottom"
            width="400"
            trigger="click"
            @show="() => handleShowResourcePopover(row,'resource_count')"
          >
            <template #reference>
              <span class="resource-count-link">{{ row.resource_count }}</span>
            </template>
            <div v-if="row._resourceLoading" style="text-align:center;padding:20px;">加载中...</div>
            <div v-else>
              <template v-if="row._resourceList && row._resourceList.length">
                <div v-for="item in row._resourceList" :key="item.group" style="margin-bottom:12px;">
                  <template v-if="item.count">
                    <div style="font-weight:bold;">{{ item.group }} ({{ item.resources.length }})</div>
                    <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:6px;">
                      <span v-for="val in item.resources" :key="val.name" class="tag">{{ val.name }}</span>
                    </div>
                  </template>
                </div>
              </template>
              <div v-else style="text-align:center;color:#999;">暂无资源</div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="permission_count" label="数据权限" align="center" v-if="fromType === 'author'">
        <template #default="{ row }">
          <span v-if="row.permission_count == 0 ">{{ row.permission_count }}</span>
          <el-popover
            v-else
            placement="bottom"
            width="400"
            trigger="click"
            @show="() => handleShowResourcePopover(row,'permission_count')"
          >
            <template #reference>
              <span class="resource-count-link">{{ row.permission_count }}</span>
            </template>
            <div v-if="row._resourceLoading" style="text-align:center;padding:20px;">加载中...</div>
            <div v-else>
              <template v-if="row._resourceList && row._resourceList.length">
                <div v-for="item in row._resourceList" :key="item.group" style="margin-bottom:12px;">
                  <template v-if="item.count">
                    <div style="font-weight:bold;">{{ item.group }} ({{ item.resources.length }})</div>
                    <div style="display:flex;flex-wrap:wrap;gap:8px;margin-top:6px;">
                      <span v-for="val in item.resources" :key="val.name" class="tag">{{ val.name }}</span>
                    </div>
                  </template>
                </div>
              </template>
              <div v-else style="text-align:center;color:#999;">暂无资源</div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column prop="joined_at" label="加入时间">
        <template #default="{ row }">
          {{ formatDate(row.joined_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="{ row }">
          <el-tooltip v-if="is_super_admin || GetPermission(['member:delete']) && row.role !== 'space_admin' " content="移除成员" placement="bottom">
            <i class="iconfont ChatData-yichu deleteIcon" @click="handleMemberAction(row)"></i>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="totalMembers"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <MemberSelect
      v-model:visible="addMemberDrawerVisible"
      :users="availableUsers"
      v-model:value="selectedUsers"
      :selected="selectedUsers"
      :role="newMemberRole"
      :members="members"
      @update:value="val => selectedUsers = val"
      @update:role="val => newMemberRole = val"
      @confirm="handleConfirmAddMember"
      @cancel="handleCancelAddMember"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus';
import { Search, Plus } from '@element-plus/icons-vue';
import MemberSelect from '@/pages/llm/workspaceManage/component/MemberSelect.vue';
import { postSapceMemberLApi,deleteSapceMemberLApi,putSapceMemberRoleLApi } from "@/common/apis/workspace";
import { getResourceNameAndCountApi, getDataSourceAndCountApi} from "@/common/apis/llm";
import { getSpaceCurrentUserApi } from "@/common/apis/llm";
import dayjs from 'dayjs'
import { useUserStore } from "@/pinia/stores/user"
const { privileges } = useUserStore()
const { is_super_admin } = privileges as any
const route = useRoute()
const memberSearchKeyword = ref('');
const members = ref<any[]>([]); // 实际项目应定义类型
const selectedMembers = ref<any[]>([]);
const batchUpdateRole = ref('');
const popoverVisibleBatchUpdate = ref(false);
const popoverVisibleBatchDelete = ref(false);
const addMemberDrawerVisible = ref(false);
const availableUsers = ref<any[]>([]); // 可添加成员列表
const selectedUsers = ref<any[]>([]);
const newMemberRole = ref('visitor');
const currentPage = ref(1);
const pageSize = ref(10);
const totalMembers = ref(0);
const spaceCode = computed(() => Number(route.query.spaceCode));
interface Props {
  fromType: String,
}
const props = defineProps<Props>()
//  监听spaceCode变化，调用空间成员列表接口
watch(spaceCode, (val,oval) => {
  if (val !== oval) {
    getSapceMember()
  }
});
const filteredMembers = computed(() => {
  if (!memberSearchKeyword.value) return members.value;
  return members.value.filter(m => m.name?.includes(memberSearchKeyword.value) || m.username?.includes(memberSearchKeyword.value));
});

const formatDate = (dateStr: any) => {
  return dateStr ? dayjs(String(dateStr)).format('YYYY-MM-DD HH:mm:ss') : '-'
}

function handleSelectionChange(val: any[]) {
  selectedMembers.value = val;
}
function handleBatchUpdate() {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请选择要变更的成员')
    return
  }
  popoverVisibleBatchUpdate.value = true

}
function handleBatchUpdateCancel() {
  popoverVisibleBatchUpdate.value = false;
  batchUpdateRole.value = '';
}
// 单个修改成员角色
function changeMemberRole(row:any) {
  putSapceMemberRoleLApi(spaceCode.value,{user_ids:[row.user_id],role_name:row.role}).then((res:any) => {
    if(res.success) {
      ElMessage.success('修改成功');
    }
  })
}
// 批量修改成员角色
const handleConfirmBatchUpdate = () => {
  const user_ids= selectedMembers.value.map(m => m.user_id)
  putSapceMemberRoleLApi(spaceCode.value,{user_ids:user_ids,role_name:batchUpdateRole.value}).then((res:any) => {
    if(res.success) {
      ElMessage.success(`成功变更 ${selectedMembers.value.length} 名成员角色`)
      popoverVisibleBatchUpdate.value = false
      batchUpdateRole.value = ''
      getSapceMember()
    }
  })
}
const handleBatchRemove = () => {
  if (selectedMembers.value.length === 0) {
    ElMessage.warning('请选择要移除的成员')
    return
  }
  popoverVisibleBatchDelete.value = true
}

function handleBatchDeleteCancel() {
  popoverVisibleBatchDelete.value = false;
}
// 批量删除
const handleConfirmBatchDelete = () => {
  const selectedNames= selectedMembers.value.map(m => m.username)
  deleteSapceMemberLApi(spaceCode.value, {usernames:selectedNames}).then((res) => {
    ElMessage.success(`成功删除 ${selectedMembers.value.length} 名成员`)
    popoverVisibleBatchDelete.value = false
    selectedMembers.value = []
    getSapceMember()
  });
}

function handleAddMember() {
  addMemberDrawerVisible.value = true;
}
const handleConfirmAddMember = ({ selected, role }: { selected: any, role: any }) => {
  selectedUsers.value = selected
  const params = {
    members:selected.map((item:any) => item.username),// 过滤名称，只添加成员
    // 过滤角色，只添加角色
    role:role
  }
  postSapceMemberLApi(spaceCode.value,params).then((res) => {
    ElMessage.success(`成功添加 ${selected.length} 名成员`)
    getSapceMember()
  })
}

const handleCancelAddMember = () => {
  addMemberDrawerVisible.value = false
  selectedUsers.value = []
  newMemberRole.value = 'visitor'
}
// 单个删除
const handleMemberAction = (member: any) => {
  ElMessageBox.confirm(
    `确定要移除用户"${member.name}"吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    deleteSapceMemberLApi(spaceCode.value, {usernames:[member.username]}).then((res) => {
      ElMessage.success("删除成功");
      getSapceMember()
    });
  }).catch(() => {
    // 用户取消删除
  })
}

function handleSizeChange(val: number) {
  pageSize.value = val;
}
function handleCurrentChange(val: number) {
  currentPage.value = val;
}
onMounted(() => {
   // 从查询参数判断是新增还是编辑
  getSapceMember()
})
function getSapceMember() {
  getSpaceCurrentUserApi(
    {
      workspace_id:spaceCode.value,
      page: currentPage.value,
      page_size: pageSize.value
    }).then((res:any) => {
    const response = res as { data: any}
    members.value = response?.data.items || []
    totalMembers.value = response?.data.total
  })
}
const handleShowResourcePopover = async (row: any,type:String) => {
  row._resourceLoading = true;
  try {
    let serviceAPi
    if(type === 'resource_count') {
      serviceAPi = getResourceNameAndCountApi({ username: row.username, workspace_id: spaceCode.value })
    } else {
      serviceAPi = getDataSourceAndCountApi({ username: row.username, workspace_id: spaceCode.value })
    }
    const res: any = await serviceAPi;
    row._resourceList = res.data
  } catch (e) {
    row._resourceList = [];
  } finally {
    row._resourceLoading = false;
  }
};
</script>

<style scoped>
.members-section {
  background: #fff;
  min-height: 400px;
  padding: 20px;
}
.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}
.member-search {
  width: 240px;
  margin-right: 16px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.batch-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

.add-member-content {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.member-search-input {
  margin-bottom: 16px;
}

.select-all-section {
  margin-bottom: 16px;
  text-align: right;
}

.available-members {
  flex: 1;
  overflow-y: auto;
}

.member-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

.member-item:hover {
  background-color: #f5f7fa;
}

.member-info {
  flex: 1;
}

.member-name {
  font-weight: 500;
  color: #303133;
}

.drawer-pagination {
  margin: 20px 0;
  text-align: center;
}

.selected-info {
  margin: 16px 0;
  color: #606266;
  font-size: 14px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.batch-update-content p,
.batch-delete-content p {
  margin-bottom: 16px;
  color: #606266;
}
.deleteIcon {
  cursor: pointer;
  font-size:14px;
  color:#4C6F88;
}
.tag {
  display: inline-block;
  background: #f2f2f2;
  margin: 4px 0 0 0;
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 14px;
}
.resource-count-link {
  color: #409EFF;
  cursor: pointer;
}
</style>
