<template>
  <div class="box-container">
    <div class="model-list-header">
      <h2>{{ $route.meta.title }}</h2>
      <div class="model-header">
        <div class="model-header-left">
          <pre-label-select
            v-model="filterType"
            label="模型类型："
            clearable
            @change="onSearch()"
          >
            <el-option
              v-for="item in WorkerType"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </pre-label-select>
          <el-input
            v-model="searchKey"
            placeholder="输入关键字搜索"
            style="width: 220px"
            clearable
            @input="onSearch"
          >
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
        <el-button type="primary" @click="addEvent">
          <el-icon>
            <Plus />
          </el-icon>
          新增模型
        </el-button>
      </div>
    </div>
    <div class="model-card-list">
      <el-row :gutter="20">
        <el-col
          v-for="item in filteredModelList"
          :key="item.model_name"
          :span="8"
          class="model-card-col"
        >
          <el-card shadow="hover" class="model-card">
            <div class="model-card-header">
              <img :src="modelLogo" class="model-logo" />
              <el-dropdown :teleported="false" :tabindex="9999"
                trigger="hover"
                @command="(command) => handleCommand(command, item)"
                style="padding-right: 78px; cursor: pointer"
              >
                <span class="el-dropdown-link">
                  <el-icon><MoreFilled /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="delete"
                      divided
                      style="color: red"
                    >
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-tag
                :type="getTypeTag(item.worker_type)"
                class="model-type-tag"
                effect="dark"
              >
                {{ getTypeDesc(item.worker_type) }}
              </el-tag>
            </div>
            <div class="model-card-title">{{ item.model_name }}</div>
            <div class="model-card-desc">{{ item.manager_host }}</div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage, ElMessageBox } from "element-plus";
import { ref, watch } from "vue";
import { CreateModelRequestData, LLM, DeleteModelRequestData } from "@/common/apis/llm/type";
import {
  createLLMtApi,
  getLLMListApi,
  getLLMTypeListApi,
  deleteLLMtApi
} from "@/common/apis/llm";
import {
  Delete,
  Edit,
  MoreFilled,
  Plus,
  Search,
} from "@element-plus/icons-vue";
import modelLogo from "@@/assets/images/docs/model_logo.png?url";
import preLabelSelect from "@/pages/agent/components/preLabelSelect.vue";

const route = useRoute()
const router = useRouter()

const WorkerType: Array<Record<string, any>> = [
  { key: "llm", label: "对话模型" },
  { key: "reranker", label: "重排模型" },
  { key: "text2vec", label: "向量模型" },
];

const modelList = ref<LLM.Model[]>([]);
const filteredModelList = ref<LLM.Model[]>([]);

const filterType = ref("");
const searchKey = ref("");

function getModeList() {
  getLLMListApi().then((res) => {
    const response = res as { data: LLM.Model[] };
    modelList.value = response.data || [];
    filteredModelList.value = [...response.data];
  });
}

const onSearch = () => {
  console.log(filterType.value, searchKey.value);
  filteredModelList.value = modelList.value.filter((item) => {
    if (searchKey.value !== "") {
      return item.model_name.includes(searchKey.value);
    }
    if (filterType.value === "" || filterType.value === undefined) {
      return true;
    }
    return filterType.value !== "" && item.worker_type === filterType.value;
  });
};

const handleCommand = (command: string, llm: LLM.Model) => {
  if (command === "edit") {
    EditEvent(llm);
  } else if (command === "delete") {
    DeleteEvent(llm);
  }
};

const addEvent = () => {
  router.push({
    path: "modelManageEdit",
    query: {
      spaceCode:route.query.spaceCode || '',
      mode: "add",
    },
  });
};

const EditEvent = (llm: LLM.Model) => {
  const { model_name, worker_type, healthy,} = llm;
  router.push({
    path: "modelManageEdit",
    query: {
      spaceCode:route.query.spaceCode || '',
      mode: "edit",
      id: model_name,
      worker_type,
      healthy: healthy.toString(),
    },
  });
};

const DeleteEvent = (llm: LLM.Model) => {
  ElMessageBox.confirm(`确定要删除数据库 "${llm.model_name}" 吗？`, "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      modelList.value = modelList.value.filter(
        (item) => item.model_name !== llm.model_name
      );
      let requestData: DeleteModelRequestData = {
        ...llm,
        model: llm.model_name,
        delete_after: true,
        params: {},
      };
      deleteLLMtApi(requestData).then((res) => {
        ElMessage.success("删除成功");
        getModeList()
      });
    })
    .catch(() => {
      // 用户取消删除
    });
};

function getTypeTag(type: string) {
  if (type === "llm") return "primary";
  if (type === "text2vec") return "success";
  if (type === "reranker") return "warning";
  return "info";
}

const getTypeDesc = (type: string) => {
  if (type === "llm") return "对话模型";
  if (type === "text2vec") return "向量模型";
  if (type === "reranker") return "重排模型";
  return "未知类型";
};

onMounted(() => {
  getModeList();
});
</script>

<style lang="scss" scoped>
.box-container {
  padding: 0 20px;
  background: #fff;;
}
.model {
  padding: 0 20px;
  height: 100%;
}

.model-container {
  padding: 24px;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.model-header-left {
  display: flex;
  align-items: center;
  gap:12px;
}

.model-card-list {
  width: 100%;
}

.model-card-col {
  margin-bottom: 24px;
  .el-dropdown-link {
    visibility: hidden;
  }
  &:hover {
    .el-dropdown-link {
      visibility: visible;
    }
  }
}

.model-card {
  border-radius: 12px;
  min-height: 150px;
  position: relative;
  overflow: visible;
  :deep(.el-card__body) {
    padding: 10px 20px;
  }
}

.model-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
  align-items: center;
}

.model-logo {
  width: 48px;
  height: 48px;
  object-fit: contain;
}

.model-card-menu {
  cursor: pointer;
  font-size: 20px;
  color: #909399;
}

.model-card-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.model-card-desc {
  color: #666;
  font-size: 14px;
  min-height: 38px;
}

.model-type-tag {
  position: absolute;
  top: 21px;
  right: 16px;
}

.model-type-card {
  border: 1px solid #eee;
  transition: border 0.2s;
}

.model-type-card.active {
  border: 1.5px solid #409eff;
  box-shadow: 0 0 6px #409eff22;
}
</style>
