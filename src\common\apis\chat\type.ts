export interface GetAgentListRequestData {
	page: number
	pageSize: number
  app_name?: string,
  workspace_id:number,
}

export interface TableRequestData {
	/** 当前页码 */
	currentPage: number;
	/** 查询条数 */
	size: number;
	/** 查询参数：用户名 */
	username?: string;
	/** 查询参数：手机号 */
	phone?: string;
}

// 参数需求对象类型
export interface ParamNeed {
	type: string;
	value?: string | null;
	bind_value?: string;
}

// 推荐问题对象类型
export interface RecommendQuestion {
	id: number;
	app_code: string;
	question: string;
	user_code: string;
	sys_code: string | null;
	gmt_create: string;
	gmt_modified: string;
	params: Record<string, any>;
	valid: string;
	chat_mode: string;
	is_hot_question: string;
}


// Agent 命名空间，用于组织相关类型
export namespace Agent {
  export interface AppList {
    admins: string[];
    app_code: string;
    app_type?: string;
    app_describe: string;
    app_name: string;
    created_at: string;
    details: string[];
    hot_value: number;
    icon: string | null;
    is_collected: string; // "true" | "false"
    keep_end_rounds: number;
    keep_start_rounds: number;
    language: string;
    owner_avatar_url: string | null;
    owner_name: string;
    param_need: ParamNeed[];
    published: string; // "true" | "false"
    recommend_questions: RecommendQuestion[];
    sys_code: string | null;
    team_context: {
      chat_scene: string;
      param_title: string;
      scene_describe: string;
      scene_name: string;
      show_disable: boolean;
    };
    team_mode: string;
    updated_at: string;
    user_code: string;
    user_icon: string | null;
    user_name: string | null;
  }
	export type TableData = AppList;

	export interface ListResponse {
		app_list: TableData[];
		current_page: number;
		total_count: number;
		total_page: number;
	}
}

// Dialogue 命名空间，用于组织相关类型
export namespace Dialogue {
  // 对话列表项接口
  export interface DialogueList {
    conv_uid: string;
    dialog_id:String;
    user_input: string;
    chat_mode: string;
    app_code: string;
    select_param: string;
    model_name: string;
    user_name: string;
    sys_code: string | null;
  }

	export interface DialogueListResponseData {
		data: DialogueList[];
		err_code: string | null;
		err_msg: string | null;
		success: boolean;
	}
}

// export type TableResponseData = ApiResponseData<{
//   list: TableData[]
//   total: number
// }>

// 聊天内容项接口
export interface UserContentItem {
  type: string
  [key: string]: any
}

// 用户消息对象接口
export interface UserMessageObject {
  role: "user"
  content: UserContentItem[]
}

// 用户聊天内容类型
export type UserChatContent = string | UserMessageObject

// 图表值类型
interface ChartValue {
  name: string
  type: string
  value: number
}

// 图表数据类型
export interface ChartData {
  chart_desc: string
  chart_name: string
  chart_sql: string
  chart_type: string
  chart_uid: string
  column_name: Array<string>
  values: Array<ChartValue>
  type?: string
}

// 聊天历史响应类型
export type ChatHistoryResponse = IChatDialogueMessageSchema[]

// 聊天对话模式类型
export type ChatMode =
  | "chat_with_db_execute"
  | "chat_excel"
  | "chat_with_db_qa"
  | "chat_knowledge"
  | "chat_dashboard"
  | "chat_execution"
  | "chat_agent"
  | "chat_flow"
  | (string & {})

// 聊天对话架构接口
export interface IChatDialogueSchema {
  conv_uid: string
  dialog_id?: string
  user_input: UserChatContent
  user_name: string
  chat_mode: ChatMode
  select_param: string
  app_code: string
  param_need?: any[]
}

// 聊天对话消息架构接口
export interface IChatDialogueMessageSchema {
  role: "human" | "view" | "system" | "ai"
  context: string
  order: number
  time_stamp: number | string | null
  model_name: string
  retry?: boolean
  thinking?: boolean
  outing?: boolean
  feedback?: Record<string, any>,
  tipAlias?: string
}

// 应用信息接口
export interface IApp {
  app_code?: string
  app_name?: string
  app_describe?: string
  param_need?: any[]
  [key: string]: any
}

// 对话列表响应类型
export type DialogueListResponse = IChatDialogueSchema[]

// 聊天参数接口
export interface ChatParams {
  chatId: string
  ctrl?: AbortController
  data?: any
  query?: Record<string, string>
  queryAgentURL?: string
  onMessage: (message: string) => void
  onClose?: () => void
  onDone?: () => void
  onError?: (content: string, error?: Error) => void
}

// 新建对话参数接口
export interface NewDialogueParam {
  chat_mode: string
  model?: string
}

// 反馈接口
export interface FeedBack {
  information?: string
  just_fun?: string
  others?: string
  work_study?: string
}

// 推荐问题参数接口
export interface RecommendQuestionParams {
  valid?: string
  app_code?: string
  chat_mode?: string
  is_hot_question?: string
}

// 推荐问题响应接口
export interface RecommendQuestionResponse {
  id: string
  app_code: string
  question: string
  chat_mode: string
  user_code: string
}

export interface StoreChatDataHistoryRequest {
  conv_uid: string
  user_input: string
  assistant_content: {
    "few-shot": any[],
    schema: any[],
    "generate-sql": string,
    "amendment-sql": string,
    "parse-intent": Record<string, any>,
    "sql-chat": Record<string, any>,
    "req_rec": string
    "sql_edit": Record<string, any>,
    "attribution_analysis": Record<string, any>,
    "data_explain": Record<string, any>
  } | string
  user_name?: string
  dialog_id: string
  app_code: string
  round_index?: string | number | unknown
}

// 删除历史对话请求类型
export interface DeleteHistoryRequest {
  dialog_id: string
  conv_uid: string
}
