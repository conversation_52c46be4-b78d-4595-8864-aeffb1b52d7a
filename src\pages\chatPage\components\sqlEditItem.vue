<template>
  <!-- 查询条件 -->
  <div v-if="data.type === 'search'" class="search-condition-item">
    <!-- 字段 -->
    <el-select v-model="data.left" filterable @change="handleFieldChange(data)">
      <el-option
        v-for="item in fieldList"
        :key="item.field"
        :label="item.alias || item.description"
        :value="item.field"
      ></el-option>
    </el-select>
    <!-- 操作符 -->
    <el-select
      v-model="data.operator"
      v-if="
        !noOperatorField.includes(data.leftType) &&
        !useNumberRange.includes(data.operator)
      "
      :disabled="!data.left"
      @change="handleOperatorChange(data)"
    >
      <el-option
        v-for="item in operatorList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>
    <!-- 字符串类型 -->
    <el-input
      v-if="typeof data.right === 'string' && !isUnshowRight"
      v-model="data.right"
      :disabled="!data.operator"
      placeholder="输入关键词，并用英文逗号隔开"
    ></el-input>
    <!-- 日期时间类型 -->
    <el-input
      v-else-if="data.leftType === 'Datetime'"
      v-model="data.right.showText"
      placeholder="请选择日期时间"
      @click="handleDateRangeClick"
    >
    </el-input>
    <!-- 为空 -->
    <span v-else-if="isUnshowRight"></span>
    <!-- 数值类型 -->
    <el-select
      v-else-if="showNumberRangeVis"
      v-model="data.right.showText"
      style="width: 100%"
      @blur="handleRangeRightBlur(data)"
    >
      <div class="number-range-config">
        <el-radio-group
          v-model="data.right.rangeType"
          @change="(val) => handleRangeTypeChange(val, data)"
        >
          <el-radio label="范围" value="range"></el-radio>
          <el-radio label="至少" value="min"></el-radio>
          <el-radio label="至多" value="max"></el-radio>
        </el-radio-group>
        <div class="number-range-config-item">
          <div class="range-min">
            <el-input
              v-model="data.right.min.value"
              :disabled="data.right.rangeType === 'max'"
              placeholder="请输入最小值"
              @blur="validateNumber('min')"
            ></el-input>
            <el-checkbox
              v-model="data.right.min.include"
              v-show="data.right.rangeType !== 'max'"
              >包含最小值</el-checkbox
            >
          </div>

          <span class="split"></span>
          <div class="range-max">
            <el-input
              v-model="data.right.max.value"
              :disabled="data.right.rangeType === 'min'"
              placeholder="请输入最大值"
              @blur="validateNumber('max')"
            ></el-input>
            <el-checkbox
              v-model="data.right.max.include"
              v-show="data.right.rangeType !== 'min'"
              >包含最大值</el-checkbox
            >
          </div>
        </div>
      </div>
      <el-option v-show="false" value=""></el-option>
    </el-select>
  </div>
  <!-- 列 -->
  <div v-if="data.type === 'column'" class="column-condition-item">
    <!-- 字段 -->
    <el-select v-model="data.left" filterable @change="handleFieldChange(data)">
      <template #label="{ label }">
        <!-- 选择了函数的情况下 -->
        <span v-if="!!data.operator">{{
          `${config?.columnFunctionMap[data.operator]?.label} (${label})`
        }}</span>
        <!-- 没有选择函数的情况下 -->
        <span v-else>{{ label }}</span>
      </template>
      <el-option
        v-for="item in fieldList"
        :key="item.field"
        :label="item.alias || item.description"
        :value="item.field"
      ></el-option>
    </el-select>
    <!-- 函数 -->
    <el-select
      v-model="data.operator"
      filterable
      v-if="data.leftType === 'Datetime'"
    >
      <el-option
        v-for="item in config.columnFunctionList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>
    <!-- 分组 / or 聚合 -->
    <el-select v-model="data.right.group" filterable>
      <el-option value="group" label="分组"></el-option>
      <el-option value="aggregate" label="聚合" disabled></el-option>
    </el-select>
  </div>
  <DateRangeDialog ref="dateRangeDialog" @confirm="handleDateRangeConfirm" />
</template>
<script setup lang="ts">
import { ref, defineModel, inject, computed, toRaw } from "vue";
import * as config from "./config";
import DateRangeDialog from "./dateRangePicker.vue";
import { get } from "lodash-es";

interface sqlConditionItem {
  type?: string; // 类型 search\column 等
  leftType?: string; // 字段类型
  left?: string; // 字段
  operator?: string; // 操作符
  right?: any; // 关键字值  为纯字符串 或 配置对象
  // -------------------- column
  alias?: string; // 字段别名
  expression?: string; // 表达式
  // rangeConfig?: Record<string, any>; // 区域、嵌套、时间类型的表达式
  // conditions?: Record<string, any>[]; // 区域、嵌套的表达式
}
// 每一sql 数据
const data = defineModel<sqlConditionItem>("data");
// 使用数字范围的类型
const useNumberRange = ref<string[]>([...config.useNumberRangeOperator]);
// 运算符map
const operatorList = ref([...config.operatorList]);
// 不显示操作符的字段类型
const noOperatorField = computed(() => {
  return ["Datetime", ...config.numberTypeMap];
});
// 字段列表
const fieldList: any = inject("fieldList");
// 获取fieldList Map
const fieldListMap = computed(() => {
  return fieldList.value.reduce(
    (acc: Record<string, any>, item: Record<string, any>) => {
      acc[item.field] = item;
      return acc;
    },
    {}
  );
});

// 判断关键字值是否为对象
const rightIsObject = computed(() => {
  return (
    Object.prototype.toString.call(get(data.value, "right")) ===
    "[object Object]"
  );
});
// 关键字显示数值区域范围的条件下
const showNumberRangeVis = computed(() => {
  // 操作符是数字范围类型 或 字段类型是数值类型
  return (
    (useNumberRange.value.includes(data.value.operator) ||
      config.numberTypeMap.includes(data.value.leftType)) &&
    !!rightIsObject.value
  );
});

// 不显示右边的条件
const isUnshowRight = computed(() => {
  return data.value.operator === "is" || data.value.operator === "is not";
});

// 日期范围弹窗
const dateRangeDialog = ref<InstanceType<typeof DateRangeDialog>>();

// -------------------------------------------------

// 日期范围确认
function handleDateRangeConfirm(result: any = {}) {
  data.value.operator = "scope";
  data.value.right = {
    ...result,
  };
  console.log(data.value, "update date");
}

// 范围输入框失去焦点时，根据配置生成right 显示字符串
function handleRangeRightBlur(item: Record<string, any>) {
  const { right = {} } = item;
  let str = "";
  if (!!right.min.value && !!right.max.value) {
    str = `${right.min.include ? "大于等于" : "大于"}${right.min.value} 且 ${
      right.max.include ? "小于等于" : "小于"
    }${right.max.value}`;
  } else if (!!right.min.value && !right.max.value) {
    // 只有min 为最小值
    str = `${right.min.include ? "大于等于" : "大于"}${right.min.value}`;
  } else if (!right.min.value && !!right.max.value) {
    // 只有max 为最大值
    str = `${right.max.include ? "小于等于" : "小于"}${right.max.value}`;
  }
  item.right.showText = str;
}

// 操作符编辑
function handleOperatorChange(item: Record<string, any>) {
  // delete item.rangeConfig;
  item.right = "";
  // 如果操作符是数字范围类型
  if (
    useNumberRange.value.includes(item.operator) ||
    config.numberTypeMap.includes(item.leftType)
  ) {
    item.right = {
      rangeType: "range",
      showText: "",
      min: {
        value: "",
        include: true,
      },
      max: {
        value: "",
        include: true,
      },
    };
  } else if (item.operator === "like") {
    item.right = "%%";
  }
}

// 范围类型改变
function handleRangeTypeChange(
  val: string | number | boolean,
  item: Record<string, any>
) {
  // 根据所选类型 重置最大最小输入值
  if (val === "min") {
    item.right.max.value = "";
  } else if (val === "max") {
    item.right.min.value = "";
  } else {
    item.right.min.value = "";
    item.right.max.value = "";
  }
}

// 验证是否为纯数字的函数
function validateNumber(type: "min" | "max") {
  if (!data.value?.right) return;

  const value = data.value.right[type].value;
  // 使用正则表达式验证是否为数字（可以包含小数点）
  const isValid = /^\d+(\.\d+)?$/.test(value);

  if (!isValid && value !== "") {
    // 如果不是纯数字且不为空，则清空
    data.value.right[type].value = "";
    // ElMessage.warning(`${type === 'min' ? '最小值' : '最大值'}必须是纯数字`);
  }
}

// 字段改变
function handleFieldChange(item: Record<string, any>) {
  // 根据选择字段、获取匹配的字段完整信息
  const field = item.left;
  const sameFieldInfo = fieldListMap.value[field];
  // 设置字段类型
  item.leftType = sameFieldInfo?.data_type;
  // 设置操作符
  item.operator = "";
  // 查询条件
  if (data.value.type === "search") {
    // 字符串值
    if (item.leftType === "String") {
      item.right = "";
    } else if (config.numberTypeMap.includes(item.leftType)) {
      // 数值类型
      item.right = {
        rangeType: "range",
        showText: "",
        min: {
          value: "",
          include: true,
        },
        max: {
          value: "",
          include: true,
        },
      };
    } else if (item.leftType === "Datetime") {
      item.right = {
        showText: "",
        rangeType: "quick",
      };
    }
  } else if (data.value.type === "column") {
    // 日期类型需要拼接expression、alias，字符串类型直接使用本身label id 来设置字段
    if (item.leftType === "Datetime") {
    } else {
      // 列切换
      item.alias = sameFieldInfo.alias || sameFieldInfo.description;
      item.expression = item.left;
    }
    item.operator = "";
    item.right = {
      group: "group",
    };
  }
}

// 日期范围点击
function handleDateRangeClick() {
  const { right = {} } = toRaw(data.value);
  // console.log(toRaw(data.value), "datetime open" )
  dateRangeDialog.value?.open({
    ...(right
      ? right
      : {
          rangeType: "quick",
        }),
  });
}
</script>
<style lang="scss" scoped>
.search-condition-item {
  display: grid;
  grid-template-columns: repeat(2, 110px) auto;
  gap: 5px;
  flex: 1;
  & > *:nth-child(3n + 2):last-child {
    grid-column: span 2;
  }
}
.column-condition-item {
  display: grid;
  grid-template-columns: repeat(2, 200px) auto;
  gap: 5px;
  flex: 1;
}
.number-range-config {
  padding: 20px;
  .number-range-config-item {
    margin-top: 10px;
    display: grid;
    grid-template-columns: 1fr 10px 1fr;
    align-items: stretch;
    gap: 10px;
    .split {
      border-top: 1px solid #c0c0ca;
      margin-top: 15px;
    }
  }
}
</style>