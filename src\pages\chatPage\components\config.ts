import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'

// 注册插件
dayjs.extend(quarterOfYear)
// 过滤条件操作符list
export const operatorList: Record<string, any>[] = [
    {
        label: "包含",
        value: "contain",
    },
    {
        label: "不包含",
        value: "not contain",
    },
    {
        label: "开头是",
        value: "startswith",
    },
    {
        label: "结尾是",
        value: "endswith",
    },
    {
        label: "等于",
        value: "eq",
    },
    {
        label: "不等于",
        value: "neq",
    },
    {
        label: "为空",
        value: "is",
    },
    {
        label: "不为空",
        value: "is not",
    },
    {
        label: "Like",
        value: "like",
    },
    {
        label: "计数",
        value: "count",
    },
    {
        label: "去重计数",
        value: "sum",
    },
];
// 操作符map
export const operatorListMap = operatorList.reduce((acc: Record<string, any>, item: Record<string, any>) => {
    acc[item.value] = item;
    return acc;
}, {});

// 计算符号 对应的中文名称，展示sql时使用
export const computedSymbolFormat = (operator: string) => {
    let map = {
        "eq": "等于",
        "neq": "不等于",
        "gte": "大于等于",
        "gt": "大于",
        "lte": "小于等于",
        "lt": "小于",
        "like": "Like",
        "in": "包含",
        "not in": "不包含",
    };
    return map[operator] || operator;
}

// 使用数值区域配置 操作的操作符
export const useNumberRangeOperator: string[] = ["count", "sum", "scope"];

// 数值字段类型list
export const numberTypeMap = ["Decimal", "Tinyint", "Smallint", "Mediumint", "Int", "Bigint", "Float", "Double", "Integer"];

// 快捷时间选项配置
export const timeOptions: any = {
    all: {
        label: '全部',
        children: [
            { label: '全部时间', value: 'all' }
        ]
    },
    day: {
        label: '按天',
        children: [
            // 第一行
            { label: '今天', value: 'today' },
            { label: '前7天', value: 'last7days', includeToday: true },
            { label: '未来7天', value: 'next7days', includeToday: true },
            // 第二行
            { label: '明天', value: 'tomorrow' },
            { label: '前14天', value: 'last14days', includeToday: true },
            { label: '未来14天', value: 'next14days', includeToday: true },
            // 第三行
            { label: '昨天', value: 'yesterday' },
            { label: '前28天', value: 'last28days', includeToday: true },
            { label: '上周同期', value: 'lastWeekSame' },
            // 第四行
            { label: '前天', value: 'beforeYesterday' },
            { label: '前30天', value: 'last30days', includeToday: true },
            { label: '上月同期', value: 'lastMonthSame' }
        ]
    },
    week: {
        label: '按周',
        children: [
            { label: '本周', value: 'thisWeek' },
            { label: '上周', value: 'lastWeek' },
            { label: '下周', value: 'nextWeek' }
        ]
    },
    month: {
        label: '按月',
        children: [
            { label: '本月', value: 'thisMonth' },
            { label: '上月', value: 'lastMonth' },
            { label: '下月', value: 'nextMonth' }
        ]
    },
    quarter: {
        label: '按季度',
        children: [
            { label: '本季度', value: 'thisQuarter' },
            { label: '上季度', value: 'lastQuarter' },
            { label: '下季度', value: 'nextQuarter' }
        ]
    },
    year: {
        label: '按年',
        children: [
            { label: '本年', value: 'thisYear' },
            { label: '上半年', value: 'firstHalfYear' },
            { label: '下半年', value: 'secondHalfYear' },
            { label: '去年', value: 'lastYear' },
            { label: '明年', value: 'nextYear' }
        ]
    }
}


// 定义参数接口
interface TimeRangeOptions {
    includeToday?: boolean;
}

// 修改时间计算函数
export const getTimeRange = (type: string, options: TimeRangeOptions = {}): [Date, Date] | null => {
    const now = dayjs()
    let start: dayjs.Dayjs
    let end: dayjs.Dayjs

    switch (type) {
        case 'all':
            return null
        // 按天
        case 'today':
            start = now.startOf('day')
            end = now.endOf('day')
            break
        case 'yesterday':
            start = now.subtract(1, 'day').startOf('day')
            end = now.subtract(1, 'day').endOf('day')
            break
        case 'tomorrow':
            start = now.add(1, 'day').startOf('day')
            end = now.add(1, 'day').endOf('day')
            break
        case 'beforeYesterday':
            start = now.subtract(2, 'day').startOf('day')
            end = now.subtract(2, 'day').endOf('day')
            break
        case 'last7days':
            start = now.subtract(7, 'day').startOf('day')
            end = options.includeToday ? now.endOf('day') : now.subtract(1, 'day').endOf('day')
            break
        case 'last14days':
            start = now.subtract(14, 'day').startOf('day')
            end = options.includeToday ? now.endOf('day') : now.subtract(1, 'day').endOf('day')
            break
        case 'last28days':
            start = now.subtract(28, 'day').startOf('day')
            end = options.includeToday ? now.endOf('day') : now.subtract(1, 'day').endOf('day')
            break
        case 'last30days':
            start = now.subtract(30, 'day').startOf('day')
            end = options.includeToday ? now.endOf('day') : now.subtract(1, 'day').endOf('day')
            break
        case 'next7days':
            start = options.includeToday ? now.startOf('day') : now.add(1, 'day').startOf('day')
            end = now.add(7, 'day').endOf('day')
            break
        case 'next14days':
            start = options.includeToday ? now.startOf('day') : now.add(1, 'day').startOf('day')
            end = now.add(14, 'day').endOf('day')
            break
        case 'lastWeekSame':
            start = now.subtract(7, 'day').startOf('day')
            end = now.subtract(7, 'day').endOf('day')
            break
        case 'lastMonthSame':
            start = now.subtract(1, 'month').startOf('day')
            end = now.subtract(1, 'month').endOf('day')
            break
        // 按周
        case 'thisWeek':
            start = now.startOf('week')
            end = now.endOf('week')
            break
        case 'lastWeek':
            start = now.subtract(1, 'week').startOf('week')
            end = now.subtract(1, 'week').endOf('week')
            break
        case 'nextWeek':
            start = now.add(1, 'week').startOf('week')
            end = now.add(1, 'week').endOf('week')
            break
        // 按月
        case 'thisMonth':
            start = now.startOf('month')
            end = now.endOf('month')
            break
        case 'lastMonth':
            start = now.subtract(1, 'month').startOf('month')
            end = now.subtract(1, 'month').endOf('month')
            break
        case 'nextMonth':
            start = now.add(1, 'month').startOf('month')
            end = now.add(1, 'month').endOf('month')
            break
        // 按季度
        case 'thisQuarter':
            start = now.startOf('quarter')
            end = now.endOf('quarter')
            break
        case 'lastQuarter':
            start = now.subtract(1, 'quarter').startOf('quarter')
            end = now.subtract(1, 'quarter').endOf('quarter')
            break
        case 'nextQuarter':
            start = now.add(1, 'quarter').startOf('quarter')
            end = now.add(1, 'quarter').endOf('quarter')
            break
        // 按年
        case 'thisYear':
            start = now.startOf('year')
            end = now.endOf('year')
            break
        case 'lastYear':
            start = now.subtract(1, 'year').startOf('year')
            end = now.subtract(1, 'year').endOf('year')
            break
        case 'nextYear':
            start = now.add(1, 'year').startOf('year')
            end = now.add(1, 'year').endOf('year')
            break
        case 'firstHalfYear':
            start = now.startOf('year')
            end = now.startOf('year').add(5, 'month').endOf('month')
            break
        case 'secondHalfYear':
            start = now.startOf('year').add(6, 'month').startOf('month')
            end = now.endOf('year')
            break
        default:
            return null
    }

    return [start.toDate(), end.toDate()]
}

// 列配置-函数list
export const columnFunctionList = [
    {value:'accurate', fn: "None", label: '精确时间'},
    {value:'year', fn: `DATE_FORMAT(date,"%Y")`, label: '年'},
    // {value:'quarter', fn: `QUARTER(date)`, label: '季度'},
    {value:'ym', fn: `DATE_FORMAT(date,"%Y-%m")`, label: '年-月'},
    // {value:'year_week_', fn: "", label: '年 - 周（跨年）'},
    {value:'yw', fn: `DATE_FORMAT(date,"%Y-%U")`, label: '年-周（不跨年）'},
    {value:'ymd', fn: `DATE_FORMAT(date,"%Y-%m-%d")`, label: '年-月-日'},
    {value:'ymdh', fn: `DATE_FORMAT(date,"%Y-%m-%d %H")`, label: '年-月-日-小时'},
    {value:'ymdhm', fn: `DATE_FORMAT(date,"%Y-%m-%d %H:%i")`, label: '年-月-日-小时-分'},
    {value: 'ymdhms', fn: `DATE_FORMAT(date,"%Y-%m-%d %H:%i:%S")`, label: '年-月-日-小时-分-秒'},
    {value:'year', fn: `YEAR(date)`, label: '年'},
    {value:'quarter', fn: "QUARTER(date)", label: '季度'},
    {value:'month', fn: "MONTH(date)", label: '月'},
    {value:'week', fn: "WEEK(date)", label: '周'},
    {value:'dayofweek', fn: "DAYOFWEEK(date)", label: '日期（星期）'},
    {value:'day', fn: "DAYOFMONTH(date)", label: '日'},
    {value:'hour', fn: "HOUR(date)", label: '小时'},
    {value:'minute', fn: "MINUTE(date)", label: '分钟'},
    {value:'second', fn: "SECOND(date)", label: '秒'},
]

export const columnFunctionMap = columnFunctionList.reduce((acc: Record<string, any>, item: Record<string, any>) => {
    acc[item.value] = item;
    return acc;
}, {});
