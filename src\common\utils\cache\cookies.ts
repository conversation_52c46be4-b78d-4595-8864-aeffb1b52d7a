// 统一处理 Cookie

import { <PERSON><PERSON><PERSON><PERSON> } from "@@/constants/cache-key"
import Cookies from "js-cookie"

export function getToken() {
  return Cookies.get(CacheKey.TOKEN)
}

export function setToken(token: string) {
  Cookies.set(CacheKey.TOKEN, token)
}

export function removeToken() {
  Cookies.remove(CacheKey.TOKEN)
}

export function getRefreshToken() {
  return Cookies.get(CacheKey.REFRESH_TOKEN)
}

export function setRefreshToken(token: string) {
  Cookies.set(CacheKey.REFRESH_TOKEN, token)
}

export function removeRefreshToken() {
  Cookies.remove(CacheKey.REFRESH_TOKEN)
}

export function setUser(user: object) {
  console.log("存储用户信息了：", user)
  Cookies.set(CacheKey.USER, JSON.stringify(user))
}
export function getUser(): object | undefined {
  const userStr = Cookies.get(CacheKey.USER)
  if (userStr) {
    try {
      return JSON.parse(userStr)
    } catch {
      return undefined
    }
  }
  return undefined
}
export function removeUser() {
  Cookies.remove(CacheKey.USER)
}
