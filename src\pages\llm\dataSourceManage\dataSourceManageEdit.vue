<template>
  <div class="database-form-container">
    <div class="database-form-header">
      <div class="header-left">
        <span class="back-btn" @click="handleBack">
          <i class="ChatData-fanhui iconfont"></i>
        </span>
        <h2>{{ isEdit ? '编辑数据源' : '新增数据源' }}</h2>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="submitForm" size="small" :loading="submitLoading">确定</el-button>
        <el-button @click="handleCancel" size="small">取消</el-button>
        <el-button @click="handleTestConnection" size="small" :loading="testConnectionLoading">测试连接</el-button>
      </div>
    </div>

    <div class="database-form-content">
      <el-form
        ref="formRef"
        :model="form"
        :rules="dynamicRules"
        label-width="120px"
        class="database-form"
        label-position="top"
      >
        <!-- 数据源类型选择 -->
        <el-form-item label="数据源类型" prop="type" required style="width:calc(34% - 24px);">
          <div class="form-item-with-icon">
            <el-select
              v-model="form.type"
              placeholder="请选择数据源类型"
              class="full-width"
              @change="handleTypeChange"
            >
              <el-option
                v-for="item in databaseTypes"
                :key="item.name"
                :label="item.name"
                :value="item.name"
              >
                <div class="select-option">
                  <div class="option-label">{{ item.name }}</div>
                  <div class="option-description">{{ item.description }}</div>
                </div>
              </el-option>
            </el-select>
          </div>
        </el-form-item>

        <!-- 数据库名称 -->
        <!-- <el-form-item label="数据库名称" prop="name" required>
          <div class="form-item-with-icon">
            <el-input v-model="form.name" placeholder="请输入数据库名称" />
            <el-tooltip content="数据库连接的名称标识" placement="top">
              <el-icon class="info-icon"><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-form-item> -->

        <!-- 动态渲染的表单字段 -->
        <template v-if="currentDatabaseType">
          <div
            v-for="(parameter, index) in sortedParameters"
            :key="parameter.param_name"
            :class="getFieldClass(parameter, index)"
          >
            <!-- 字符串类型字段 -->
            <el-form-item
              v-if="parameter.param_type === 'string'"
              :label="parameter.label"
              :prop="parameter.param_name"
              :required="parameter.required"
              class="form-item-with-tooltip"
            >
            <!-- //自定义label -->
            <template #label>
              <span class="custom-label">
                {{ parameter.label }}
                <el-tooltip :content="parameter.description" placement="top">
                  <i class="iconfont ChatData-zhushi tip-icon"></i>
                </el-tooltip>
              </span>
            </template>
              <div class="form-item-with-icon">
                <!-- 选择框（有valid_values的字段） -->
                <el-select
                  v-if="parameter.valid_values && parameter.valid_values.length > 0"
                  v-model="form[parameter.param_name]"
                  :placeholder="`请选择${parameter.label}`"
                  class="full-width"
                >
                  <el-option
                    v-for="value in parameter.valid_values"
                    :key="value"
                    :label="value"
                    :value="value"
                  />
                </el-select>
                <!-- 密码输入框 -->
                <el-input
                  v-else-if="parameter.ext_metadata?.tags === 'privacy'"
                  v-model="form[parameter.param_name]"
                  type="password"
                  :placeholder="parameter.default_value || `请输入${parameter.label}`"
                  show-password
                />
                <!-- 普通文本输入框 -->
                <el-input
                  v-else
                  v-model="form[parameter.param_name]"
                  :placeholder="parameter.default_value || `请输入${parameter.label}`"
                />
              </div>
            </el-form-item>

            <!-- 整数类型字段 -->
            <el-form-item
              v-else-if="parameter.param_type === 'integer'"
              :label="parameter.label"
              :prop="parameter.param_name"
              :required="parameter.required"
              class="form-item-with-tooltip"
            >
            <template #label>
              <span class="custom-label">
                {{ parameter.label }}
                <el-tooltip :content="parameter.description" placement="top">
                  <i class="iconfont ChatData-zhushi tip-icon"></i>
                </el-tooltip>
              </span>
            </template>
              <div class="form-item-with-icon">
                <el-input-number
                  v-model="form[parameter.param_name]"
                  :placeholder="`请输入${parameter.label}`"
                  :min="0"
                  class="full-width"
                />
              </div>
            </el-form-item>

            <!-- 布尔类型字段 -->
            <el-form-item
              v-else-if="parameter.param_type === 'boolean'"
              :label="parameter.label"
              :prop="parameter.param_name"
              class="form-item-with-tooltip checkbox-item"
            >
              <template #label>
              <span class="custom-label">
                {{ parameter.label }}
                <el-tooltip :content="parameter.description" placement="top">
                  <i class="iconfont ChatData-zhushi tip-icon"></i>
                </el-tooltip>
              </span>
            </template>
              <div class="checkbox-wrapper">
                <el-checkbox v-model="form[parameter.param_name]" />
              </div>
            </el-form-item>
          </div>
        </template>

        <!-- 描述字段 -->
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="定义描述，以便快速理解"
          />
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {
  ArrowLeft,
  InfoFilled
} from '@element-plus/icons-vue'
import { getDatasourceByIdApi,postDatasourceApi,putDatasourceApi,getDatasourcesTypeApi,testDatasourceConnectionApi } from "@/common/apis/llm"


// 路由相关
const route = useRoute()
const router = useRouter()
const isEdit = ref(false)
const databaseId = ref<number | null>(null)

// 表单引用
const formRef = ref<FormInstance>()

// 提交loading状态
const submitLoading = ref(false)
// 测试连接loading状态
const testConnectionLoading = ref(false)

// 数据库类型配置
const databaseTypes = ref<any[]>([])

// 表单数据
const form = reactive<any>({
  id: 0,
  name: '',
  type: 'mysql',
  description: ''
})

// 当前选中的数据库类型配置
const currentDatabaseType = computed(() => {
  return databaseTypes.value.find(db => db.name === form.type)
})

// 按顺序排列的参数
const sortedParameters = computed(() => {
  if (!currentDatabaseType.value) return []
  return [...currentDatabaseType.value.parameters].sort((a, b) => a.param_order - b.param_order)
})

// 动态生成的表单验证规则
const dynamicRules = computed<FormRules>(() => {
  const rules: FormRules = {
    type: [
      { required: true, message: '请选择数据库类型', trigger: 'change' }
    ],
    name: [
      { required: true, message: '请输入数据库名称', trigger: 'blur' }
    ]
  }

  // if (currentDatabaseType.value) {
  //   currentDatabaseType.value.parameters.forEach((param: any) => {
  //     if (param.required) {
  //       const message = `请${param.param_type === 'boolean' ? '选择' : '输入'}${param.label}`
  //       rules[param.param_name] = [
  //         { required: true, message, trigger: param.param_type === 'boolean' ? 'change' : 'change' }
  //       ]
  //     }
  //   })
  // }

  return rules
})

// 生命周期钩子
onMounted(async () => {
  await fetchDatabaseTypes()

  // 从查询参数判断是新增还是编辑
  const id = route.query.id
  const mode = route.query.mode

  if (mode === 'edit' && id) {
    isEdit.value = true
    databaseId.value = parseInt(id as string)
    await fetchDatabaseData()
  } else {
    getInitDefaultValues()
  }
})
function getInitDefaultValues(){
   // 根据新类型设置默认值
   currentDatabaseType.value.parameters.forEach((param: any) => {
    if (param.default_value !== null && param.default_value !== undefined) {
      form[param.param_name] = param.default_value
    } else {
      // 根据类型设置初始值
      if (param.param_type === 'boolean') {
        form[param.param_name] = false
      } else if (param.param_type === 'integer') {
        form[param.param_name] = 10
      } else {
        form[param.param_name] = ''
      }
    }
  })
  // 取消表单校验
  formRef.value?.clearValidate()
}
const fetchDatabaseTypes = async () => {
  try {
    const response = await getDatasourcesTypeApi() as { data?: { types: any[] } }
    databaseTypes.value = response.data?.types || []
  } catch (error) {
    ElMessage.error('获取数据库类型配置失败')
    console.error(error)
  }
}

// 获取数据库详情
const fetchDatabaseData = async () => {
  if (!databaseId.value) return

  try {
    const response = await getDatasourceByIdApi(databaseId.value) as any

    if (response.success && response.data) {
      const { data } = response

      // 设置基本字段
      form.id = data.id
      form.type = data.type
      form.description = data.description || ''

      // 设置动态参数字段
      if (data.params) {
        Object.keys(data.params).forEach(key => {
          form[key] = data.params[key]
        })
      }
    }
  } catch (error) {
    ElMessage.error('获取数据库信息失败')
    console.error(error)
  }
}

// 处理数据库类型变化
const handleTypeChange = () => {
  if (!currentDatabaseType.value) return
  // 清除之前的动态字段
  Object.keys(form).forEach(key => {
    if (!['id', 'name', 'type', 'description'].includes(key)) {
      delete form[key]
    }
  })
  getInitDefaultValues()

}

// 获取字段的CSS类
const getFieldClass = (parameter: any, index: number) => {
  const classes = ['dynamic-field']

  // 根据参数顺序和类型决定是否在同一行
  const nextParam = sortedParameters.value[index + 1]
  const isInRow = shouldBeInRow(parameter, nextParam)

  if (isInRow) {
    classes.push('form-row-item')
  } else {
    classes.push('form-full-width')
  }

  return classes.join(' ')
}

// 判断字段是否应该在同一行
const shouldBeInRow = (current: any, next: any) => {
  if (!next) return false

  // 简单的逻辑：相邻的字符串或整数类型字段可以在同一行
  const currentIsSimple = ['string', 'integer'].includes(current.param_type) && !current.valid_values
  const nextIsSimple = ['string', 'integer'].includes(next.param_type) && !next.valid_values

  return currentIsSimple && nextIsSimple
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      submitLoading.value = true
      try {
        // 过滤掉空值
        const submitData = Object.keys(form).reduce((acc: any, key) => {
          if (form[key] !== '' && form[key] !== null && form[key] !== undefined) {
            acc[key] = form[key]
          }
          return acc
        }, {})

        if (isEdit.value && databaseId.value) {
          // 更新数据库
          await putDatasourceApi(databaseId.value, {params: submitData,type:submitData.type,description:form.description})
          ElMessage.success('数据库更新成功')
        } else {
          // 添加数据库
          await postDatasourceApi( {params: submitData,type:submitData.type})
          ElMessage.success('数据库添加成功')
        }
        // 返回列表页
        handleCancel()
      } catch (error) {
        ElMessage.error(isEdit.value ? '更新数据库失败' : '添加数据库失败')
        console.error(error)
      } finally {
        submitLoading.value = false
      }
    } else {
      console.log('表单验证失败', fields)
    }
  })
}

const handleBack = () => {
  handleCancel()
}

const handleCancel = () => {
  router.push({ path: 'dataSourceManage', query: {
    spaceCode:route.query.spaceCode || '',
  }})
}

const handleTestConnection = async () => {
  console.log('测试连接')
  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      testConnectionLoading.value = true
      try {
        const response = await testDatasourceConnectionApi(form) as { success: boolean, err_msg:string }
        if(response.success){
          ElMessage.success('测试连接成功')
        }else{
          ElMessage.error(response.err_msg || '测试连接失败')
        }
      } finally {
        testConnectionLoading.value = false
      }
    } else {
      console.log('表单验证失败', fields)
    }
  })
}

</script>

<style scoped lang="scss">
.database-form-container {
  background-color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.database-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  line-height: 48px;
  padding: 0 20px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  .back-btn {
    margin-right: 10px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f1f7ff;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    color: #005EE0;
    &:hover {
      background-color: #005EE0;
      color: #fff;
    }
  }
}

.header-left h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.header-right {
  display: flex;
  gap: 2px;
}

.database-form-content {
  padding: 24px;
  flex: 1;
}

.database-form {
  max-width: 1200px;
  margin: 0 auto;
}

.select-option {
  padding: 4px 0;
}

.option-label {
  font-weight: 500;
  color: #303133;
}

.option-description {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
  line-height: 1.2;
}

.dynamic-field {
  margin-bottom: 18px;
}

.form-row-item {
  display: inline-block;
  width: calc(34% - 8px);
  padding-right: 15px;
  vertical-align: top;
}

.form-row-item:nth-child(even) {
  margin-right: 0;
}

.form-full-width {
  width: 100%;
}

.form-item-with-icon {
  position: relative;
  width: 100%;
}


.tip-icon {
    font-size: 13px;
    color: #005ee0;
    cursor: pointer;
    margin-left: 10px;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
}

.full-width {
  width: 100%;
}

.el-input-number {
  width: 100%;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .form-row-item {
    width: 100%;
    margin-right: 0;
    display: block;
  }
}
</style>
