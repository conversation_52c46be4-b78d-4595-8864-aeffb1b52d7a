import type { ChatHistoryResponse, DialogueListResponse, IApp } from "@@/apis/chat/type"

// 模拟API延迟
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// 模拟获取对话列表
export async function getDialogueList(): Promise<DialogueListResponse> {
  await delay(500)
  return [
    {
      conv_uid: "default-conversation",
      user_input: "Hello",
      user_name: "用户",
      chat_mode: "chat_knowledge",
      select_param: "",
      app_code: "default-app"
    }
  ]
}

// 模拟获取聊天历史
export async function getChatHistory(chatId: string): Promise<ChatHistoryResponse> {
  await delay(300)
  if (!chatId || chatId === "default") {
    return []
  }
  
  return [
    {
      role: "human",
      context: "你好，请介绍一下自己",
      order: 1,
      time_stamp: Date.now() - 60000,
      model_name: "gpt-3.5-turbo"
    },
    {
      role: "view",
      context: "你好！我是AI助手，很高兴为您服务。我可以帮助您回答问题、提供信息、协助解决问题等。有什么我可以帮助您的吗？",
      order: 2,
      time_stamp: Date.now() - 50000,
      model_name: "gpt-3.5-turbo"
    }
  ]
}

// 模拟获取应用信息
export async function getAppInfo(params: any): Promise<IApp> {
  await delay(200)
  return {
    app_code: "default-app",
    app_name: "AI聊天助手",
    app_describe: "智能对话助手，可以回答各种问题",
    param_need: [
      {
        type: "temperature",
        value: 0.7
      },
      {
        type: "max_new_tokens",
        value: 2048
      }
    ]
  }
}

// 模拟聊天流式响应
export async function* streamChatResponse(message: string): AsyncGenerator<string, void, unknown> {
  const responses = [
    "我理解您的问题。",
    "让我为您详细解答。",
    "根据我的知识，",
    "这个问题涉及到多个方面。",
    "首先，我们需要考虑...",
    "其次，还要注意...",
    "总的来说，",
    "希望这个回答对您有帮助！"
  ]

  for (const response of responses) {
    await delay(200 + Math.random() * 300)
    yield response
  }
}

// API拦截器（简化版）
export async function apiInterceptors<T>(promise: Promise<T>): Promise<[boolean, T]> {
  try {
    const result = await promise
    return [true, result]
  } catch (error) {
    console.error("API Error:", error)
    throw error
  }
} 