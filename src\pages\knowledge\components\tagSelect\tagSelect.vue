<script setup lang="ts">
import { cloneDeep } from "lodash-es"
import { getTagList } from "@/common/apis/knowledge"
import tagManage from "./tagManage.vue"

const props = withDefaults(
  defineProps<{
    labelKey?: string
    valueKey?: string
    options?: Array<any>
    width?: string
    simpleShow?: boolean
  }>(),
  {
    labelKey: "label_name",
    valueKey: "id",
    width: "100%"
  }
)
const emit = defineEmits<{
  (e: "update"): void
}>()
const value = defineModel<string[]>()
const showOptions = ref<any[]>([])
const tagManageIsVisible = ref<boolean>(false)

// --------------------------------------

function initOptions() {
  getTagList({
    page: 1,
    page_size: 99999
  }).then((res: any) => {
    showOptions.value = res?.data?.data || []
  })
}

function update() {
  if (props.options) {
    emit("update")
    // showOptions.value = cloneDeep(props.options)
  } else {
    initOptions()
  }
}

watch(
  () => props.options,
  (newVal) => {
    // 有值 传入自定义的值
    if (newVal) {
      showOptions.value = cloneDeep(newVal)
    } else {
      // 没有值 请求
      initOptions()
    }
  },
  {
    immediate: true,
    deep: true
  }
)
</script>

<template>
  <el-select
    clearable
    multiple
    filterable

    placeholder="支持多选"
    class="tag-select" :class="[simpleShow && 'tag-select-simple']"
    v-bind="$attrs"
    v-model="value"
  >
    <el-option
      v-for="item in showOptions"
      :key="item[valueKey]"
      :label="item[labelKey]"
      :value="item[valueKey]"
    />
    <template #footer>
      <div class="tag-bottom" @click="tagManageIsVisible = true">
        <i class="iconfont bottom-icon ChatData-biaoqian" />
        管理标签
      </div>
    </template>
  </el-select>
  <tag-manage v-model:visible="tagManageIsVisible" @update="update" />
</template>

<style lang="scss" scoped>
.tag-bottom {
  color: #005ee0;
  padding: 0px 10px;
  cursor: pointer;

  .bottom-icon {
    font-size: 14px;
  }
}
.tag-select {
  :deep(.el-select-dropdown__footer) {
    background: #fcfcfc;
  }
}
.tag-select-simple {
  :deep(.el-select__wrapper) {
    box-shadow: none;
    &:hover {
      background: #f7f7f7;
      cursor: pointer;
    }
    .el-select__selection .el-tag {
      background: transparent;
      .el-tag__close {
        display: none;
      }
    }
  }
}
</style>
