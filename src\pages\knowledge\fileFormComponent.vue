<template>
  <el-form ref="formRef" :model="formData" :rules="rules" label-position="top" class="file-form" :disabled="!basicData.name">
    <!-- 类型部分 -->
    <div class="form-section">
      <h3 class="section-title">
        <span class="icon"><i class="iconfont ChatData-liebiaoshitu"></i></span>
        <span class="text">类型</span>
      </h3>
      <el-form-item class="type-formItem">
        <el-radio-group v-model="formData.doc_type" >
          <div class="content-type-options">
            <el-radio value="TEXT" class="content-type-option">
              <div class="option-content">
                <div class="option-icon text">
                  <i class="iconfont ChatData-wenben"></i>
                </div>
                <div class="option-text">
                  <div class="option-title">文本</div>
                  <div class="option-desc">填写您的原始文本</div>
                </div>
              </div>
            </el-radio>

            <el-radio value="DOCUMENT" class="content-type-option">
              <div class="option-content">
                <div class="option-icon document">
                  <i class="iconfont ChatData-wendang"></i>
                </div>
                <div class="option-text">
                  <div class="option-title">文档</div>
                  <div class="option-desc">上传文档,文档类型支持PDF、CSV、Text、Word、Markdown、Zip</div>
                </div>
              </div>
            </el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
    </div>

    <!-- 上传部分 -->
    <div class="form-section">
      <h3 class="section-title">
        <span class="icon"><i class="iconfont ChatData-shangchuan"></i></span>
        <span class="text">上传</span>
      </h3>
      <!-- 文本上传 -->
      <template v-if="showTextFields">
        <el-form-item label="文本名称" prop="doc_name">
          <el-input v-model="formData.doc_name" placeholder="请输入名称" clearable />
        </el-form-item>

        <el-form-item label="文本来源">
          <el-input v-model="formData.source" placeholder="请输入文本来源" clearable />
        </el-form-item>

        <el-form-item label="文本" prop="content">
          <el-input v-model="formData.content" type="textarea" :rows="6" placeholder="定义文本描述" />
        </el-form-item>
      </template>
             <!-- 文档上传 -->
       <template v-else>
         <el-form-item label="选择文件" prop="files" required class="file-form">
           <div class="upload-container">
             <!-- 拖拽上传区域 -->
             <div class="upload-area" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
               <el-upload
                 ref="uploadRef"
                 :auto-upload="false"
                 :on-change="handleFileChange"
                 :on-remove="handleFileRemove"
                 :file-list="fileList"
                 :limit="10"
                 multiple
                 drag
                 class="upload-dragger"
               >
                 <el-icon class="upload-icon"><Upload /></el-icon>
                 <div class="upload-text">将文件拖拉到此处或点击上传</div>
               </el-upload>
             </div>

             <!-- 支持格式说明 -->
             <div class="format-tip">
               文档格式仅支持PDF、CSV、Text、Word、Markdown、Zip
             </div>
             <!-- 文件列表 -->
             <div class="file-list" v-if="fileList.length > 0">
               <div
                 v-for="(file, index) in fileList"
                 :key="index"
                 class="file-item"
                 :class="{ 'uploading': file.status === 'uploading', 'failed': file.status === 'fail' }"
               >
                 <div class="file-info">
                   <el-icon class="file-icon" :class="{ 'failed': file.status === 'fail' }">
                     <Document />
                   </el-icon>
                   <span class="file-name" :class="{ 'failed': file.status === 'fail' }">
                     {{ file.doc_name || file.name }}
                   </span>
                 </div>
                 <div class="file-status">
                   <!-- 上传中状态 -->
                   <template v-if="file.status === 'uploading'">
                     <span class="status-text uploading">上传中</span>
                     <el-icon class="loading-icon"><Loading /></el-icon>
                   </template>
                   <!-- 失败状态 -->
                   <template v-else-if="file.status === 'fail'">
                     <span class="status-text failed">查看失败原因</span>
                   </template>
                   <!-- 成功状态 -->
                   <template v-else>
                     <span class="status-text success">{{ formatFileSize(file.size) }}</span>
                     <el-icon class="delete-icon" @click="handleFileRemove(file)">
                       <Delete />
                     </el-icon>
                   </template>
                 </div>
               </div>
             </div>
           </div>
         </el-form-item>
       </template>
    </div>

    <!-- 分片部分 -->
    <div class="form-section">
      <h3 class="section-title">
        <span class="icon"><i class="iconfont ChatData-hebing"></i></span>
        <span class="text">分片</span>
      </h3>
      <!-- 文本 -->
      <template v-if="formData.doc_type === 'TEXT'">
        <el-form-item class="type-formItem chunk-form">
          <el-radio-group v-model="formData.chunk_strategy">
          <!-- 自动切片 -->
          <el-radio value="Automatic" class="chunking-radio">
            自动切片
          </el-radio>
          <!-- chunk size配置 -->
          <el-radio value="CHUNK_BY_SIZE" class="chunking-radio">
            chunk size
          </el-radio>
          <!-- separator配置 -->
          <el-radio value="CHUNK_BY_SEPARATOR" class="chunking-radio">
            separator
          </el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="formData.chunk_strategy === 'Automatic'" class="chunking-config">
          <el-text type="info" size="small">
            不需要配置分片参数
          </el-text>
        </div>
        <div v-if="showChunkSizeConfig" class="chunking-config">
          <el-row>
            <el-col :span="12">
              <el-form-item prop="chunk_size">
                <template #label>
                    <span>chunk_size</span>
                    <el-tooltip
                      content="The size of the data chunks used in processing."
                      placement="top"
                    >
                      <i class="iconfont ChatData-zhushi tip-icon"></i>
                    </el-tooltip>
                </template>
                <el-input v-model="formData.chunk_size" type="number" placeholder="请输入chunk_size" clearable>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="11" :offset="1">
              <el-form-item  prop="chunk_overlap">
                <template #label>
                    <span>chunk_overlap</span>
                    <el-tooltip
                      content="The amount of overlap between adjacent data chunks."
                      placement="top"
                    >
                      <i class="iconfont ChatData-zhushi tip-icon"></i>
                    </el-tooltip>
                </template>
                <el-input v-model="formData.chunk_overlap" type="number" placeholder="请输入chunk_overlap" clearable>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div v-if="showSeparatorConfig" class="chunking-config">
          <el-form-item prop="enable_merge"   class="flex-item">
            <template #label>
              <span>enable_merge</span>
              <el-tooltip
                content="Whether to merge according to the chunk_size after splitting by the separator."
                placement="top"
              >
                <i class="iconfont ChatData-zhushi tip-icon"></i>
              </el-tooltip>
            </template>
            <el-checkbox v-model="formData.enable_merge" ></el-checkbox>
          </el-form-item>
          <el-form-item prop="separator">
            <template #label>
              <span>separator</span>
              <el-tooltip
                content="chunk separator"
                placement="top"
              >
                <i class="iconfont ChatData-zhushi tip-icon"></i>
              </el-tooltip>
            </template>
            <el-input :rows="2" type="textarea" v-model="formData.separator" placeholder="chunk separator" clearable>
            </el-input>
          </el-form-item>
        </div>
      </template>
       <!-- 文件上传切片 -->
      <template v-else >
        <el-collapse expand-icon-position="left" class="customCollapse"  :model-value="fileList.map((item, idx) => idx)">
          <el-collapse-item :title="item.doc_name || item.name" :name="idx" v-for="(item,idx) in fileList" :key="idx">
            <el-form-item class="type-formItem chunk-form">
              <el-radio-group v-model="item.chunk_strategy">
              <!-- 自动切片 -->
              <el-radio value="Automatic" class="chunking-radio">
                自动切片
              </el-radio>
              <!-- chunk size配置 -->
              <el-radio value="CHUNK_BY_SIZE" class="chunking-radio">
                chunk size
              </el-radio>
              <!-- separator配置 -->
              <el-radio value="CHUNK_BY_SEPARATOR" class="chunking-radio">
                separator
              </el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="item.chunk_strategy === 'Automatic'" class="chunking-config">
              <el-text type="info" size="small">
                不需要配置分片参数
              </el-text>
            </div>
            <div v-if="item.chunk_strategy === 'CHUNK_BY_SIZE'" class="chunking-config">
              <el-row>
                <el-col :span="12">
                  <el-form-item prop="chunk_size">
                    <template #label>
                        <span>chunk_size</span>
                        <el-tooltip
                          content="The size of the data chunks used in processing."
                          placement="top"
                        >
                          <i class="iconfont ChatData-zhushi tip-icon"></i>
                        </el-tooltip>
                    </template>
                    <el-input v-model="item.chunk_size" type="number" placeholder="请输入chunk_size" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="11" :offset="1">
                  <el-form-item  prop="chunk_overlap">
                    <template #label>
                        <span>chunk_overlap</span>
                        <el-tooltip
                          content="The amount of overlap between adjacent data chunks."
                          placement="top"
                        >
                          <i class="iconfont ChatData-zhushi tip-icon"></i>
                        </el-tooltip>
                    </template>
                    <el-input v-model="item.chunk_overlap" type="number" placeholder="请输入chunk_overlap" clearable>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
            <div v-if="item.chunk_strategy === 'CHUNK_BY_SEPARATOR'" class="chunking-config">
              <el-form-item prop="enable_merge"  class="flex-item">
                <template #label>
                  <span>enable_merge</span>
                  <el-tooltip
                    content="Whether to merge according to the chunk_size after splitting by the separator."
                    placement="top"
                  >
                    <i class="iconfont ChatData-zhushi tip-icon"></i>
                  </el-tooltip>
                </template>
                <el-checkbox v-model="item.enable_merge" ></el-checkbox>
              </el-form-item>
              <el-form-item prop="separator">
                <template #label>
                  <span>separator</span>
                  <el-tooltip
                    content="chunk separator"
                    placement="top"
                  >
                    <i class="iconfont ChatData-zhushi tip-icon"></i>
                  </el-tooltip>
                </template>
                <el-input :rows="2" type="textarea" v-model="item.separator" placeholder="chunk separator" clearable>
                </el-input>
              </el-form-item>
            </div>
          </el-collapse-item>
        </el-collapse>
      </template>

    </div>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { Document, Upload, Loading, Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { uploadEventtApi,delDocumentApi } from "@/common/apis/knowledge"
import { useRoute } from "vue-router"
const route = useRoute()
interface FileFormData {
  doc_type: string
  doc_name: string
  source: string
  content: string,
  files:any,
  chunk_strategy: string
  chunk_size: number
  chunk_overlap: number
  separator: string
  enable_merge: boolean
}

interface Props {
  modelValue?: Partial<FileFormData>,
  basicData?:any
}

interface Emits {
  (e: 'update:modelValue', value: FileFormData): void
}
// 是否为新增文件
const isFileAdd= computed<boolean>(() => {
  return route?.query?.knowledge_name ? true : false
})
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive<FileFormData>({
  doc_type: 'TEXT',
  doc_name: '',
  source: '',
  content: '',
  files:[],
  chunk_strategy: 'Automatic',
  chunk_size: 512,
  chunk_overlap: 50,
  separator: '\n',
  enable_merge: false,
  ...props.modelValue
})

// 表单规则
const rules = reactive<FormRules>({
  doc_name: [
    { required: true, message: '请输入文本名称', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入文本内容', trigger: 'blur' }
  ],
  files : [
    { required: true, message: '请上传文档', trigger: 'blur' }
  ],
  chunk_size: [
    { required: true, message: '请输入chunk_size', trigger: 'blur' }
  ],
  chunk_overlap: [
    { required: true, message: '请输入chunk_overlap', trigger: 'blur' }
  ],
  separator:  [
    {
      validator: (rule: any, value: any, callback: any) => {
        // 允许默认值 '\n'，但检查用户是否清空了输入框
        if (!value.trim()) {
          callback(new Error('请输入separator'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  enable_merge:  [
    {
      validator: (rule: any, value: any, callback: any) => {
        // 对于 boolean 类型，我们检查它是否是 undefined 或 null
        if (!value) {
          callback(new Error('请选择 enable_merge'))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ],
})

const formRef = ref<FormInstance>()
const uploadRef = ref()

// 文件列表
const fileList = ref<any[]>([])

// 表单验证方法
const validateForm = async () => {
  if (!formRef.value) return false;
  try {
    await formRef.value.validate();
    return true;
  } catch (error) {
    return false;
  }
};

// 文件拖拽处理
const handleDrop = (e: DragEvent) => {
  e.preventDefault()
  const files = e.dataTransfer?.files
  if (files) {
    Array.from(files).forEach(file => {
      handleFileChange(file)
    })
  }
}

// 文件选择处理
const handleFileChange = (file:any) => {
  // const uploadFile: UploadFile = {
  //   name: file.name,
  //   size: file.size,
  //   status: 'ready',
  //   uid: Date.now() + Math.random()
  // }
  // 检查文件格式
  // const allowedTypes = ['.pdf', '.csv', '.txt', '.doc', '.docx', '.md', '.zip']
  // const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))

  // if (!allowedTypes.includes(fileExtension)) {
  //   uploadFile.status = 'fail'
  //   ElMessage.error(`不支持的文件格式: ${fileExtension}`)
  // } else {
  //   uploadFile.status = 'ready'
  //   simulateUpload(uploadFile)
  // }
  file.status = 'ready'
  simulateUpload(file)
}

// 文件移除处理
const handleFileRemove = (file: any) => {
  ElMessageBox.confirm(`确定删除文档 '${file.name || file.doc_name}' 吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    const basicData = props.basicData as { name: string};
    delDocumentApi(basicData.name,{doc_name:file.name || file.doc_name,is_temp_space:true})
      .then(async (res: any) => {
        ElMessage.success("删除成功");
        const index = fileList.value.findIndex(f => f.uid === file.uid)
        if (index > -1) {
          fileList.value.splice(index, 1)
        }
        formData.files = fileList
      })
      .catch((err: any) => {
        ElMessage.error("删除失败");
      });
  });

}

// 模拟上传过程
const simulateUpload = (file: any) => {
  file.status = 'uploading';
  const basicData = props.basicData as { name: string, id:string };
  const space_name = basicData.name
  const raw = file as { raw: any };
  const formDataObj = new FormData();
  formDataObj.append("doc_file", file.raw); // file.raw 兼容 el-upload 组件
  formDataObj.append("doc_name", file.name);
  formDataObj.append("doc_type", formData.doc_type);
  if(!basicData.id && !isFileAdd.value) {
    // 使用临时空间创建
    formDataObj.append("is_temp_space", 'true');
  }
  uploadEventtApi(space_name, formDataObj)
    .then((res: any) => {
      file.status = 'success';
      if(!isFileAdd.value) {
        let fileData = res.data
        fileData.size = file.size
        Object.assign(fileData, {
          chunk_strategy: 'Automatic',
          chunk_size: 512,
          chunk_overlap: 50,
          separator: '\n',
          enable_merge: false,
        })
        fileList.value.push(fileData)
      } else {
        Object.assign(file, {
          doc_id:res.data,
          chunk_strategy: 'Automatic',
          chunk_size: 512,
          chunk_overlap: 50,
          separator: '\n',
          enable_merge: false,
        })
        fileList.value.push(file)
      }
      // 清除校验
      formData.files = fileList
      if(fileList.value.length) {
        formRef.value.clearValidate()
      }
      ElMessage.success(`${file.name} 上传成功`);
    })
    .catch((err: any) => {
      file.status = 'fail';
      ElMessage.error(`${file.name} 上传失败`);
    });
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 重置表单
const resetForm = (params: Partial<FileFormData> = {}) => {
  if (!formRef.value) return
  formRef.value.resetFields()
  fileList.value = []
  Object.assign(formData, {
    doc_type: 'TEXT',
    doc_name: '',
    source: '',
    content: '',
    chunk_strategy: 'Automatic',
    chunk_size: 512,
    chunk_overlap: 50,
    separator: '\n',
    enable_merge: false,
    ...params
  })
};

// 获取表单数据
const getFormData = () => {
  return {
    ...formData,
    files: fileList.value
  }
};

// 抛出方法
defineExpose({
  formData,
  validateForm,
  resetForm,
  getFormData,
});

// 监听表单数据变化，向父组件发送更新
const emitFormData = () => {
  emit('update:modelValue', { ...formData })
}

// 监听表单数据变化
Object.keys(formData).forEach(key => {
  watch(() => formData[key as keyof FileFormData], emitFormData)
})

// 计算属性：是否显示文本相关字段
const showTextFields = computed(() => formData.doc_type === 'TEXT')

// 计算属性：是否显示chunk size配置
const showChunkSizeConfig = computed(() => formData.chunk_strategy === 'CHUNK_BY_SIZE')

// 计算属性：是否显示separator配置
const showSeparatorConfig = computed(() => formData.chunk_strategy === 'CHUNK_BY_SEPARATOR')
</script>

<style lang="scss" scoped>
.file-form {
  max-width: 860px;
  margin: 0 auto 20px;
}

.form-section {
  border-left: 1px solid #ebeef5;
  position: relative;
  padding: 45px 0 20px 25px;
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    position: absolute;
    left: -16px;
    top: -10px;

    .icon {
      margin-right: 15px;
      font-weight: normal;
      width: 20px;
      height: 20px;
      border-radius: 20px;
      padding: 5px;
      background: #EBF3FD;
      border: 1px solid #D9E7FC;
      color: #005EE0
    }
  }

  .type-formItem {
    display: block;

    :deep(.el-form-item__content) {
      display: block;
      width: 100%;

      .el-radio-group {
        display: block;
      }
    }
  }

  .chunking-config {
    :deep(.el-text) {
      background: #FAFAFA;
      border:1px solid #EDEDED;
      border-radius: 5px;
      padding:10px 20px;
      display: block;
    }
    .tip-icon {
      font-size: 13px;
      color: #005ee0;
      cursor: pointer;
      margin-left:5px;
    }
    .flex-item {
      display: flex;
      align-items: baseline;
    }
  }
}

.content-type-options {
  display: flex;
  gap: 20px;

  .content-type-option {
    margin-right: 0;
    line-height: 20px;
    width: 50%;
    flex: 1;
    border: 1px solid #dcdfe6;
    border-radius: 8px;
    padding: 10px;
    height: 70px;
    transition: all 0.3s;
    position: relative;

    :deep(.el-radio__input) {
      position: absolute;
      right: 10px;
      top: 10px;
    }

    :deep(.el-radio__label) {
      padding-left: 0;
    }

    &:hover {
      border-color: #409eff;
    }

    &.is-checked {
      border-color: #409eff;
      background-color: #f0f9ff;
    }

    .option-content {
      display: flex;
      align-items: center;
      gap: 10px;

      .option-icon {
        font-size: 30px;
        padding:2px 8px 8px 10px;
        border-radius: 5px;
        font-weight: bold;
        &.text {
          color:#005EE0;
          background: #DCECFF;
        }
        &.document {
          color:#2BBF79;
          background: #DFF6EB;
        }
      }

      .option-text {
        .option-title {
          font-size: 14px;
          font-weight: 600;
          color: #555;
        }

        .option-desc {
          font-size: 12px;
          color: #8C8C8C;
        }
      }
    }
  }
}
.file-form {
  :deep(.el-form-item__content) {
    display: block;
    width: 100%;
  }
}
// 文件上传样式
.upload-container {
  .upload-area {
    .upload-dragger {
      width: 100%;
      height: 180px;
      border: 2px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: border-color 0.3s;

      &:hover {
        border-color: #409eff;
      }

      .upload-icon {
        font-size: 48px;
        color: #c0c4cc;
        margin-bottom: 16px;
      }

      .upload-text {
        color: #606266;
        font-size: 14px;
      }
    }
  }

  .format-tip {
    margin-top: 8px;
    color: #909399;
    font-size: 12px;
  }

  .file-list {
    margin-top: 16px;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding:5px 12px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      margin-bottom: 8px;
      transition: all 0.3s;

      &:hover {
        background-color: #f5f7fa;
      }

      &.uploading {
        border-color: #e6a23c;
        background-color: #fdf6ec;
      }

      &.failed {
        border-color: #f56c6c;
        background-color: #fef0f0;
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .file-icon {
          font-size: 20px;
          color: #409eff;

          &.failed {
            color: #f56c6c;
          }
        }

        .file-name {
          color: #303133;
          font-size: 14px;
          word-break: break-all;

          &.failed {
            color: #f56c6c;
          }
        }
      }

      .file-status {
        display: flex;
        align-items: center;
        gap: 8px;

        .status-text {
          font-size: 12px;

          &.uploading {
            color: #e6a23c;
          }

          &.failed {
            color: #409eff;
            cursor: pointer;

            &:hover {
              text-decoration: underline;
            }
          }

          &.success {
            color: #67c23a;
          }
        }

        .loading-icon {
          font-size: 14px;
          color: #409eff;
          animation: rotate 1s linear infinite;
        }

        .delete-icon {
          font-size: 16px;
          color: #909399;
          cursor: pointer;

          &:hover {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .content-type-options {
    flex-direction: column;
    gap: 12px;
  }
}
.customCollapse {
  border-top:none;
  border-bottom: none;
  .el-collapse-item {
    border:1px solid #F2F2F2;
    margin-bottom: 10px;
    border-bottom: none;
  }
  :deep(.el-collapse-item__content){
    padding:10px 20px 20px;
  }
  :deep(.el-collapse-item__header) {
    height: 36px;
    padding:0 20px;
    line-height:36px;
    background: #FCFCFC;
    border-bottom: 1px solid #F2F2F2;
  }
}
</style>
