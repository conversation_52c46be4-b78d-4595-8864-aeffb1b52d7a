import { request } from "@/http/axios"

// 创建知识库
export function addKnowledgeTmpApi(data: any) {
  return request({
    url: "/space/create_from_temp",
    method: "post",
    data,
    version: "knowledge"
  })
}

// 创建知识库
export function addKnowledgeApi(data: any) {
  return request({
    url: "/space/add",
    method: "post",
    data,
    version: "knowledge"
  })
}

/** 获取知识库列表 */
export function getKnowledgeListApi<T extends object>(data?: T) {
  return request({
    url: "/space/list",
    method: "post",
    data,
    version: "knowledge"
  })
}

/** 删除知识库 */
export function delKnowledgeApi<T extends object>(data?: T) {
  return request({
    url: "/space/delete",
    method: "post",
    data,
    version: "knowledge"
  })
}

/** arguments列表 */
export function getArgumentsListApi(space_id: string, data?: any) {
  return request({
    url: `/${space_id}/arguments`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 召回配置
export function getRecallRetrieversApi(space_id: string, data: any) {
  return request({
    url: `/${space_id}/recall_retrievers`,
    method: "get",
    data,
    version: "knowledge"
  })
}
// 召回测试
export function postRecallTestApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/recall_test`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 保存Arguments
export function postArgumentApi(space_id: string, data: any) {
  return request({
    url: `/${space_id}/argument/save`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 新增文本
export function postDocumentApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/document/add`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 编辑文档
export function editDocumentApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/document/edit`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 分片策略
export function getChunkstrategiesApi(data: any) {
  return request({
    url: `/document/chunkstrategies`,
    method: "get",
    data,
    version: "knowledge"
  })
}
// 知识库配置（知识库列表后调这个）
export function getSpaceConfigApi(data: any) {
  return request({
    url: `/space/config`,
    method: "get",
    data,
    version: "knowledge"
  })
}
// 文档列表
export function getDocumentListApi(space_name: string | number, data: any) {
  return request({
    url: `/${space_name}/document/list`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 查看图谱
export function getGraphvisApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/graphvis`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 删除文档
export function delDocumentApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/document/delete`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 文件上传
export function uploadEventtApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/document/upload`,
    method: "post",
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    version: "knowledge"
  })
}

// 下载文档
export function downDocument(space_name: string, id: string) {
  return request({
    url: `/${space_name}/document/download_stream/${id}`,
    method: "get",
    version: "knowledge"
  })
}

// 下载文档模板
export function downDocumentTemp<T extends Record<string, any>> (data ?: T) {
  return request({
    url: `/csv_template/download_stream`,
    method: "get",
    version: "knowledge"
  })
}

// 文档同步（单独同步）
export function syncDocumentApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/document/sync`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 批量同步（切片处理）
export function syncbatchDocumentApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/document/sync_batch`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 切片列表（文档详情）
export function getChunkListApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/chunk/list`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// 编辑分片（文档详情内）
export function editChuntApi(space_name: string, data: any) {
  return request({
    url: `/${space_name}/chunk/edit`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// Similarity Query
export function getQueryApi(vector_name: string, data: any) {
  return request({
    url: `/${vector_name}/query`,
    method: "post",
    data,
    version: "knowledge"
  })
}
// Document Summary
export function getDocumentSummaryApi(data: any) {
  return request({
    url: `/document/summary`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 标签列表
export function getTagList(data?: any) {
  return request({
    url: `/label/list`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 新增标签
export function addTag(data?: any) {
  return request({
    url: `/label/add`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 编辑标签
export function editTag(data?: any) {
  return request({
    url: `/label/edit`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 删除标签
export function delTag(id: string, data?: any) {
  return request({
    url: `/label/delete?label_id=${id}`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 知识库关联标签调整
export function updateKnowledgeTag(data?: any) {
  return request({
    url: `/label/assign_batch`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 召回测试
export function recallTest(name: string, data?: any) {
  return request({
    url: `/${name}/recall_test`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 获取召回方法
export function getRecallFn(name: string, data?: any) {
  return request({
    url: `/${name}/recall_retrievers`,
    method: "get",
    data,
    version: "knowledge"
  })
}

// 新增分片
export function addChunk(name: string, data?: any) {
  return request({
    url: `/${name}/chunk/add`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 编辑分片
export function editChunk(name: string, data?: any) {
  return request({
    url: `/${name}/chunk/edit`,
    method: "post",
    data,
    version: "knowledge"
  })
}

// 删除分片
export function delChunk(name: string, id: string) {
  return request({
    url: `/${name}/chunk/delete?chunk_id=${id}`,
    method: "post",
    version: "knowledge"
  })
}

// 批量添加分片
export function batchAddChunk<T extends Record<string, any>>(name: string, data: T) {
  return request({
    url: `/${name}/chunk/batch_add`,
    method: "post",
    data,
    headers: {
      "Content-Type": "multipart/form-data"
    },
    version: "knowledge"
  })
}