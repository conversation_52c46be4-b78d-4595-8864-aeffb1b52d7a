<script lang="ts" setup>
import { ref, watch, defineProps, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: Boolean,
  type: {
    type: String,
    default: 'add',
  },
  permissionDialogData: {
    type: Object,
    default: () => ({})
  }
});
const emit = defineEmits(['update:visible', 'success']);

const formRef = ref();
const form = ref({
  permission_name: '',
  description: '',
});
const rules = {
  permission_name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
  ],
};

watch(() => props.visible, (val) => {
  if (val) {
    if (props.type === 'add') {
      form.value = { permission_name: '', description: '' };
    } else if (props.type === 'edit' && props.permissionDialogData) {
      form.value = {
        permission_name: props.permissionDialogData.permission_name || '',
        description: props.permissionDialogData.description || '',
      };
    }
  }
});

function handleClose() {
  emit('update:visible', false);
}
function handleSubmit() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      emit('success', { ...form.value });
      handleClose();
    }
  });
}
</script>

<template>
  <el-dialog
    :model-value="props.visible"
    :title="props.type === 'add' ? '新增权限' : '重命名数据权限'"
    width="400"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="权限名称" prop="permission_name">
        <el-input v-model="form.permission_name" placeholder="请输入权限名称" />
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" resize="none" placeholder="定义描述，以便快速理解" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">{{ props.type === 'add' ? '新增' : '保存' }}</el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
</style>
