chatdata-ui 是智能问数产品前端，基于Vue3、Vite、TypeScript、Element Plus等主流技术研发。

<details>
<summary>推荐环境</summary>

<br>

- 新版 `Visual Studio Code`
- 安装 `.vscode/extensions.json` 文件中推荐的插件
- `node` 20.x 或 22+
- `yarn` 1.22+
- `pnpm` 9.x 或 10+

</details>

<details>
<summary>本地开发</summary>

<br>

```bash
# 克隆项目
git clone http://e-git.yfb.sunline.cn/yfb/llm/chatdata/chatdata-ui.git

# 进入项目目录
cd chatdata-ui

# 安装依赖
yarn

# 启动服务
yarn dev
```

</details>

<details>
<summary>debug调试</summary>

<br>

```bash
# 1. 首先在终端启动开发服务
yarn dev

# 2. 然后在 VSCode 中按 F5 或点击"运行和调试"面板中的"针对 Chrome 启动"
# 这将打开 Chrome 浏览器并连接到 localhost:9080，您可以在代码中设置断点进行调试
```

</details>

<details>
<summary>打包构建</summary>

<br>

```bash
# 打包构建预发布环境
yarn run build:staging

# 打包构建生产环境
yarn build
```

</details>

<details>
<summary>本地预览</summary>

<br>

```bash
# 先执行打包构建命令生成 dist 目录后再执行以下预览命令
yarn run preview
```

</details>

<details>
<summary>代码检查</summary>

<br>

```bash
# 代码校验与格式化
yarn run lint

# 单元测试
yarn run test
```

</details>

<details>
<summary>代码提交规范</summary>

<br>

`feat` 新功能

`fix` 修复错误

`perf` 性能优化

`refactor` 重构代码

`docs` 文档和注释

`types` 类型相关

`test` 单测相关

`ci` 持续集成、工作流

`revert` 撤销更改

`chore` 琐事（更新依赖、修改配置等）

</details>
