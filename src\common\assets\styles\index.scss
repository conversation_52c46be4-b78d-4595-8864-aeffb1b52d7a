// 全局 CSS 变量
@import "./variables.css";
// Transition
@import "./transition.scss";
// Element Plus
@import "./element-plus.css";
@import "./element-plus.scss";
// Vxe Table
@import "./vxe-table.css";
@import "./vxe-table.scss";
// 注册多主题
@import "./theme/register.scss";
// Mixins
@import "./mixins.scss";
// View Transition
@import "./view-transition.scss";

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 20px;
}

html {
  height: 100%;
  // 灰色模式
  &.grey-mode {
    filter: grayscale(1);
  }
  // 色弱模式
  &.color-weakness {
    filter: invert(0.8);
  }
}

body {
  height: 100%;
  font-size: 13px;
  overflow: hidden;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family:
    Inter, "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
  @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

// 覆盖按钮颜色
.el-button--primary:not(.is-plain), .el-radio-button.is-active .el-radio-button__original-radio:not(:disabled)+.el-radio-button__inner {
	border-color: #005EE0 !important;
	background-color: #005EE0 !important;
  color: #fff !important;
}
.el-button--primary:disabled, .el-radio-button__original-radio:is(:disabled)+.el-radio-button__inner {
	border-color: #005EE0 !important;
	background-color: #005EE0 !important;
  opacity: 0.4;
}
.flex-row{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.flex-column{
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.flex1{
  flex: 1;
  overflow: auto;
}
.ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.gray-inp-bg {
  .el-input__wrapper {
    background-color: #F2F2F2;
  }
}