<template>
  <el-collapse
    class="detail-collapse"
    v-model="detailCollapseVals"
    v-loading="isDataLoaded"
  >
    <!-- 基本配置 -->
    <el-collapse-item name="base" :icon="ArrowRightBold">
      <template #title>
        <span class="pre-title"> 基本信息 </span>
      </template>
      <el-descriptions :column="1" size="large" border>
        <el-descriptions-item label-width="180" label="助手类型">{{
          detailData?.app_type
        }}</el-descriptions-item>
        <el-descriptions-item label="温度">{{
          detailData?.temperature
        }}</el-descriptions-item>
        <el-descriptions-item label="最大输出Token">{{
          detailData?.max_tokens
        }}</el-descriptions-item>
        <el-descriptions-item label="使用者">{{
          detailData?.user_name || "--"
        }}</el-descriptions-item>
        <el-descriptions-item label="描述">{{
          detailData?.app_describe
        }}</el-descriptions-item>
        <el-descriptions-item label="实例问题">
          <div v-html="getExample(detailData?.recommend_questions)"></div>
        </el-descriptions-item>
      </el-descriptions>
    </el-collapse-item>
    <!-- 模型配置 -->
    <el-collapse-item name="model" :icon="ArrowRightBold">
      <template #title>
        <span class="pre-title"> 模型配置 </span>
      </template>
      <!-- 模型选择 -->
       <div class="switch-wrapper">
      <span
        :class="['switch-item', curSceneType === key && 'active-switch' ]"
        v-for="(item, key) in switchOptions"
        :key="key"
        @click="handleSwitchClick(item.value)"
      >
        {{ item.label }}
        <el-tooltip :content="item.tip" placement="top" v-if="!!item.tip">
          <i
            class="iconfont ChatData-zhushi tip-icon"
            style="margin-top: 2px"
          ></i>
        </el-tooltip>
        <!-- <el-switch v-model="modelConfig[item.value].enable" /> -->
      </span>
    </div>
      <template v-if="!!curSceneType">
        <el-descriptions :column="1" size="large" border>
          <el-descriptions-item label-width="180" label="应用模型">{{
            switchOptions?.[curSceneType]?.applicationModel
          }}</el-descriptions-item>
          <el-descriptions-item label="提示词模板">{{
            switchOptions?.[curSceneType]?.promptTemplate
          }}</el-descriptions-item>
        </el-descriptions>
      </template>
    </el-collapse-item>
  </el-collapse>
</template>
<script lang="ts" setup>
import { ArrowRightBold } from "@element-plus/icons-vue";
import { get } from "http";
import { cloneDeep } from "lodash-es";

// 折叠面板val
const detailCollapseVals = ref<string[]>(["base", "model"]);

const props = defineProps<{
  data: any;
}>();
// 详情数据
const detailData = ref<any>({});
// 添加加载状态
const isDataLoaded = ref(true);
// 开关配置
const switchOptions = ref<any>({});
// 是否显示模型配置的详情
const isShowModelConfig = computed(() => {
  let show = false;
  // 只要有一个显示true
  for (let key in switchOptions.value) {
    if (switchOptions.value[key].enable === true) {
      show = true;
      break;
    }
  }
  return show;
});
// 选择的模型
const curSceneType = ref<string | number>("");
// =------------------------------

// 选择模型
function handleSwitchClick(value: string) {
  curSceneType.value = value;
}
// 获取实例问题
function getExample(example: any[] = []) {
  let exampleStr = "";
  example.forEach((item: any) => {
    exampleStr += `${item.question};<br />`;
  });
  return exampleStr;
}

function initSwitchOptions() {
  switchOptions.value = {
    sqlAnalysis: {
      label: "语义SQL解析",
      value: "sqlAnalysis",
      tip: "通过大模型做语义解析生成S2SQL",
    },
    sqlRepair: {
      label: "语义SQL修订",
      value: "sqlRepair",
      tip: "通过大模型对解析S2SQL做二次修正",
    },
    resultDataParsing: {
      label: "结果数据解读",
      value: "resultDataParsing",
      tip: "通过大模型对结果数据做提炼总结",
    },
  };
}

// 监听数据变化
watch(
  () => props.data,
  (newVal) => {
    if (newVal) {
      isDataLoaded.value = false;
      detailData.value = cloneDeep(newVal);
      initSwitchOptions()
      // 重置模型选项
      curSceneType.value = "";
      // 根据paramsneed 重新设置 模型和 部分参数
      (detailData?.value?.param_need ?? []).forEach((item) => {
        if (item.type === "temperature" || item.type === "max_tokens") {
          detailData.value[item.type] = item.value;
        } else {
          for (let key in item) {
            if (switchOptions.value[key]) {
              // 根据已选择的模型设置显示数据
              if (item[key].enable === true) {
                // 没有时进行初始化 默认选中第一个
                !curSceneType.value && (curSceneType.value = key);
                // 拼接参数
                switchOptions.value[key] = {
                  ...item[key],
                  ...switchOptions.value[key],
                };
              } else {
                // 没有全部置空
                delete switchOptions.value[key];
                // curSceneType.value = "";
              }
            }
          }
        }
      });
      // console.log(newVal, switchOptions.value, curSceneType.value, 99999);
    }
  },
  { immediate: true, flush: "post", deep: true }
);
</script>
<style scoped lang="scss">
.detail-collapse {
  border: none;
  width: 100%;
  :deep(.el-collapse-item__header) {
    display: flex;
    align-items: center;
    gap: 20px;
    border: none;
  }
  .el-collapse-item__arrow {
    margin: 0px;
    margin-right: auto;
    color: #4c6f88;
  }
  :deep(.el-collapse-item__wrap) {
    border: none;
  }
  .el-descriptions__label {
    background: #fcfcfc;
    color: #595959;
  }
}
.pre-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  &::before {
    content: "";
    height: 12px;
    width: 3px;
    display: inline-block;
    background: #005ee0;
  }
}
:deep(.switch-wrapper) {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  .switch-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 13px;
    padding: 0px 10px;
    color: #595959;
    background: #fafafa;
    border-radius: 4px;
    cursor: pointer;
    :deep(.el-switch__core) {
      min-width: 30px;
    }
  }
  .active-switch {
    background: #ebf3fd;
    color: #005ee0;
  }
}
</style>