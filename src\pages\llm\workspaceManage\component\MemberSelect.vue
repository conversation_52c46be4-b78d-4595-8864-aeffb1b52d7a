<template>
  <el-drawer v-model="drawerVisible" title="添加成员" size="500px" direction="rtl" @close="handleCancel" class="member-select-drawer">
    <div class="add-member-content">
      <div class="search-input">
        <el-input
          clearable
          size="small"
          placeholder="请输入内容"
          v-model="keyword"
          @input="onSearch"
        >
          <el-button
            slot="append"
            icon="el-icon-search"
            @click="debouncedSearch.flush"
          ></el-button>
        </el-input>
      </div>
      <div class="content-container">
        <div class="tree-container" ref="treeContainer">
          <el-tree
            ref="userTree"
            class="tree-list"
            :node-key="nodeKey"
            :lazy="false"
            :data="treeData"
            :default-expand-all="true"
            :props="{
              label: 'name',
              children: 'children',
              disabled: 'disabled',
              isLeaf: isLeaf,
            }"
            show-checkbox
            @check-change="onCheckChange"
            @check="onCheck"
          />
        </div>
        <div class="selected-panel">
          <div class="list mone-scrollbar">
            <div class="select-item" v-for="s in showList" :key="s[nodeKey]">
              <span class="name">{{ s.name }}</span>
              <el-icon class="deleteIcon"  @click="onDel(s)"><CircleCloseFilled /></el-icon>
            </div>
          </div>
          <div class="tools">
            <span class="num">已选 {{ showList.length }}项目</span>
            <el-button type="text" @click="onClear">清除已选</el-button>
          </div>
        </div>
      </div>
      <el-form-item label="角色">
        <el-select v-model="localRole" placeholder="请选择角色">
          <el-option label="空间管理员" value="space_admin" />
          <el-option label="开发者" value="developer" />
          <el-option label="访问者" value="visitor" />
        </el-select>
      </el-form-item>
      <div class="drawer-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { ref, watch, onMounted, onUnmounted } from 'vue';
import { uniqBy, debounce } from 'lodash';
import { getUseTreeApi } from '@/common/apis/workspace';

const props = defineProps({
  visible: Boolean,
  value: {
    type: Array,
    default: () => []
  },
  role: {
    type: String,
    default: 'visitor'
  },
  nodeKey: {
    type: String,
    default: 'user_id'
  },
  isMultiple: {
    type: Boolean,
    default: true
  },
  members:{
    type:Array,
    default:()=>[]
  },
});
const emit = defineEmits(['update:visible', 'update:value', 'update:role', 'confirm', 'cancel']);
interface TreeNode {
  user_id: string | number;
  name: string;
  type?: string;
  children?: TreeNode[];
  [key: string]: any;
}
const drawerVisible = ref(props.visible);
const localRole = ref(props.role);
const keyword = ref('');
const showList = ref<any[]>(props.value ? [...props.value] : []);
const treeData = ref<any[]>([]);
const originalTreeData = ref<any[]>([]); // 添加原始数据存储
const firstOpen = ref(true);
const userTree = ref<any>(null);
const nodeKey = props.nodeKey;
const isMultiple = props.isMultiple;

// 创建防抖的搜索函数
const debouncedSearch = debounce(() => {
  if (!keyword.value.trim()) {
    // 如果搜索关键词为空，显示所有数据
    treeData.value = [...originalTreeData.value];
    return;
  }

  // 前端过滤逻辑
  const filteredData = filterTreeData(originalTreeData.value, keyword.value.trim());
  treeData.value = filteredData;
}, 300);

watch(() => props.visible, v => (drawerVisible.value = v));
watch(drawerVisible, v => {
  emit('update:visible', v)
  if(v) {
    getuserList();
  }
});
// watch(() => props.value, v => (showList.value = [...v]));
watch(() => props.role, v => (localRole.value = v));

onMounted(() => {
});

onUnmounted(() => {
  // 取消防抖函数，避免内存泄漏
  debouncedSearch.cancel();
});

function getuserList(){
  getUseTreeApi({}).then((res) => {
    const response = res as { data: TreeNode[] }
    const data = response?.data || []
    data.forEach(item => {
      item.name = item.department
      item.children = item.users
    });
    // 遍历 data 的 children，如果存在于 props.members 中，则设置为 disabled，如果子节点全部为disabled，则父节点也为disabled
    data.forEach(item => {
      if (item.children && Array.isArray(item.children)) {
        // 标记所有子节点
        item.children.forEach(user => {
          if (
            Array.isArray(props.members) &&
            props.members.some((m: any) => m && 'user_id' in m && m.user_id === user.user_id)
          ) {
            user.disabled = true;
          } else {
            user.disabled = false;
          }
        });
        // 如果所有子节点都为 disabled，则父节点也 disabled
        if (item.children.length > 0 && item.children.every(user => user.disabled)) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      }
    });
    originalTreeData.value = data; // 保存原始数据
    treeData.value = data
  })
}

function onSearch() {
  debouncedSearch();
}

// 添加前端过滤函数
function filterTreeData(data: TreeNode[], keyword: string): TreeNode[] {
  return data.map(item => {
    // 检查部门名称是否匹配
    const departmentMatch = item.department && item.department.toLowerCase().includes(keyword.toLowerCase());

    // 过滤用户列表
    const filteredUsers = item.users ? item.users.filter(user => {
      const userNameMatch = user.name && user.name.toLowerCase().includes(keyword.toLowerCase());
      const userIdMatch = user.user_id && user.user_id.toString().includes(keyword);
      return userNameMatch || userIdMatch;
    }) : [];

    // 如果部门匹配或有匹配的用户，返回过滤后的数据
    if (departmentMatch || filteredUsers.length > 0) {
      return {
        ...item,
        name: item.department,
        children: filteredUsers
      };
    }

    return null;
  }).filter(Boolean) as TreeNode[];
}

function isDisabled(data: any, node: any) {
  const { user_id, department } = data;
  const { childNodes = [] } = node;
  return !childNodes.length && department;
}
function isLeaf(data: any, node: any) {
  const { type } = data;
  return type === 'user';
}
function onCheckChange(obj: any, isChecked: boolean) {
  if (isChecked) {
    if (isMultiple) {
      if (obj.user_id) {
        showList.value.push(obj);
      } else {
        showList.value.push(...(obj.children || []));
      }
    } else {
      if (obj.user_id) {
        showList.value = [obj];
      } else {
        showList.value = [...(obj.children || [])];
      }
    }
  } else {
    if (obj.user_id) {
      showList.value = showList.value.filter((item) => item[nodeKey] !== obj[nodeKey]);
    }
  }
  showList.value = uniqBy(showList.value, nodeKey);
  emit('update:value', showList.value);
}
function onCheck(obj: any, data: any) {
  // 可选：处理全选/半选逻辑
}
// function onDel(item: any) {
//   const idx = showList.value.findIndex((s) => s[nodeKey] === item[nodeKey]);
//   if (idx !== -1) {
//     showList.value.splice(idx, 1);
//     emit('update:value', [...showList.value]);
//   }
// }
function onDel(item: TreeNode) {
  const idx = showList.value.findIndex((s) => s[nodeKey] === item[nodeKey]);
  if (idx !== -1) {
    showList.value.splice(idx, 1);
    emit('update:value', [...showList.value]);
    nextTick(() => {
      // 取消树形中对应节点的选中状态
      if (userTree.value && userTree.value.setChecked) {
        userTree.value.setChecked(item[nodeKey], false, false);
      } else if (userTree.value && userTree.value.setCheckedKeys) {
        // 兼容 setCheckedKeys 方法
        const checkedKeys = userTree.value.getCheckedKeys(false) || [];
        userTree.value.setCheckedKeys(checkedKeys.filter(key => key !== item[nodeKey]));
      }
    });
  }
}
function onClear() {
  showList.value = [];
  userTree.value.setCheckedKeys([]);
  emit('update:value', []);
}
function handleCancel() {
  showList.value = [];
  userTree.value.setCheckedKeys([]);
  emit('cancel');
  drawerVisible.value = false;
}
function handleConfirm() {
  if (showList.value.length === 0) {
    ElMessage.warning('请选择要添加的成员')
    return
  }
  emit('update:role', localRole.value);
  emit('confirm', { selected: showList.value, role: localRole.value });
  drawerVisible.value = false;
}
</script>

<style scoped lang="scss">

.content-container {
  display: flex;
  height: calc(100vh - 240px);
  margin-bottom: 30px;
  min-width: 400px;
}
.selected-panel {
  width: 200px;
  display: flex;
  flex-direction: column;
  border: solid 1px #eff0f2;
  box-sizing: border-box;
  overflow: auto;
}
.select-item {
  display: flex;
  align-items: center;
  padding: 4px 6px;
  margin-bottom: 6px;
  background: #f3f3f3;
  border-radius: 3px;
  .deleteIcon {
    color:#999;
    cursor: pointer;
  }
}
.name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.el-icon-circle-close {
  cursor: pointer;
  &:hover {
    color: #005EE0;
    font-weight: bold;
  }
}
.list {
  flex: 1;
  padding: 10px;
  overflow: auto;
}
.tools {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background: #fcfcfc;
  border-top: solid 1px #eff0f2;
}
.num {
  line-height: 14px;
  color: #999;
}
.tree-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  max-width: 600px;
  border-radius: 3px;
  overflow: auto;
  border: solid 1px #eff0f2;
  border-right: 0;
}
.tree-list {
  flex: 1;
  overflow: auto;
}
.search-input {
  width: 100%;
  margin-bottom: 10px;
}
.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}
</style>
<style lang="scss">
.el-drawer__header {
  margin-bottom: 0;
}
</style>
