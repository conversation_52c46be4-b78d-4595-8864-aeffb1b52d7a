
import { v4 as uuidv4 } from 'uuid';

export function transformFieldData(data: any[], tables: any[] = []) {
    if (!data.length) return [];
    let dataMap = {
        layer: {},
        table: {},
        custom: {},
    }
    // 根据tables 初始化 table 生成唯一id
    if (tables.length) {
        // for (let [index, item] of tables) {
        tables.forEach((item, index) => {
            dataMap.table[item.nodeId] = {
                label: item.table_alias || item.table_name,
                display_type: "table",
                id: item.nodeId || uuidv4(),
                table_id: String(item.table_id),
                originIndex:index,
                children: [],
            }
        })
        // }
    }
    let newData = data.reduce(
        (pre, item) => {
            let {
                display_type,
                display_id,
                display_name,
                display_order,
                table_id,
                table_name,
            } = item;
            // 表格以tableid table name 判断
            if (!!display_id) {
                // 分层、自定义 不存在时先初始化
                if (!pre[display_type][display_id] && display_type !== "table") {
                    pre[display_type][display_id] = {
                        label: display_name,
                        id: display_id,
                        table_id,
                        display_type,
                        children: [],
                    };
                }
                if (display_type === "layer") {
                    // 分层需要按 order排列顺序
                    pre[display_type][display_id].children[display_order] = {
                        label: item.alias,
                        // id: item.field_id,
                        belong_uId: pre[display_type][display_id].id, // 分层 需要记录 父级id
                        ...item,
                    };
                } else if (display_type === "table" && !!pre[display_type][display_id]) {
                    pre[display_type][display_id].children.push({
                        label: item.alias,
                        // id: item.field_id,
                        belong_uId: pre[display_type][display_id].id, // 表格 需要记录 父级id
                        ...item,
                    });
                } else if (display_type === "custom") {
                    pre[display_type][display_id].children.push({
                        label: item.alias,
                        // id: item.field_id,
                        belong_uId: pre[display_type][display_id].id, // 自定义 需要记录 父级id
                        ...item,
                    });
                }
            }

            return pre;
        },
        dataMap
    );
    // 清除 层级下可能会有的空值
    Object.values(newData.layer).forEach((item: any) => {
        item.children = item.children.filter((item) => !!item);
    });
    // console.log(newData, 'init 1111');
    return [
        ...Object.values(newData.table).sort((a:any, b:any) => a.originIndex - b.originIndex),
        ...Object.values(newData.layer),
        ...Object.values(newData.custom),
    ];
}