<template>
<el-dialog
  v-model="visible"
  :close-on-click-modal="false"
  :close-on-press-escape="false"
  :append-to-body="true"
  title="重命名"
  width="300"
>
  <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
    <el-form-item label="表名称" prop="name">
      <el-input v-model="form.name" placeholder="请输入" clearable />
    </el-form-item>
  </el-form>
  <template #footer>
    <div class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="onSure">确定</el-button>
    </div>
  </template>
</el-dialog>
</template>
  
<script setup lang="ts">
const emit = defineEmits(['sure']);
let formRef = ref();
let visible = ref(false);
let form = ref({
  name: '',
});
let rules = {
  name: [ { required: true, message: '请输入名称', trigger: 'blur' }]
}

function show(name) {
  form.value.name = name;
  visible.value = true;
}

function onSure() {
  formRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    emit('sure', form.value.name);
    visible.value = false;
  });
}

defineExpose({
  show
})
</script>