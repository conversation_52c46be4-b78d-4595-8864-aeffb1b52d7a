<script lang="ts" setup>
import { useRoute } from "vue-router"
import logo from "@@/assets/images/layouts/sunline_logo_mini.png?url"
import logoText from "@@/assets/images/layouts/sunline_logo.png?url"
const route = useRoute()
interface Props {
  collapse?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  collapse: true
})
</script>

<template>
  <div class="layout-logo-container" :class="{ collapse: props.collapse }">
    <transition name="layout-logo-fade">
      <router-link v-if="props.collapse" key="collapse" :to="`/?spaceCode=`+route.query.spaceCode || ''">
        <img :src="logo" class="layout-logo">
      </router-link>
      <router-link v-else key="expand" :to="`/?spaceCode=`+route.query.spaceCode || ''">
        <img :src="logoText" class="layout-logo-text">
      </router-link>
    </transition>
  </div>
</template>

<style lang="scss" scoped>
.layout-logo-container {
  position: relative;
  width: 100%;
  height: var(--v3-navigationbar-height);
  line-height: var(--v3-navigationbar-height);
  text-align: center;
  overflow: hidden;
  .layout-logo {
    display: none;
  }
  .layout-logo-text {
    height: 25px;
    width: 125px;
    vertical-align: middle;
  }
}

.collapse {
  .layout-logo {
    width: 32px;
    height: 32px;
    vertical-align: middle;
    display: inline-block;
  }
  .layout-logo-text {
    display: none;
  }
}
</style>
