<template>
  <!-- vue-flow添加nodrag类名不可拖动 -->
  <div class="virtual-node"></div>
  <!-- 连接桩 -->
  <Handle type="target" :position="Position.Left" :connectable="false" style="opacity: 0"/>
  <Handle type="source" :position="Position.Right" :connectable="false" style="opacity: 0"/>
</template>

<script lang="ts" setup>
  import { Handle, Position } from '@vue-flow/core';

</script>

<style scoped lang="scss">
.virtual-node{
  width: 200px;
  height: 36px;
  border-radius: 4px;
  border: 1px dashed #005EE0;
}
</style>