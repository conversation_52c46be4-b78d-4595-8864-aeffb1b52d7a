<template>
  <div class="resource-authority el-row">
    <!-- 左侧分组列表 -->
    <div class="group-list el-col">
      <div class="small-title mb-3">资源权限管理</div>
      <el-select v-model="selectedGroupType" placeholder="助手管理" class="group-select" @change="getGroupListData">
        <el-option v-for="item in groupList" :key="item.type" :label="item.name" :value="item.type" />
      </el-select>
      <div style="height: 100%; overflow: scroll;">
        <div v-for="item in filteredGroups" :key="item.id"
          :class="['group-item', { active: selectedGroup.id === item.id }]"
          @click="selectedGroup = item; getMemberData()">
          <span>{{ item.name }}</span>
        </div>
      </div>
    </div>
    <!-- 右侧成员权限表格 -->
    <div class="member-table el-col">
      <div class="table-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
        <div class="small-title">{{ selectedGroup.name }}</div>
        <div>
          <el-button  v-permission="['member:add']" type="primary"  @click="drawerVisible = true" :disabled="selectedGroup.id ? false : true">添加成员</el-button>
          <el-button  v-permission="['member:delete']" style="margin-left: 8px;"
            :disabled="selectedRows.length === 0"
            @click="handleBatchRemove"
          >批量移除</el-button>
        </div>
      </div>
      <el-table
        :data="members"
        width="100%"
        @selection-change="onSelectionChange"
        v-if="fresh"
        v-loading="memberLoading"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="name" label="成员"  />
        <el-table-column v-for="col in filterPermissionColumns" :key="col.prop" :prop="col.prop" :label="col.label" align="center" width="80">
          <template #default="scope">
            <el-checkbox v-model="scope.row[col.prop]" disabled />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template #default="scope">
            <el-popover placement="bottom" width="180" trigger="click">
              <div style="padding: 10px;">
                <div v-for="item in filterPermissionColumns" :key="item.prop" @click="togglePermission(scope.row, item.prop)"
                  :style="{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer',
                    color: scope.row.permissions && scope.row.permissions.includes(item.prop) ? '#222' : '#666',
                    marginBottom: '12px',
                  }">
                  <span style="flex: 1;">{{ item.label }}</span>
                  <el-icon v-if="scope.row.permissions && scope.row.permissions.includes(item.prop)" color="#1677ff">
                    <Check />
                  </el-icon>
                </div>
              </div>
              <template #reference>
                <el-button v-if="GetPermission(['member:set_role'])" type="text" size="small" @click.stop>成员权限</el-button>
              </template>
            </el-popover>
            <el-tooltip v-if="GetPermission(['member:delete'])" effect="dark" content="移除成员" placement="bottom">
              <i class="iconfont ChatData-yichu deleteIcon" @click="removeMember(scope.row)"></i>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 16px; text-align: right;">
        <el-pagination background layout="total, prev, pager, next" :total="100" :page-size="5" :current-page="1" />
      </div>
    </div>
    <AddMemberDrawer v-model="drawerVisible" @add="onAddMembers" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import AddMemberDrawer from './AddMemberDrawer.vue';
import { getAgentListApi } from '@/common/apis/chat';
import { getSemanticModelListApi } from '@/common/apis/semantic';
import { getAngenMemberApi, getDatamodelMemberApi, getAngenPermissionsApi, getDatamodelPermissionsApi, deleteAngenPermissionsApi, deleteDatamodelPermissionsApi } from '@/common/apis/llm';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Check } from '@element-plus/icons-vue';
import {useRoute } from 'vue-router'
const drawerVisible = ref(false);
const selectedGroupType = ref('agent');
const route = useRoute()
const groupList = ref([
  { type: 'agent', name: '助手管理' },
  { type: 'datamodel', name: '数据模型管理' }
]);
const spaceCode = computed(()=> route.query.spaceCode)
const groupSearch = ref('');
const selectedGroup = ref({ name: '', id: '' });
const groups = ref([]);
const filteredGroups = computed(() => {
  if (!groupSearch.value) return groups.value;
  return groups.value.filter(g => g.name.includes(groupSearch.value));
});
const fresh = ref(true)
const memberLoading = ref(false)
const permissionColumns = [
  { prop: 'view', label: '查看' },
  { prop: 'edit', label: '编辑' },
  { prop: 'delete', label: '删除' },
  { prop: 'publish', label: '发布' },
];
const filterPermissionColumns = computed(()=>{
  return selectedGroupType.value === 'agent'
    ? permissionColumns
    : permissionColumns.filter(col => col.prop !== 'publish');
})
provide("permissionColumns", filterPermissionColumns);
const members = ref([]);
const selectedRows = ref([]);
function onSelectionChange(val: any[]) {
  selectedRows.value = val;
}

function handleBatchRemove() {
  if (selectedRows.value.length === 0) return;
  ElMessageBox.confirm(
    `确定要批量移除选中的${selectedRows.value.length}个成员吗？删除后无法恢复。`,
    '批量删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    const usernames = selectedRows.value.map((row: any) => row.username);
    let payload = {
      usernames,
    };
    let deleteServiveAPI = selectedGroupType.value === 'agent'
      ? deleteAngenPermissionsApi(selectedGroup.value.id, payload)
      : deleteDatamodelPermissionsApi(selectedGroup.value.id, payload);
    deleteServiveAPI.then(res => {
      const response = res as { success: boolean };
      if (response.success) {
        ElMessage.success('批量移除成功');
        getMemberData();
        selectedRows.value = [];
      }
    });
  }).catch(() => {});
}

function togglePermission(row: any, prop: string) {
  if (!row.permissions) row.permissions = [];
  const idx = row.permissions.indexOf(prop);
  if (idx > -1) {
    row.permissions.splice(idx, 1);
  } else {
    row.permissions.push(prop);
  }
  if (!row.permissions.includes('view') && row.permissions.length > 0) {
    row.permissions.unshift('view');
  }
  onPermissionChange(row, row.permissions);
}

function onPermissionChange(row: any, perms: string[]) {
  row.permissions = perms;
  row.view = perms.includes('view');
  row.create = perms.includes('create');
  row.edit = perms.includes('edit');
  row.delete = perms.includes('delete');
  row.publish = perms.includes('publish');
  let params = {
    usernames: [row.username],
    permission_type: perms
  };
  onAddMembers(params, 'noFresh');
}

function removeMember(row: any) {
  let payload = {
    usernames: [row.username],
  };
  ElMessageBox.confirm(
    `确定要移除用户"${row.name}"吗？删除后无法恢复。`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      dangerouslyUseHTMLString: true
    }
  ).then(() => {
    let deleteServiveAPI = selectedGroupType.value === 'agent'
      ? deleteAngenPermissionsApi(selectedGroup.value.id, payload)
      : deleteDatamodelPermissionsApi(selectedGroup.value.id, payload);
    deleteServiveAPI.then(res => {
      const response = res as { success: boolean };
      if (response.success) {
        ElMessage.success('成员已移除');
        getMemberData();
      }
    });
  }).catch(() => {});
}

function onAddMembers(payload: any, type?: any) {
  let serviveAPI = selectedGroupType.value === 'agent'
    ? getAngenPermissionsApi(selectedGroup.value.id, payload)
    : getDatamodelPermissionsApi(selectedGroup.value.id, payload);
  serviveAPI.then((res: any) => {
    ElMessage.success('权限已更新');
    if(!type) {
      getMemberData();
    }
  }).catch(()=>{
    getMemberData();
  });
}

onMounted(() => {
  getGroupListData();
});

function getGroupListData() {
  fresh.value = false
  if (selectedGroupType.value === 'agent') {
    getAgentListApi({ page: 1, pageSize: 1000, workspace_id:spaceCode.value}).then((res) => {
      const response = res as { data: any };
      let curData = response.data.app_list || [];
      if(curData.length) {
        curData.forEach(val => {
          val.name = val.app_name;
          val.id = val.app_code;
        });
        selectedGroup.value = curData[0];
        getMemberData();
      } else {
        members.value = []
        selectedGroup.value = {id:'',name:''}
      }
      groups.value = curData;
      fresh.value = true
    }).catch(()=>{
      fresh.value = true
    });
  } else {
    getSemanticModelListApi({workspace_id:spaceCode.value}).then((res) => {
      const response = res as { data: any };
      if(response.data && response.data.length) {
        groups.value = response.data;
        selectedGroup.value = groups.value[0];
        getMemberData();
      } else {
        groups.value = []
        selectedGroup.value = {id:'',name:''};
      }
      fresh.value = true
    }).catch(()=>{
      fresh.value = true
    });
  }
}

async function getMemberData() {
  memberLoading.value = true
  // members.value = []
  try {
    let serviveAPI = selectedGroupType.value === 'agent'
      ? getAngenMemberApi(selectedGroup.value.id)
      : getDatamodelMemberApi(selectedGroup.value.id);
    const res = await serviveAPI;
    const response = res as { data: any };
    members.value = transformPermissions(response.data);
  } catch (error) {
    console.error('获取成员数据失败:', error);
    ElMessage.error('获取成员数据失败');
  } finally {
    memberLoading.value = false
  }
}

function transformPermissions(arr: any[]) {
  return arr.map(item => {
    const permsObj = (item.permissions || []).reduce((acc: any, perm: string) => {
      acc[perm] = true;
      return acc;
    }, {});
    return {
      ...item,
      ...permsObj,
    };
  });
}
</script>

<style scoped lang="scss">
.resource-authority {
  display: flex;
  height: 100%;
  background: #fff;
  height: calc(100vh - 180px);
  .small-title {
    font-weight: bold;
    font-size: 14px;
  }
}
.group-list {
  width: 220px;
  min-width: 200px;
  height: 100%;
  border-right: 1px solid #f0f0f0;
  padding: 15px;
  .group-select {
    width: 90%;
    margin-bottom: 10px;
  }
  .group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;
    border-radius: 4px;
    margin-bottom: 2px;
    transition: background 0.2s, color 0.2s;
    &.active {
      background: #F4F9FF;
      color: #005EE0;
      font-weight: bold;
    }
    &:hover {
      background: #F4F9FF;
    }
  }
}
.member-table {
  flex: 1;
  padding: 15px;
}
.el-table th,
.el-table td {
  text-align: center;
}
.deleteIcon {
  cursor: pointer;
  font-size:14px;
  color:#4C6F88;
}
</style>
