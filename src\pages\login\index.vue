<script lang="ts" setup>
import type { FormInstance, FormRules } from "element-plus"
import type { LoginRequestData } from "./apis/type"
import ThemeSwitch from "@@/components/ThemeSwitch/index.vue"
import { Key, Loading, Lock, Picture, User, ArrowDown, ArrowLeft } from "@element-plus/icons-vue"
import { useSettingsStore } from "@/pinia/stores/settings"
import { useUserStore } from "@/pinia/stores/user"
import { getCaptchaApi, loginApi } from "./apis"
import { useFocus } from "./composables/useFocus"

const router = useRouter()
const userStore = useUserStore()
const settingsStore = useSettingsStore()
const { isFocus, handleBlur, handleFocus } = useFocus()

const loginFormRef = ref<FormInstance | null>(null)
const loading = ref(false)
const codeUrl = ref("")
const loginType = ref("account")
const currentLang = ref("zh")
const showReset = ref(false)

const loginFormData: LoginRequestData = reactive({
  username: "admin",
  password: "********",
  code: "Sunline"
})

const loginFormRules: FormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 8, max: 16, message: "长度在 8 到 16 个字符", trigger: "blur" }
  ],
  code: [
    { required: true, message: "请输入验证码", trigger: "blur" }
  ]
}

function handleLogin() {
  loginFormRef.value?.validate((valid) => {
    if (!valid) {
      // ElMessage.error("表单校验不通过")
      return
    }
    loading.value = true
    loginApi(loginFormData).then(({ data }: any) => {
      userStore.setToken(data.access_token as unknown as string)
      userStore.setUser(data.user)
      router.push("/")
    }).catch(() => {
      createCode()
      loginFormData.password = ""
    }).finally(() => {
      loading.value = false
    })
  })
}

function createCode() {
  loginFormData.code = "Sunline"
  codeUrl.value = ""
}

function setLang(lang: string) {
  currentLang.value = lang
}

function onFind() {
  showReset.value = true
}
function backToLogin() {
  showReset.value = false
}

// 重置密码表单
const resetFormRef = ref<FormInstance | null>(null)
const resetForm = reactive({ email: '' })
const resetLoading = ref(false)
const resetRules = {
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email' as const, message: '邮箱格式不正确', trigger: 'blur' }
  ]
}
function handleReset() {
  resetFormRef.value?.validate((valid) => {
    if (!valid) return
    resetLoading.value = true
    setTimeout(() => {
      resetLoading.value = false
      ElMessage.success('重置密码邮件已发送')
      showReset.value = false
    }, 1000)
  })
}

createCode()
</script>

<template>
  <div class="login-wrapper">
    <!-- 左侧品牌区 -->
    <div class="login-left">
      <img class="logo" src="./images/sunline-logo.png" alt="Sunline" />
      <div class="slogan">智能问数，你的专属数据智囊</div>
    </div>
    <!-- 右侧内容区 -->
    <div class="login-right" :class="{ 'show-reset': showReset }">
      <div class="lang-switch">
        <el-dropdown>
          <span class="el-dropdown-link">
            {{ currentLang === 'zh' ? '中文' : 'English' }}
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="setLang('zh')">中文</el-dropdown-item>
              <el-dropdown-item @click="setLang('en')">English</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <template v-if="!showReset">
        <div class="login-title">欢迎登录</div>
        <el-tabs v-model="loginType" class="login-tabs">
          <el-tab-pane label="账号登录" name="account">
            <el-form ref="loginFormRef" :model="loginFormData" :rules="loginFormRules" @keyup.enter="handleLogin" class="login-form">
              <el-form-item prop="username">
                <el-input
                  v-model.trim="loginFormData.username"
                  placeholder="请输入用户名"
                  type="text"
                  tabindex="1"
                  :prefix-icon="User"
                  size="large"
                />
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model.trim="loginFormData.password"
                  placeholder="请输入密码"
                  type="password"
                  tabindex="2"
                  :prefix-icon="Lock"
                  size="large"
                  show-password
                  @blur="handleBlur"
                  @focus="handleFocus"
                />
              </el-form-item>
              <el-button :loading="loading" type="primary" size="large" @click.prevent="handleLogin" class="login-btn">
                登 录
              </el-button>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="二维码登录" name="qrcode">
            <div class="qrcode-box">
              <img src="./images/sunline-logo.png" alt="二维码" style="width:120px;opacity:0.7;" />
              <div class="qrcode-tip">请使用企业微信扫码登录</div>
            </div>
          </el-tab-pane>
        </el-tabs>
        <div class="login-actions" @click="onFind">
          <span class="forgot">忘记密码?</span>
          <span class="find">立即找回</span>
        </div>
        <ThemeSwitch v-if="settingsStore.showThemeSwitch" class="theme-switch" />
      </template>
      <template v-else>
        <div class="back-login" @click="backToLogin">
          <el-icon style="vertical-align: middle;"><ArrowLeft /></el-icon>
          <span style="color:#005EE0;font-weight:600;margin-left:4px;cursor:pointer;">返回登录</span>
        </div>
        <div class="reset-title">重置密码</div>
        <div class="reset-desc">请输入您的账户邮箱，接收重置密码邮件。</div>
        <el-form ref="resetFormRef" :model="resetForm" :rules="resetRules" class="reset-form">
          <el-form-item prop="email">
            <el-input v-model.trim="resetForm.email" placeholder="请输入邮箱" :prefix-icon="User" size="large" />
          </el-form-item>
          <el-button :loading="resetLoading" type="primary" size="large" @click.prevent="handleReset" class="login-btn">
            发送重置密码邮件
          </el-button>
        </el-form>
      </template>
      <div class="login-foot">
        <el-divider content-position="center">版权所有 ©深圳市长亮科技股份有限公司</el-divider>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-wrapper {
  display: flex;
  height: 100vh;
  width: 100%;
  position: relative;
  background-image: url(./images/bg.png);
  background-position: left top;
  background-repeat: no-repeat;
  background-size: auto 100%;
  .login-left {
    flex: 0.6;
    padding:60px 0 0 60px;
    .logo {
      margin-bottom: 60px;
    }
    .slogan {
      font-size: 28px;
      color: #005EE0;
      margin-bottom: 40px;
      font-weight: 800;
    }
  }
  .login-right {
    position: relative;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #fff;
    position: relative;
    &.show-reset {
      .back-login,.reset-title,.reset-desc {
        width: 360px;
      }
      // align-items: flex-start;
      // padding-left: 150px;
      // box-sizing: border-box;
    }
    .lang-switch {
      position: absolute;
      top: 32px;
      right: 48px;
      font-size: 16px;
      color: #333;
      z-index: 2;
    }
    .login-title {
      width: 360px;
      font-size: 30px;
      font-weight: bold;
      margin-bottom: 35px;
      color: #222;
    }
    .login-tabs {
      width: 360px;
      margin-bottom: 0;
      :deep(.el-tabs__header) {
        margin-bottom: 26px;
      }
    }
    .login-form {
      width: 360px;
      margin-top: 0;
      .el-form-item {
        margin-bottom: 26px;
      }
    }
    .login-btn {
      width: 100%;
      margin-top: 8px;
    }
    .qrcode-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 220px;
      .qrcode-tip {
        margin-top: 16px;
        color: #888;
        font-size: 15px;
      }
    }
    .login-actions {
      margin: 18px 0 0 0;
      width: 360px;
      display: flex;
      justify-content: flex-start;
      gap: 10px;
      .forgot, .find {
        cursor: pointer;
      }
      .find {
        color: #005EE0;
      }
    }
    .theme-switch {
      position: absolute;
      top: 24px;
      left: 32px;
      z-index: 2;
    }
    .back-login {
      margin-bottom: 32px;
      font-size: 15px;
      cursor: pointer;
      color: #005EE0;
      display: flex;
      align-items: center;
      font-weight: 600;
    }
    .reset-title {
      font-size: 28px;
      font-weight: bold;
      margin-bottom: 18px;
      margin-top: 12px;
      color: #222;
    }
    .reset-desc {
      color: #999;
      margin-bottom: 45px;
    }
    .reset-form {
      width: 360px;
    }
    .login-foot {
        position: fixed;
        bottom: 20px;
        right: 20px;
        text-align: center;
        font-size: 13px;
        color: #ffffff;
        z-index: 8;
        width: 50%;
        :deep(.el-divider__text) {
            color: #999;
        }
    }
  }
}
</style>
