<template>
  <el-popover ref="btnPopover" trigger="click" @show="onShow" @hide="onHide" width="500px">
    <div class="content-container">
      <div class="tree-container" ref="treeContainer">
        <div class="search-input">
          <el-input
            clearable
            size="small"
            placeholder="请输入内容"
            v-model="keyword"
            @input="onSearch"
          >
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="debouncedSearch.flush"
            ></el-button>
          </el-input>
        </div>
        <el-tree
          v-if="!firstOpen"
          ref="userTree"
          class="tree-list"
          :node-key="nodeKey"
          :lazy="false"
          :data="treeData"
          :default-expand-all="true"
          :props="{
            label: 'name',
            children: 'children',
            disabled: isDisabled,
            isLeaf: isLeaf,
          }"
          show-checkbox
          @check-change="onCheckChange"
          @check="onCheck"
        />
      </div>
      <div class="selected-panel">
        <div class="list mone-scrollbar">
          <div class="select-item" v-for="s in showList" :key="s[nodeKey]">
            <span class="name">{{ s.name }}</span>
            <el-icon class="deleteIcon"  @click="onDel(s)"><CircleCloseFilled /></el-icon>
          </div>
        </div>
        <div class="tools">
          <span class="num">已选 {{ showList.length }}项目</span>
          <el-button type="text" @click="onClear">清除已选</el-button>
        </div>
      </div>
    </div>
    <template #reference>
      <slot name="reference">
        <el-button plain type="primary" size="small">+ 添加</el-button>
      </slot>
    </template>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { uniqBy, debounce } from 'lodash';
import { getUseTreeApi, getWorkspaceUseTreeApi } from "@/common/apis/workspace"
import { useRoute } from "vue-router";
import { tr } from 'element-plus/es/locale';
const route = useRoute();
interface TreeNode {
  user_id: string | number;
  name: string;
  type?: string;
  children?: TreeNode[];
  [key: string]: any;
}

const props = defineProps<{
  value: TreeNode[];
  isMultiple?: boolean;
  nodeKey?: string;
  fromType?:string,  // authorityManage 代表来自权限管理，获取空间下的成员树
}>();

const emit = defineEmits<{
  (e: 'update:value', value: TreeNode[]): void;
  (e: 'change', value: TreeNode[]): void;
  (e: 'addRemove', isAdd: boolean, objs: TreeNode[]): void;
  (e: 'hideEvent'): void;
}>();

const keyword = ref('');
const showList = ref<TreeNode[]>(props.value ? [...props.value] : []);
const treeData = ref<TreeNode[]>([]);
const originalTreeData = ref<TreeNode[]>([]); // 添加原始数据存储
const lazyLoaded = ref(false);
const lazy = ref(false);
const firstOpen = ref(true);

const btnPopover = ref<any>(null);
const userTree = ref<any>(null);
const treeContainer = ref<any>(null);

const nodeKey = props.nodeKey ?? 'id';
const isMultiple = props.isMultiple ?? true;

// 创建防抖的搜索函数
const debouncedSearch = debounce(() => {
  if (!keyword.value.trim()) {
    // 如果搜索关键词为空，显示所有数据
    treeData.value = [...originalTreeData.value];
    updatePosi();
    return;
  }

  // 前端过滤逻辑
  const filteredData = filterTreeData(originalTreeData.value, keyword.value.trim());
  treeData.value = filteredData;
  updatePosi();
}, 300);

watch(
  () => props.value,
  (val = []) => {
    showList.value = [...val];
    setCheckedKeys();
  },
  { immediate: true, deep: true }
);

onMounted(() => {
  resizeObserver();
});

onUnmounted(() => {
  // 取消防抖函数，避免内存泄漏
  debouncedSearch.cancel();
});
function getuserList(){
  let serviceAPi =  props.fromType === 'authorityManage' ? getWorkspaceUseTreeApi({workspace_id: route.query.spaceCode}) : getUseTreeApi({})

  serviceAPi.then((res) => {
    const response = res as { data: TreeNode[] }
    const data = response?.data || []
    data.forEach(item => {
      item.name = item.department
      item.children = item.users
    });
    originalTreeData.value = data; // 保存原始数据
    treeData.value = data
  })
}
function onShow() {
  if (firstOpen.value) {
    lazy.value = true;
    firstOpen.value = false;
    setCheckedKeys();
  } else if (!lazyLoaded.value) {
    onSearch();
  }
  getuserList()
}

function getTreeNode(nodeId: string | number = ''): any {
  const treeRef = userTree.value;
  if (!treeRef) return null;
  return treeRef.getNode(nodeId);
}

// function lazyLoadData(node: any, resolve: (data: TreeNode[]) => void) {
//   // if (node.level === 0 && defaultData && defaultData.length) {
//   //     return resolve([...defaultData]);
//   // }
//   const { data = {} } = node;
//   getUseTreeApi().then((res) => {
//     const response = res as { data: TreeNode[] }
//     const data = response?.data || []
//     lazyLoaded.value = true;
//     data.forEach(item => {
//       // if (!item[nodeKey]) {
//       //   item[nodeKey] = item.id;
//       // }
//     });
//     resolve(treeData);
//   });
//   // mock for demo:
//   setTimeout(() => {
//     lazyLoaded.value = true;
//     const mockData: TreeNode[] = [
//       { id: `${data.id || '0'}-1`, name: '子节点1', type: 'user' },
//       { id: `${data.id || '0'}-2`, name: '子节点2', type: 'org' },
//     ];
//     mockData.forEach(item => {
//       if (!item[nodeKey]) {
//         item[nodeKey] = item.id;
//       }
//     });
//     resolve(mockData);
//   }, 500);
// }

function isDisabled(data: TreeNode, node: any) {
  const { user_id ,department} = data;
  const { childNodes = [] } = node;
  return !childNodes.length && department;
}

function isLeaf(data: TreeNode, node: any) {
  const { type } = data;
  return type === 'user';
}

function getSelected() {
  return JSON.parse(JSON.stringify(showList.value));
}

function onCheckChange(obj: TreeNode, isChecked: boolean) {
  const { type } = obj;
  // if (type !== 'user') {
  //   return;
  // }
  if (isChecked) {
    if (isMultiple) {
      if(obj.user_id){
        showList.value.push(obj);
      } else {
        showList.value.push(...obj.children);
      }
    } else {
      if(obj.user_id){
        showList.value = [obj];
      } else {
        showList.value = [...obj.children];
      }
    }
  } else {
    if(obj.user_id){
      showList.value = showList.value.filter((item) => item[nodeKey] !== obj[nodeKey]);
    } else {
      // showList.value = showList.value.filter((item) => item[nodeKey] !== obj[nodeKey]);
    }
  }
  showList.value = uniqBy(showList.value, nodeKey);
  emit('update:value', showList.value);
  emit('change', showList.value);
}

function onCheck(obj: TreeNode, data: any) {
  const {
    checkedKeys = [],
    checkedNodes = [],
    halfCheckedKeys = [],
    halfCheckedNodes = [],
  } = data;
  addRemove(checkedKeys.includes(obj[nodeKey]), [obj]);
}

function onSearch() {
  debouncedSearch();
}

// 添加前端过滤函数
function filterTreeData(data: TreeNode[], keyword: string): TreeNode[] {
  return data.map(item => {
    // 检查部门名称是否匹配
    const departmentMatch = item.department && item.department.toLowerCase().includes(keyword.toLowerCase());

    // 过滤用户列表
    const filteredUsers = item.users ? item.users.filter(user => {
      const userNameMatch = user.name && user.name.toLowerCase().includes(keyword.toLowerCase());
      const userIdMatch = user.user_id && user.user_id.toString().includes(keyword);
      return userNameMatch || userIdMatch;
    }) : [];

    // 如果部门匹配或有匹配的用户，返回过滤后的数据
    if (departmentMatch || filteredUsers.length > 0) {
      return {
        ...item,
        name: item.department,
        children: filteredUsers
      };
    }

    return null;
  }).filter(Boolean) as TreeNode[];
}

function updatePosi() {
  nextTick(() => {
    btnPopover.value?.updatePopper && btnPopover.value.updatePopper();
  });
}

function onHide() {
  keyword.value = '';
  lazyLoaded.value = false;
  emit('hideEvent');
}

function onClear() {
  emit('update:value', []);
  emit('change', []);
  addRemove(false, []);
}

function onDel(item: TreeNode) {
  const idx = showList.value.findIndex((s) => s[nodeKey] === item[nodeKey]);
  if (idx !== -1) {
    showList.value.splice(idx, 1);
    emit('update:value', [...showList.value]);
    emit('change', [...showList.value]);
    addRemove(false, [item]);
    nextTick(() => {
      userTree.value?.setCheckedKeys && userTree.value.setCheckedKeys([item[nodeKey]], false);
    });
  }
}

function addRemove(isAdd: boolean, objs: TreeNode[] = []) {
  emit('addRemove', isAdd, objs);
}

function setCheckedKeys() {
  nextTick(() => {
    if (!firstOpen.value) {
      const treeRef = userTree.value;
      if (treeRef) {
        const ids = showList.value.map((v) => v[nodeKey]);
        treeRef.setCheckedKeys(ids);
        updatePosi();
      }
    }
  });
}

function resizeObserver() {
  const windowH = window.innerHeight;
  nextTick(() => {
    const popper = btnPopover.value?.$refs?.popper;
    if (!popper) return;
    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const width = entry.contentRect.width;
        const height = entry.contentRect.height;
        const popperH = height + 26;
        const poperTop = popper.offsetTop;
        if (poperTop + popperH > windowH) {
          popper.style.top = `${windowH - popperH}px`;
        }
      }
    });
    resizeObserver.observe(popper);
  });
}
</script>

<style lang="scss" scoped>
.content-container {
  display: flex;
  min-height: 200px;
  min-width: 400px;
  .selected-panel {
    width: 200px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    border: solid 1px #eff0f2;
    box-sizing: border-box;
    overflow: auto;
  }
  .select-item {
    display: flex;
    align-items: center;
    padding: 4px 6px;
    margin-bottom: 6px;
    background: #f3f3f3;
    border-radius: 3px;
    .deleteIcon {
    cursor: pointer;
    font-size:12px;
    color:#999;
    &:hover {
      color: #005EE0;
      font-weight: bold;
    }
  }
  }
  .name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .list {
    flex: 1;
    padding: 10px;
    overflow: auto;
  }
  .tools {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 15px;
    background: #fcfcfc;
    border-top: solid 1px #eff0f2;
  }
  .num {
    line-height: 14px;
    color: #999;
  }
  .tree-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    max-width: 600px;
    max-height: 400px;
    border-radius: 3px;
    overflow: auto;
    border: solid 1px #eff0f2;
    border-right: 0;
  }
  .tree-list {
    flex: 1;
    overflow: auto;
  }
  .search-input {
    width: 100%;
    padding: 3px 0;
    box-sizing: border-box;
    border-bottom: solid 1px #eff0f2;
    background: #fff;
    :deep(.el-input__inner ){
      border: 0;
    }
    :deep(.el-input-group__append ){
      background: #fff;
      border: 0;
      padding: 0 12px;
    }
    :deep(.el-input__suffix .el-input__validateIcon) {
      display: none;
    }
  }
}
</style>
