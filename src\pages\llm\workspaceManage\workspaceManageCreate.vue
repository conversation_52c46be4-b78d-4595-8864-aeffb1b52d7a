<template>
  <div class="workspace-detail-container">
    <!-- 页面头部 -->
    <div class="detail-header">
      <div class="header-left">
        <span class="back-btn" @click="handleBack">
          <i class="ChatData-fanhui iconfont"></i>
        </span>
        <h2 v-if="!isEdit">新增空间</h2>
        <el-dropdown @visible-change="handleWorkspaceSelect" @command="handleWorkspaceSelect" v-else>
          <span class="workspace-selector">
            {{ currentWorkspace?.workspace_name }}
            <el-icon><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item
                v-for="workspace in workspaces"
                :key="workspace.id"
                :command="workspace.id"
              >
                {{ workspace.workspace_name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <div class="header-right">
          <template v-if="!isEdit">
            <el-button type="primary" @click="handleSave">保存</el-button>
            <el-button @click="handleCancelEdit">取消</el-button>
          </template>
      </div>
    </div>

    <!-- 基本信息 -->
    <div class="basic-info-section">
      <h3>基本信息</h3>
      <el-form :model="workspaceForm" :rules="Rules" :label-position="isEdit ? 'left' : 'top'"  label-width="120px" class="workspace-form" ref="formRef" :disabled="!GetPermission(['workspace:update'])">
        <el-form-item label="工作空间名称" prop="workspace_name">
          <el-input
            v-model="workspaceForm.workspace_name"
            placeholder="工作空间名称"
          />
        </el-form-item>
        <el-form-item label="管理员" v-if="!isEdit">
          <ListShow
            labelField="name"
            :max="5"
            :dataList="workspaceForm.admin_user_ids"
          >
            <template #item="{ content }">
              <div class="user-tag">
                <span>{{ content.name }}</span>
                <el-icon  @click=" () => { onUserDel(content.user_id)} "><Close /></el-icon>
              </div>
            </template>
          </ListShow>
          <BusmMemberOrgBtn
            v-bind="$attrs"
            :isMultiple="true"
            :nodeKey="'user_id'"
            v-model:value="workspaceForm.admin_user_ids"
            @change="(list) => changeUser(list)"
          ></BusmMemberOrgBtn>
        </el-form-item>
        <el-form-item label="描述">
          <el-input
            v-model="workspaceForm.description"
            type="textarea"
            :rows="4"
            placeholder="定义描述，以便快速理解"
          />
        </el-form-item>
      </el-form>
      <el-button
        style="margin-left: 120px;"
        v-if="isEdit"
        v-show="GetPermission(['workspace:update'])"
        type="primary"
        :disabled="!hasChanges"
        @click="handleSave"
      >
        保存修改
      </el-button>
    </div>
    <!-- 空间成员 -->
    <div v-if="isEdit">
      <WorkspaceMembers  fromType="Workspace"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, FormInstance ,FormRules} from 'element-plus'
import ListShow from './component/ListShow.vue'
import BusmMemberOrgBtn from './component/BusmMemberOrgBtn.vue'
import { postSapceLApi,getSapceListApi,getSapceByIdApi,getSapceMemberApi,putSapceLApi } from "@/common/apis/workspace"
import {
  ArrowLeft,
  ArrowDown,
  Search,
  Plus,
  Setting
} from '@element-plus/icons-vue'
import WorkspaceMembers from '@/pages/llm/authorityManage/UserManage/WorkspaceMembers.vue'

interface Workspace {
  id: number
  workspace_name: string
  description?: string
}

interface Member {
  id: number
  name: string
  username: string
  role_name: string
  joinTime: string
}

interface User {
  id: number
  name: string
}

const route = useRoute()
const router = useRouter()
// 表单引用
const formRef = ref<FormInstance>()
const Rules = ref<FormRules>({
  workspace_name: [
    { required: true, message: "请输入空间名称", trigger: "blur" },
  ],
});

// 响应式数据
const isEdit = ref(false)
// 工作空间数据
const workspaces = ref<Workspace[]>([])

const currentWorkspace = ref<Workspace | null>(null)

const workspaceForm = ref({
  workspace_name: '',
  admin_user_ids: [],
  description: ''
})

// 保存原始数据用于比较
const originalData = ref({
  workspace_name: '',
  admin_user_ids: [],
  description: ''
})

// 计算是否有修改
const hasChanges = computed(() => {
  if (!isEdit.value) {
    // 新增模式下，只要名称不为空就激活按钮
    return workspaceForm.value.workspace_name.trim() !== '';
  } else {
    // 编辑模式下，比较当前值和原始值
    return workspaceForm.value.workspace_name !== originalData.value.workspace_name ||
           workspaceForm.value.description !== originalData.value.description;
  }
});

// 方法
const handleBack = () => {
  router.push({
    path: 'workspaceManage',
    query: {
      spaceCode:route.query.spaceCode || '',
    }
  })
}

const handleWorkspaceSelect = (workspaceId: number) => {
  const workspace = workspaces.value.length && workspaces.value.find(w => w.id === workspaceId)
  if (workspace) {
    currentWorkspace.value = workspace
    workspaceForm.value.workspace_name = workspace.workspace_name
    workspaceForm.value.description = workspace.description || ''
    // 保存原始数据
    originalData.value = {
      workspace_name: workspace.workspace_name,
      admin_user_ids: [],
      description: workspace.description || ''
    }
    // 修改路由query的spaceCode
    router.replace({
      query: {
        ...route.query,
        spaceCode: workspace.id
      }
    })
  }
}

const onUserDel = (userId: number) => {
  workspaceForm.value.admin_user_ids = workspaceForm.value.admin_user_ids.filter(member => member.user_id !== userId)
}
const changeUser =  (userList: any[]) => {
  workspaceForm.value.admin_user_ids = userList
}

const handleSave = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid, fields) => {
    if (valid) {
      try {
        const params = {
          ...workspaceForm.value,
          admin_user_ids: workspaceForm.value.admin_user_ids.map(item => item.user_id)
        }
        if (isEdit.value && currentWorkspace.value) {
          // 更新
          await putSapceLApi(currentWorkspace.value.id, params)
          ElMessage.success('空间更新成功')
        } else {
          // 添加
          await postSapceLApi( params)
          ElMessage.success('空间添加成功')
        }
        // 返回列表页
        handleBack()
      } catch (error) {
        // ElMessage.error(isEdit.value ? '更新空间失败' : '添加空间失败')
        console.error(error)
      }
    }
  })
}

const handleCancelEdit = () => {
  handleBack()
}

// 删除与成员管理相关的多余响应式变量、方法和计算属性
// 将workspaceId定义为响应式ID，请定义为 ref 并赋值
const workspaceId = ref(parseInt(route.query.spaceCode as string))

onMounted(() => {
   // 从查询参数判断是新增还是编辑
  const spaceCode = route.query.spaceCode
  const mode = route.query.mode
  if (mode === 'edit' && spaceCode) {
    getSapceList()
    isEdit.value = true
    // getSapceByIdData() // This line was removed
  }
})
function getSapceList() {
  getSapceListApi().then((res) => {
    const response = res as { data: Workspace[] }
    workspaces.value = response?.data || []
    const workspace = workspaces.value.find(w => w.id ===  workspaceId.value)
    if (workspace) {
      currentWorkspace.value = workspace
      workspaceForm.value.workspace_name = workspace.workspace_name
      workspaceForm.value.description = workspace.description || ''
      // 保存原始数据
      originalData.value = {
        workspace_name: workspace.workspace_name,
        admin_user_ids: [],
        description: workspace.description || ''
      }
    }
  })
}
function getSapceByIdData() {
  getSapceByIdApi(workspaceId.value).then((res) => {
    const response = res as { data: Workspace }
    currentWorkspace.value = response?.data || ({} as Workspace)
  })
}
</script>

<style scoped>
.workspace-detail-container {
  background-color: #fff;
  min-height: 100vh;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  line-height: 48px;
  margin-bottom: 20px;
  padding: 0 20px;
  border-bottom: 1px solid #e5e7eb;
  h2 {
    margin:0;
  }
  .back-btn {
    margin-right: 10px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f1f7ff;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    color: #005EE0;
    &:hover {
      background-color: #005EE0;
      color: #fff;
    }
  }
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.workspace-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  color: #303133;
}

.basic-info-section {
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
}

.basic-info-section h3 {
  padding-left: 15px;
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  position: relative;
  &::before {
    position: absolute;
    content: "";
    left: 0;
    top: 2px;
    width: 3px;
    height: 16px;
    background: #005EE0;
  }
}

.workspace-form {
  max-width: 600px;
}
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
.user-tag{
  display: flex;
  align-items: center;
  padding: 4px 6px;
  border-radius: 3px;
  color: #555;
  background: #F3F3F3;
  .el-icon-close{
    width: 20px;
    height: 20px;
    line-height: 20px;
    color: #ccc;
    text-align: center;
    &:hover{
      color: #005EE0;
      font-weight: bold;
      cursor: pointer;
    }
  }
}
</style>
