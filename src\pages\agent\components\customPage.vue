<script setup lang="ts">
import { Back, Right } from "@element-plus/icons-vue"
// 确保属性和事件监听器能够正确继承
defineOptions({
  inheritAttrs: false
})

const props = defineProps({
  total: {
    type: Number,
    default: 0
  },
  pageSizes: {
    type: Array as () => number[],
    default: () => [10, 20, 30, 40]
  },
  showPageSize: {
    type: Boolean,
    default: false
  }
})

const emits = defineEmits(["size-change", "current-change"])

// page 实例
const pageRef = ref()

// 显示的文本
const showText = computed(() => {
  return `${currentPage.value}/${maxPage.value}`
})
// 当前页数
const currentPage = defineModel<number>("currentPage")
// 当前条数
const pageSize = defineModel<number>("pageSize")
// 最大页数
const maxPage = computed(() => {
  return Math.ceil(props.total / (pageSize.value || props.pageSizes[0])) || 1
})

// ----------------------

function handleSub() {
  currentPage.value > 1 && currentPage.value--
  emits("current-change")
}

function handleAdd() {
  currentPage.value < maxPage.value && currentPage.value++
  emits("current-change")
}

function sizeChange (){
  currentPage.value = 1;
  emits('size-change')
}
</script>

<template>
  <div class="custom-page">
    <div class="handle-toggle">
      <el-button @click="handleSub" :disabled="currentPage === 1">
        <template #icon>
          <el-icon :color="currentPage === 1 ? '#8099aa' : '#005ee0'" size="12">
            <Back />
          </el-icon>
        </template>
      </el-button>
      <span>{{ showText }}</span>
      <el-button @click="handleAdd" :disabled="currentPage === maxPage">
        <template #icon>
          <el-icon :color="currentPage === 1 ? '#8099aa' : '#005ee0'" size="12">
            <Right />
          </el-icon>
        </template>
      </el-button>
    </div>
    <el-pagination
      size="small"
      @current-change="emits('current-change')"
      v-bind="$attrs"
      layout="prev, pager, next"
      ref="pageRef"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="props.pageSizes"
    />
    <el-segmented
      v-model="pageSize"
      v-if="props.showPageSize"
      :options="pageSizes"
      size="small"
      @change="sizeChange"
      class="custom-segmented"
    />
  </div>
</template>

<style lang="scss" scoped>
.custom-page {
  display: flex;
  justify-content: space-between;
  // grid-template-columns: 1fr 1fr;
  align-items: center;
  gap: 10px;
  .handle-toggle {
    width: max-content;
    background: #f2f2f2;
    border-radius: 6px;
    height: 28px;
    box-sizing: border-box;
    padding: 2px;
    display: flex;
    align-items: center;
    gap: 8px;
    color: #262626;
    :deep(.el-button) {
      width: 24px;
      height: 24px;
      padding: 8px 10px;
      //   svg path {
      //     fill: #005ee0;
      //   }
      //   &.disabled {
      //     svg path {
      //       fill: #8099aa !important;
      //     }
      //   }
    }
  }
  :deep(.el-pagination) {
    display: flex;
    justify-content: end;
    .btn-prev,
    .btn-next {
      display: none;
    }
    .el-pager {
      .number {
        color: #262626;
      }
      .is-active.number {
        background: #f2f2f2;
        border-radius: 6px;
      }
    }
  }
}
.custom-segmented {
  --el-segmented-item-selected-color: var(--el-text-color-primary);
  --el-segmented-item-selected-bg-color: #ffffff !important;
  // --el-segmented-item-selected-color: #262626 !important;
  // --el-border-radius-base: 16px;
  :deep(.el-segmented__group) {
    background: #f2f2f2;
  }
}
</style>
