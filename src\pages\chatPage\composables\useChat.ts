import type { Chat<PERSON>ara<PERSON> } from "@@/apis/chat/type"
import { ref } from "vue"

interface UseChatProps {
  queryAgentURL?: string
  app_code?: string
}

export function useChat({ queryAgentURL = "/api/v1/chat/completions", app_code }: UseChatProps = {}) {
  const ctrl = ref<AbortController>(new AbortController())

  const chat = async ({ data, chatId,queryAgentURL, onMessage, onDone, onError, ctrl: abortCtrl }: ChatParams) => {
    if (abortCtrl) {
      ctrl.value = abortCtrl
    }

    if (!data?.user_input && !data?.doc_id) {
      console.warn("No context provided")
      return
    }

    const params = {
      ...data,
      conv_uid: chatId,
      // app_code
    }
    try {
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || ""}${queryAgentURL}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "User-Id": localStorage.getItem("userId") || ""
        },
        body: JSON.stringify(params),
        signal: ctrl.value ? ctrl.value.signal : null
      })
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error("No response body reader available")
      }
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          onDone?.()
          break
        }

        const chunk = decoder.decode(value, { stream: true })
        const lines = chunk.split("\n")

        for (const line of lines) {
          if (line.startsWith("data:")) {
            const data = line.slice(5).trim()
            if (data === "[DONE]") {
              onDone?.()
              return
            }

            if (data.startsWith("[ERROR]")) {
              onError?.(data.replace("[ERROR]", ""))
              return
            }

            try {
              const parsed = JSON.parse(data)
              if (typeof parsed === "string") {
                onMessage?.(parsed)
              } else {
                onMessage?.(parsed)
                onDone?.()
              }
            } catch {
              // 如果不是JSON，直接作为字符串处理
              if (data) {
                onMessage?.(data.replaceAll("\\n", "\n"))
              }
            }
          }
        }
      }
    } catch (err) {
      if (ctrl.value) {
        ctrl.value.abort()
      }
      onError?.("Sorry, We meet some error, please try again later.", err as Error)
    }
  }

  return {
    chat,
    ctrl: ctrl.value
  }
} 