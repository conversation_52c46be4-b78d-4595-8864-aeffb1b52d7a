<template>
  <div class="table-manage-container">
    <!-- 头部导航 -->
    <div class="table-manage-header">
      <div class="header-left">
        <span class="back-btn" @click="handleBack">
          <i class="ChatData-fanhui iconfont"></i>
        </span>
        <h2>表信息</h2>
      </div>
    </div>

    <div class="table-manage-content">
      <!-- 左侧表列表 -->
      <div class="table-list-panel">
        <div class="panel-header">
          <h3>表信息列表 ({{ tableList.length }})</h3>
        </div>
        <div class="search-box">
          <el-input
            v-model="searchKeyword"
            placeholder="输入关键字"
            prefix-icon="Search"
            class="search-input"
          />
        </div>
        <div class="table-list">
          <div
            v-for="(table, index) in filteredTableList"
            :key="index"
            :class="['table-item', { active: selectedTableIndex === index }]"
            @click="selectTable(index, table)"
          >
            <div class="table-name">{{ table.name }}</div>
            <div class="table-description">{{ table.description || '未设置表说明'}}</div>
          </div>
        </div>
      </div>

      <!-- 右侧字段配置 -->
      <div class="field-config-panel">
        <div v-if="selectedTable" class="config-content">
          <!-- 表说明区域 -->
          <div class="table-description-section">
            <div class="description-header">
              <span class="current-table-name">{{ selectedTable.name }}</span>
              <span class="modified-time">最后修改：{{ selectedTable.modifiedTime || '' }}</span>
            </div>
                          <div class="table-description-content">
                <div
                  v-if="!isEditingDescription"
                  class="description-display"
                  @dblclick="startEditDescription"
                >
                  <span class="description-label">表说明：</span>
                  <span class="description-text">{{ selectedTable.description || '' }}</span>
                </div>
                <div v-else class="description-edit">
                  <span class="description-label">表说明：</span>
                  <el-input
                    ref="descriptionInputRef"
                    v-model="editingDescriptionText"
                    type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    @blur="saveDescription"
                    @keydown.enter.prevent="saveDescription"
                    class="description-textarea"
                  />
                </div>
              </div>
          </div>

          <!-- 搜索和操作区域 -->
          <div class="field-search-section">
            <div class="search-left">
              <el-input
                v-model="fieldSearchKeyword"
                placeholder="输入关键字"
                prefix-icon="Search"
                class="field-search-input"
              />
            </div>
            <div class="search-right">
              <el-button @click="handleBatchEdit" :disabled="selectedFields.length === 0">
                批量操作
              </el-button>
              <el-button type="primary" @click="handleSave" :loading="loading">保存</el-button>
            </div>
          </div>

          <!-- 字段表格 -->
          <div class="field-table-section">
            <el-table
              ref="fieldTableRef"
              :data="filteredFieldList"
              @selection-change="handleSelectionChange"
              class="field-table"
            >
              <el-table-column type="selection" width="55" />
              <el-table-column prop="originalName" label="原名" width="120" />
              <el-table-column prop="aliasName" label="别名" width="120">
                <template #default="{ row }">
                  <el-input v-model="row.aliasName" size="small" />
                </template>
              </el-table-column>
              <el-table-column prop="fieldType" label="字段类型" width="140">
                <template #default="{ row }">
                  <el-select v-model="row.fieldType" size="small" class="full-width">
                    <el-option label="转换为整数" value="integer" />
                    <el-option label="ABC" value="string" />
                    <el-option label="日期" value="date" />
                    <el-option label="浮点数" value="float" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="fieldAttribute" label="字段属性" width="120">
                <template #default="{ row }">
                  <el-select v-model="row.fieldAttribute" size="small" class="full-width">
                    <el-option label="度量" value="measure" />
                    <el-option label="维度" value="dimension" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column prop="fieldDescription" label="字段说明" width="150">
                <template #default="{ row }">
                  <el-input
                    v-model="row.fieldDescription"
                    placeholder="请输入字段说明"
                    size="small"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="comment" label="注释" width="120" />
              <el-table-column prop="ignore" label="忽略" width="120">
                <template #default="{ row }">
                  <el-select v-model="row.ignore" size="small" class="full-width">
                    <el-option label="是" :value="true" />
                    <el-option label="否" :value="false" />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <a
                    v-if="row.fieldAttribute === 'dimension'"
                    class="enum-btn"
                    @click="handleFieldEnum(row)"
                  >
                    字段枚举
                  </a>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div v-else class="no-table-selected">
          <p>请在左侧选择一个表进行配置</p>
        </div>
      </div>
    </div>

    <!-- 批量操作弹窗 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量操作"
      width="500px"
      :show-close="true"
    >
      <div class="batch-operation-content">
        <p class="batch-tip">
          把选择的 <span class="highlight">{{ selectedFields.length }}</span> 条数据更改为：
        </p>

        <el-form :model="batchForm" label-width="80px" class="batch-form">
          <el-form-item label="别名">
            <div class="batch-option">
              <el-button
                size="small"
                @click="batchForm.aliasAction = 'copyFromComment'"
                :type="batchForm.aliasAction === 'copyFromComment' ? 'primary' : ''"
              >
                将别名修改为注释内容
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="字段类型">
            <el-select v-model="batchForm.fieldType" placeholder="数据类型" class="full-width">
              <el-option label="转换为整数" value="integer" />
              <el-option label="ABC" value="string" />
              <el-option label="日期" value="date" />
              <el-option label="浮点数" value="float" />
            </el-select>
          </el-form-item>

          <el-form-item label="字段属性">
            <el-select v-model="batchForm.fieldAttribute" placeholder="选择字段属性" class="full-width">
              <el-option label="度量" value="measure" />
              <el-option label="维度" value="dimension" />
            </el-select>
          </el-form-item>

          <el-form-item label="字段说明">
            <div class="batch-option">
              <el-button
                size="small"
                @click="batchForm.descriptionAction = 'copyFromComment'"
                :type="batchForm.descriptionAction === 'copyFromComment' ? 'primary' : ''"
              >
                将字段说明修改为注释内容
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="忽略">
            <el-select v-model="batchForm.ignore" placeholder="选择是否忽略" class="full-width">
              <el-option label="是" :value="true" />
              <el-option label="否" :value="false" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyBatchOperation">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字段枚举管理弹窗 -->
    <el-dialog
      v-model="enumDialogVisible"
      title="字段枚举管理"
      width="600px"
      :show-close="true"
    >
      <div class="enum-management-content">
        <div class="current-field-info">
          <span class="field-name">当前字段：{{ currentField?.originalName || '' }}</span>
        </div>

        <div class="enum-list-section">
          <div class="enum-header">
            <span class="enum-title">枚举列表</span>
            <el-button size="small" type="primary" @click="addEnumItem">
              <el-icon><Plus /></el-icon>
              新增枚举
            </el-button>
          </div>

          <div class="enum-items">
            <div
              v-for="(item, index) in currentFieldEnumList"
              :key="index"
              class="enum-item"
            >
              <div class="enum-item-content">
                <div class="enum-input-group">
                  <label>键值：</label>
                  <el-input
                    v-model="item.key"
                    size="small"
                    placeholder="请输入键值"
                    class="enum-input"
                  />
                </div>
                <div class="enum-input-group">
                  <label>显示值：</label>
                  <el-input
                    v-model="item.value"
                    size="small"
                    placeholder="请输入显示值"
                    class="enum-input"
                  />
                </div>
                <div class="enum-actions">
                  <el-button
                    size="small"
                    type="danger"
                    @click="removeEnumItem(index)"
                    :disabled="currentFieldEnumList.length <= 1"
                  >
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="enumDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveFieldEnum">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { getDatasourceTableListApi, getDatasourceTableFieldListApi, putDatasourceTableApi, putDatasourceTableFieldApi } from "@/common/apis/llm"

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const searchKeyword = ref('')
const fieldSearchKeyword = ref('')
const selectedTableIndex = ref(-1)
const selectedTable = ref<any>(null)
const selectedFields = ref<any[]>([])
const batchDialogVisible = ref(false)
const hasValidationError = ref(true)
const isEditingDescription = ref(false)
const editingDescriptionText = ref('')
const loading = ref(false)
const tableLoading = ref(false)

// 字段枚举相关
const enumDialogVisible = ref(false)
const currentField = ref<any>(null)
const currentFieldEnumList = ref<{ key: string, value: string }[]>([])

// 表格引用
const fieldTableRef = ref()
const descriptionInputRef = ref()

// 表列表数据（从 API 获取）
const tableList = ref<any[]>([])

// 字段列表数据（从 API 获取）
const fieldListMap = ref<{ [key: number]: any[] }>({})

// 批量操作表单
const batchForm = reactive({
  aliasAction: '',
  fieldType: '',
  fieldAttribute: '',
  descriptionAction: '',
  ignore: null as boolean | null
})

// 计算属性
const filteredTableList = computed(() => {
  if (!searchKeyword.value) return tableList.value
  return tableList.value.filter(table =>
    table.name?.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
    table.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
  )
})

const filteredFieldList = computed(() => {
  const fields = fieldListMap.value[selectedTableIndex.value] || []
  if (!fieldSearchKeyword.value) return fields
  return fields.filter(field =>
    field.originalName?.toLowerCase().includes(fieldSearchKeyword.value.toLowerCase()) ||
    field.aliasName?.toLowerCase().includes(fieldSearchKeyword.value.toLowerCase())
  )
})

// API 调用方法
const loadTableList = async () => {
  const datasourceId = route.query.id
  if (!datasourceId) {
    ElMessage.error('数据源 ID 不能为空')
    return
  }

  loading.value = true
  try {
    const response = await getDatasourceTableListApi(Number(datasourceId))
    if (response && (response as any).data) {
      // 处理 API 返回的数据结构
      console.log(response)
      tableList.value = (response as any).data.map((table: any, index: number) => ({
        id: table.id || index,
        name: table.table_name || table.name || '未知表名',
        description: table.description || table.comment || '',
        modifiedTime: table.gmt_modified || ''
      }))
      console.log(tableList.value)
      // 默认选择第一个表
      if (tableList.value.length > 0) {
        selectTable(0, tableList.value[0])
      }
    } else {
      tableList.value = []
    }
  } catch (error) {
    console.error('获取表信息列表失败:', error)
    ElMessage.error('获取表信息列表失败')
    tableList.value = []
  } finally {
    loading.value = false
  }
}

const loadTableFields = async (tableId: number) => {
  if (!tableId) return

  tableLoading.value = true
  try {
    const response = await getDatasourceTableFieldListApi(tableId)
    if (response && (response as any).data) {
      // 数据类型映射
      const dataTypeMapping: { [key: string]: string } = {
        'String': 'string',
        'Datetime': 'date',
        'Whole': 'integer',
        'Decimal': 'float',
        'Real': 'float',
        'Integer': 'integer',
        'Boolean': 'string'
      }

      // 字段属性映射
      const attributeMapping: { [key: string]: string } = {
        'Dimension': 'dimension',
        'Measure': 'measure'
      }

      // 处理字段数据结构
      const fields = (response as any).data.map((field: any) => ({
        fieldId: field.id,
        originalName: field.field || '未知字段',
        aliasName: field.alias || field.field || '未知字段',
        fieldType: dataTypeMapping[field.data_type] || 'string',
        fieldAttribute: attributeMapping[field.type] || attributeMapping[field.role] || 'dimension',
        fieldDescription: field.description || '',
        comment: field.description || '',
        ignore: field.ignore || false,
        enumList: field.filed_enum || []
      }))

      fieldListMap.value[selectedTableIndex.value] = fields
    } else {
      fieldListMap.value[selectedTableIndex.value] = []
    }
  } catch (error) {
    console.error('获取字段信息失败:', error)
    ElMessage.error('获取字段信息失败')
    fieldListMap.value[selectedTableIndex.value] = []
  } finally {
    tableLoading.value = false
  }
}

// 方法
const selectTable = async (index: number, table: any) => {
  selectedTableIndex.value = index
  selectedTable.value = {
    ...table,
  }
  selectedFields.value = []

  // 重置编辑状态
  isEditingDescription.value = false
  editingDescriptionText.value = ''

  // 加载选中表的字段信息
  if (table.id) {
    await loadTableFields(table.id)
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedFields.value = selection
}

const handleBatchEdit = () => {
  if (selectedFields.value.length === 0) {
    ElMessage.warning('请先选择要操作的字段')
    return
  }
  batchDialogVisible.value = true
}

const applyBatchOperation = () => {
  // 应用批量操作
  selectedFields.value.forEach(field => {
    if (batchForm.aliasAction === 'copyFromComment') {
      field.aliasName = field.comment
    }
    if (batchForm.fieldType) {
      field.fieldType = batchForm.fieldType
    }
    if (batchForm.fieldAttribute) {
      field.fieldAttribute = batchForm.fieldAttribute
    }
    if (batchForm.descriptionAction === 'copyFromComment') {
      field.fieldDescription = field.comment
    }
    if (batchForm.ignore !== null) {
      field.ignore = batchForm.ignore
    }
  })

  ElMessage.success(`已对${selectedFields.value.length}个字段应用批量操作`)
  batchDialogVisible.value = false

  // 重置批量操作表单
  batchForm.aliasAction = ''
  batchForm.fieldType = ''
  batchForm.fieldAttribute = ''
  batchForm.descriptionAction = ''
  batchForm.ignore = null
}

const handleSave = async () => {
  if (selectedTableIndex.value === -1 || !filteredFieldList.value.length) {
    ElMessage.warning('没有可保存的字段数据')
    return
  }

  if (!selectedTable.value?.id) {
    ElMessage.warning('缺少表ID信息')
    return
  }

  loading.value = true
  try {
    // 反向映射：前端值 -> 后端值
    const reverseDataTypeMapping: { [key: string]: string } = {
      'string': 'String',
      'date': 'Datetime',
      'integer': 'Integer',
      'float': 'Decimal'
    }

    const reverseAttributeMapping: { [key: string]: string } = {
      'dimension': 'Dimension',
      'measure': 'Measure'
    }

    // 准备批量更新的字段数据
    const batchUpdateData = filteredFieldList.value
      .filter(field => field.fieldId) // 只保存有fieldId的字段
      .map(field => ({
        id: field.fieldId,
        alias: field.aliasName,
        data_type: reverseDataTypeMapping[field.fieldType] || 'String',
        type: reverseAttributeMapping[field.fieldAttribute] || 'Dimension',
        description: field.fieldDescription || '',
        ignore: field.ignore,
        filed_enum: field.enumList || []
      }))

    if (batchUpdateData.length === 0) {
      ElMessage.warning('没有有效的字段数据可保存')
      return
    }

    // 批量保存所有字段
    await putDatasourceTableFieldApi(selectedTable.value.id, batchUpdateData)
    ElMessage.success(`成功保存${batchUpdateData.length}个字段`)
  } catch (error) {
    console.error('保存字段失败:', error)
    ElMessage.error('保存字段失败')
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  router.push({ path: 'dataSourceManage', query: {
    spaceCode:route.query.spaceCode || '',
  }})
}

// 表说明编辑相关方法
const startEditDescription = () => {
  if (!selectedTable.value) return
  isEditingDescription.value = true
  editingDescriptionText.value = selectedTable.value.description || ''

  // 使用 nextTick 确保 DOM 更新后再聚焦
  nextTick(() => {
    if (descriptionInputRef.value) {
      descriptionInputRef.value.focus()
    }
  })
}

const saveDescription = async () => {
  if (!selectedTable.value) return

  try {
    // 调用 API 更新表说明
    await putDatasourceTableApi(selectedTable.value.id, {
      description: editingDescriptionText.value
    })

    // 更新成功后更新本地数据
    selectedTable.value.description = editingDescriptionText.value

    // 同时更新表列表中对应的描述
    if (selectedTableIndex.value >= 0) {
      tableList.value[selectedTableIndex.value].description = editingDescriptionText.value
    }

    isEditingDescription.value = false
    ElMessage.success('表说明已保存')
    loadTableList()

  } catch (error) {
    console.error('保存表说明失败:', error)
    ElMessage.error('保存表说明失败')
  }
}

// 字段枚举相关方法
const handleFieldEnum = (field: any) => {
  currentField.value = field

  // 初始化字段枚举列表，如果字段没有枚举数据则创建默认项
  if (!field.enumList || field.enumList.length === 0) {
    currentFieldEnumList.value = [{ key: '', value: '' }]
  } else {
    currentFieldEnumList.value = [...field.enumList]
  }

  enumDialogVisible.value = true
}

const addEnumItem = () => {
  currentFieldEnumList.value.push({
    key: '',
    value: ''
  })
}

const removeEnumItem = (index: number) => {
  if (currentFieldEnumList.value.length > 1) {
    currentFieldEnumList.value.splice(index, 1)
  }
}

const saveFieldEnum = () => {
  if (!currentField.value) return

  // 验证枚举项是否有效
  const isValid = currentFieldEnumList.value.every(item =>
    item.key.trim() && item.value.trim()
  )

  if (!isValid) {
    ElMessage.warning('请确保所有枚举项的键值和显示值都不为空')
    return
  }

  // 检查键值是否重复
  const keys = currentFieldEnumList.value.map(item => item.key)
  const uniqueKeys = new Set(keys)
  if (keys.length !== uniqueKeys.size) {
    ElMessage.warning('枚举项的键值不能重复')
    return
  }

  // 保存枚举列表到字段对象
  currentField.value.enumList = [...currentFieldEnumList.value]

  ElMessage.success('字段枚举保存成功')
  enumDialogVisible.value = false
}

// 生命周期
onMounted(() => {
  loadTableList()
})
</script>

<style scoped>
.table-manage-container {
  background-color: #fff;
  min-height: calc(100vh - 70px);
  display: flex;
  flex-direction: column;
}

.table-manage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;

  .back-btn {
    margin-right: 10px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f1f7ff;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    color: #005EE0;

    &:hover {
      background-color: #005EE0;
      color: #fff;
    }
  }

  h2 {
    margin: 0;
    font-size: 18px;
    font-weight: 500;
  }
}

.table-manage-content {
  flex: 1;
  display: flex;
  height: calc(100vh - 150px);
  overflow: hidden;
}

.table-list-panel {
  min-width: 240px;
  width: 280px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}

.search-box {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}

.search-input {
  width: 100%;
}

.table-list {
  height: calc(100vh - 240px);
  /* flex: 1; */
  overflow-y: auto;
}

.table-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f5f7fa;
  }

  &.active {
    background-color: #EBF3FD;
    border-right: 3px solid #005EE0;
  }
}

.table-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.table-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.field-config-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-x: auto;
  min-width: 0;
}

.config-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 800px;
}

.table-description-section {
  margin-bottom: 20px;
}

.description-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.current-table-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.modified-time {
  font-size: 12px;
  color: #909399;
}

.error-tip {
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  color: #f56c6c;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;

  i {
    margin-right: 6px;
  }
}

.table-description-content {
  /* background-color: #f9f9f9; */
  padding: 12px;
  border-radius: 4px;

  p {
    margin: 0;
    font-size: 13px;
    color: #666;
    line-height: 1.5;
  }
}

.description-display {
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: rgba(24, 144, 255, 0.05);
  }
}

.description-text {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}

.description-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

.description-edit {
  .description-label {
    display: inline-block;
    margin-bottom: 8px;
    font-size: 13px;
    color: #666;
    font-weight: 500;
  }
}

.description-textarea {
  font-size: 13px;
}

.field-search-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.field-search-input {
  width: 300px;
}

.search-right {
  display: flex;
  gap: 0px;
}

.field-table-section {
  flex: 1;
  height: 100%;
  overflow-x: auto;
}

.field-table {
  height: calc(100vh - 300px);
  min-width: 1000px;
}

.no-table-selected {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #909399;
  font-size: 14px;
}

.batch-operation-content {
  .batch-tip {
    margin-bottom: 20px;
    font-size: 14px;
    color: #303133;

    .highlight {
      color: #1890ff;
      font-weight: 500;
    }
  }
}

.batch-form {
  .batch-option {
    display: flex;
    gap: 8px;
  }
}

.full-width {
  width: 100%;
}

/* 字段枚举管理弹窗样式 */
.enum-management-content {
  .current-field-info {
    margin-bottom: 20px;
    padding: 12px;
    background-color: #f0f9ff;
    border-radius: 6px;

    .field-name {
      font-size: 14px;
      font-weight: 500;
      color: #1890ff;
    }
  }

  .enum-list-section {
    .enum-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      .enum-title {
        font-size: 14px;
        font-weight: 500;
        color: #303133;
      }
    }

    .enum-items {
      .enum-item {
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 12px;

        &:last-child {
          margin-bottom: 0;
        }

        .enum-item-content {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;

          .enum-input-group {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;

            label {
              font-size: 12px;
              color: #606266;
              white-space: nowrap;
              min-width: 50px;
            }

            .enum-input {
              flex: 1;
            }
          }

          .enum-actions {
            flex-shrink: 0;
          }
        }
      }
    }
  }
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .table-list-panel {
    min-width: 240px;
    width: 240px;
  }
}

@media (max-width: 768px) {
  .table-manage-content {
    flex-direction: column;
  }

  .table-list-panel {
    min-width: 100%;
    width: 100%;
    height: 200px;
    flex-shrink: 0;
  }

  .field-config-panel {
    height: calc(100vh - 273px);
    overflow-x: auto;
  }

  .config-content {
    min-width: 600px;
  }

  .field-table {
    min-width: 800px;
  }

  .enum-management-content {
    .enum-items {
      .enum-item {
        .enum-item-content {
          flex-direction: column;
          align-items: stretch;
          gap: 12px;

          .enum-input-group {
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;

            label {
              min-width: unset;
            }
          }

          .enum-actions {
            align-self: center;
          }
        }
      }
    }
  }
}
.enum-btn{
  color: #1890ff;
  cursor: pointer;
}
</style>
