<template>
  <div class="list-show" :class="{'inline': inline}">
      <div
          v-for="(tag, index) in viewList"
          :key="index"
          class="list-show-item"
          :class="{
              'single-item': dataList.length === 1,
              'pointer-cursor': cursor,
              'has-popover-btn-item': dataList.length > max && index === (max - 1),
              [showItemCls]: true
          }"
          :style="showItemStyle"
          >
          <slot name="item" v-bind:content="tag">
              <el-tag v-if="autoEllipsis" :type="(tag.tagType || type) as any" :style="tagStyle" @click="handleClick(tag)">
                  <slot name="content" v-bind:content="tag">
                      <mone-ellipsis-tooltip :width="showWidth" :content="tag[labelField] || tag">
                          <span>{{ tag[labelField] || tag }}</span>
                      </mone-ellipsis-tooltip>
                  </slot>
              </el-tag>
              <el-tooltip v-else effect="dark" :content="tag[popoverLabelField]" placement="top">
                  <el-tag :type="(tag.tagType || type) as any" :style="tagStyle" @click="handleClick(tag)">
                      <slot name="content" v-bind:content="tag">
                          {{ tag[labelField] || tag }}
                      </slot>
                  </el-tag>
              </el-tooltip>
          </slot>
          <el-popover v-if="dataList.length > max && index === max - 1" :trigger="trigger as any" placement="bottom">
              <span slot="reference" class="more-count" @click.stop="handleViewMore">+{{ dataList.length - max }}</span>
              <div class="more-title">全部（{{dataList.length}}）</div>
              <div class="more-box">
                  <div
                      v-for="(item, idx) in dataList"
                      :key="idx"
                      class="list-pop-item"
                      :class="{
                          'pointer-cursor': cursor,
                          [popItemCls]: true
                      }">
                      <slot name="popItem" v-bind:content="item">
                          <el-tag :type="(item.tagType || type) as any" :style="tagStyle" @click="handleClick(item)">
                              <slot name="popContent" v-bind:content="item">
                                  <mone-ellipsis-tooltip :width="width" :content="item[popoverLabelField] || item">
                                      <span>{{ item[popoverLabelField] || item }}</span>
                                  </mone-ellipsis-tooltip>
                              </slot>
                          </el-tag>
                      </slot>
                  </div>
              </div>
          </el-popover>
      </div>
      <slot name="suffix"></slot>
  </div>
</template>

<script setup lang="ts">
import { computed, defineProps, defineEmits } from 'vue';

interface TagItem {
  [key: string]: any;
  tagType?: string;
}

const props = defineProps<{
  dataList: TagItem[];
  max?: number;
  labelField?: string;
  popLabelField?: string;
  trigger?: string;
  showWidth?: string;
  width?: string;
  type?: string;
  inline?: boolean;
  cursor?: boolean;
  showItemCls?: string;
  popItemCls?: string;
  tagStyle?: string;
  showItemStyle?: string;
  autoEllipsis?: boolean;
}>();

const emit = defineEmits<{
  (e: 'clickItem', item: TagItem): void;
}>();

const dataList = computed(() => props.dataList ?? []);
const max = computed(() => props.max ?? 3);
const labelField = computed(() => props.labelField ?? '');
const popLabelField = computed(() => props.popLabelField ?? '');
const trigger = computed(() => props.trigger ?? 'click');
const showWidth = computed(() => props.showWidth ?? '160px');
const width = computed(() => props.width ?? '360px');
const type = computed(() => props.type ?? 'info');
const inline = computed(() => props.inline ?? false);
const cursor = computed(() => props.cursor ?? false);
const showItemCls = computed(() => props.showItemCls ?? '');
const popItemCls = computed(() => props.popItemCls ?? '');
const tagStyle = computed(() => props.tagStyle ?? '');
const showItemStyle = computed(() => props.showItemStyle ?? '');
const autoEllipsis = computed(() => props.autoEllipsis !== false);

const viewList = computed(() => {
  return max.value > 0 ? dataList.value.slice(0, max.value) : dataList.value;
});

const popoverLabelField = computed(() => popLabelField.value || labelField.value);

function handleViewMore() {
  // 可编辑表格中点击无效问题
}

function handleClick(item: TagItem) {
  emit('clickItem', item);
}
</script>

<style lang="scss" scoped>
.pointer-cursor{
  cursor: pointer;
}
.list-show{
  display: flex;
  // 一行显示
  flex-wrap: wrap;

}
// 换行显示
.list-show-item{
  min-height: 20px;
  margin: 0 6px 6px 0;
  line-height: 1;
  &::marker{
    display: none;
  }
  // &.single-item, &:last-child{
  &.single-item{
      margin-bottom: 0;
  }
  &.has-popover-btn-item{
      display: flex;
      align-items: center;
  }
}
.list-pop-item{
  margin-bottom: 5px;
}
.more-title {
  padding-bottom: 8px;
}
.more-count {
  display: inline-block;
  cursor: pointer;
  background: #e8f5ff;
  border-radius: 2px;
  padding: 0px 5px;
  color: #005EE0;
  font-size: 12px;
  line-height: 20px;
  vertical-align: top;
  margin-left: 4px;
  white-space: nowrap;
}
.more-box {
  max-height: 260px;
  overflow: auto;
  padding: 0 5px;
}
</style>
