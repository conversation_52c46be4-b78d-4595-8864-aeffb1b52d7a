<script lang="ts" setup>
import { tr } from 'element-plus/es/locale';
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  currentNode: {
    type: Object,
    default: () => {}
  }
})


const currentNodeData = ref(props.currentNode);

const emit = defineEmits(['nodeClick', 'nodeCommand']);

const isDisabled = (data: any) => {
  return !data.parent_id;
}
const handleNodeClick = (data: any) => {
  // 分组节点不可点击
  if (!data.parent_id) return;
  currentNodeData.value = data;
  emit('nodeClick', data);
}

const handleCommand = (command: string, data: any) => {
  emit('nodeCommand', command, data);
}
// 根据权限判断是否显示树操作
function isShowEvent(data:any){
  let flag = true
  const { proxy } = getCurrentInstance();
  if(!data.parent_id) {
    // 数据主题 如果无数据主题新增权限，则无编辑和删除权限
    if(!(proxy.GetPermission(['data_subject:create']))) {
      flag = false
    }
  } else {
    // 数据模型
    if(!(proxy.GetPermission(['data_model_permissions:edit'],data.id)) && !(proxy.GetPermission(['data_model_permissions:delete'],data.id))) {
      flag = false
    }
  }
  return flag
}
</script>

<template>
  <div class="model-tree-container">
    <el-tree :data="data" default-expand-all @node-click="handleNodeClick"
      :current-node-key="currentNodeData?.id" node-key="id"
      :props="{
        label: 'name',
        children: 'children',
        disabled: isDisabled,
    }">
      <template #default="{ data }">
        <div class="node-content">
          <div class="node-label" :disabled="!data.parent_id">
            <i class="iconfont ChatData-zhuti" v-if="!data.parent_id"></i>
            <i class="iconfont ChatData-cangchupeizhi" v-else></i>
            {{ data.name }}
          </div>
          <span class="operate">
            <el-dropdown trigger="hover" @command="(command) => handleCommand(command, data)">
              <span class="node-dropdown"  >
                <el-icon v-if="isShowEvent(data)">
                  <MoreFilled />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <template v-if="!data.parent_id">
                    <el-dropdown-item command="add">新增模型</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="delete" divided style="color: red">
                      删除
                    </el-dropdown-item>
                  </template>
                  <template v-else>
                    <el-dropdown-item command="cancelCollect">{{ data.popular ? '取消收藏' : '收藏' }}</el-dropdown-item>
                    <el-dropdown-item command="cloneNode">克隆</el-dropdown-item>
                    <el-dropdown-item command="moveNode">移动到</el-dropdown-item>
                    <el-dropdown-item v-if="GetPermission(['data_model_permissions:edit'],data.id)" command="editNode">编辑</el-dropdown-item>
                    <el-dropdown-item command="deleteNode" divided style="color: red" v-if="GetPermission(['data_model_permissions:delete'],data.id)">
                      <!-- <el-icon>
                        <Delete />
                      </el-icon> -->
                      删除
                    </el-dropdown-item>
                  </template>

                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </span>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<style lang="scss" scoped>
.model-tree-container {
  .el-tree {
    padding: 5px;
    background: none;

    :deep(.el-tree-node__children) {
      .is-current {
        .el-tree-node__content {
          background-color: #EBF3FD;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: 5px;
            left: 0;
            width: 3px;
            height: 18px;
            background: #005ee0;
            border-radius: 0 100px 100px 0;
          }

          .node-label {
            color: #005EE0;
            font-weight: bold;
          }
        }
      }
    }


    :deep(.el-tree-node__content) {
      display: flex;
      justify-content: space-between;
      height: 30px;
      padding-right: 10px;

      .operate {
        visibility: hidden;
        font-size: 12px;
      }

      .node-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .node-label {
          font-size: 14px;
          color: #262626;
          display: flex;
          .iconfont {
            margin-right: 5px;
            font-size: 14px;
            &.ChatData-cangchupeizhi {
              font-size:13px;
            }
          }
        }
        &:hover {
          background-color: #EBF3FD;
          .node-label {
            color: #005EE0;
          }
          .operate {
            color: #005EE0;
            visibility: visible;
          }
        }
      }

    }
  }
}
</style>
