import type { RouteRecordRaw } from "vue-router"
import { createRouter } from "vue-router"
import { routerConfig } from "@/router/config"
import { registerNavigationGuard } from "@/router/guard"
import { flatMultiLevelRoutes } from "./helper"

const Layouts = () => import("@/layouts/index.vue")

/**
 * @name 常驻路由
 * @description 除了 redirect/403/404/login 等隐藏页面，其他页面建议设置唯一的 Name 属性
 */
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layouts,
    meta: {
      hidden: true
    },
    children: [
      {
        path: ":path(.*)",
        component: () => import("@/pages/redirect/index.vue")
      }
    ]
  },
  {
    path: "/403",
    component: () => import("@/pages/error/403.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/404",
    component: () => import("@/pages/error/404.vue"),
    meta: {
      hidden: true
    },
    alias: "/:pathMatch(.*)*"
  },
  {
    path: "/login",
    component: () => import("@/pages/login/index.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/semanticmodelingEdit",
    name: "SemanticModelingEdit",
    component: () => import("@/pages/semanticmodeling/editPage.vue"),
    meta: {
      hidden: true
    }
  },
  {
    path: "/",
    component: Layouts,
    redirect: "/chat",
    children: [
      {
        path: "chat",
        component: () => import("@/pages/chatPage/index.vue"),
        name: "Chat",
        meta: {
          title: "问答对话",
          icon: "ChatData-duihua",
          affix: true,
          showSildeMenu: false
        }
      },
      {
        path: "agent",
        component: () => import("@/pages/agent/index.vue"),
        name: "agent",
        meta: {
          title: "助手管理",
          icon: "ChatData-jiqiren",
          showSildeMenu: false
        },
        redirect: "/agent/agentIndex",
        children: [
          {
            path: "agentIndex",
            component: () => import("@/pages/agent/dashboard.vue"),
            name: "agentIndex",
            meta: {

              activeMenu: "/agent",
              groupTitle: "助手管理",
              showSildeMenu: false
            }
          },
          {
            path: "agentManage",
            component: () => import("@/pages/agent/agentManage.vue"),
            name: "agentManage",
            meta: {
              activeMenu: "/agent",
              groupTitle: "助手管理",
              showSildeMenu: false
            }
          }
        ]
      },
      {
        path: "plugin",
        component: () => import("@/pages/chatPlugin/index.vue"),
        name: "Plugin",
        meta: {
          title: "插件管理",
          icon: "ChatData-yewuzujian-xian",
          showSildeMenu: true

        }
      },
      {
        path: "semanticmodeling",
        component: () => import("@/pages/semanticmodeling/index.vue"),
        name: "Semanticmodeling",
        meta: {
          title: "语义建模",
          icon: "ChatData-liuchengjianmo",
          showSildeMenu: false
        }
      },
      {
        path: "metrics",
        component: () => import("@/pages/metrics/index.vue"),
        name: "Metrics",
        meta: {
          title: "指标市场",
          icon: "ChatData-yewuchangjing",
          showSildeMenu: true
        }
      },
      {
        path: "knowledge",
        component: () => import("@/pages/knowledge/index.vue"),
        name: "knowledge",
        meta: {
          title: "知识库",
          icon: "ChatData-jiqiren",
          showSildeMenu: false
        },
        redirect: "/knowledge/knowledgeIndex",
        children: [
          {
            path: "knowledgeIndex",
            component: () => import("@/pages/knowledge/dashboard.vue"),
            name: "knowledgeIndex",
            meta: {

              activeMenu: "/knowledge",
              groupTitle: "知识库",
              showSildeMenu: false
            }
          },
          {
            path: "createKnowledge",
            component: () => import("@/pages/knowledge/createKnowledge.vue"),
            name: "createKnowledge",
            meta: {
              activeMenu: "/knowledge",
              groupTitle: "知识库管理",
              showSildeMenu: false
            }
          },
          {
            path: "knowledgeDetail",
            component: () => import("@/pages/knowledge/knowledgeDetail.vue"),
            name: "knowledgeDetail",
            meta: {
              activeMenu: "/knowledge",
              groupTitle: "知识库管理",
              showSildeMenu: false
            }
          }
        ]
      },
      {
        path: "llm",
        component: () => import("@/pages/llm/index.vue"),
        name: "LLM",
        redirect: "/llm/basic",
        meta: {
          title: "系统管理",
          icon: "ChatData-xitong",
          showSildeMenu: true
        },
        children: [
          // 基版管理分组
          {
            path: "basic",
            redirect: "/llm/basic/modelManage",
            meta: {
              title: "基版管理",
              activeMenu: "/llm",
              groupTitle: "系统管理",
              showSildeMenu: true
            },
            children: [{
              path: "modelManage",
              component: () => import("@/pages/llm/modelManage/index.vue"),
              name: "modelManage",
              meta: {
                title: "模型管理",
                icon: "ChatData-yewuchangjing",
                activeMenu: "/llm",
                groupTitle: "大模型管理",
                showSildeMenu: true
              }
            }, {
              path: "modelManageEdit",
              component: () => import("@/pages/llm/modelManage/modelManageEdit.vue"),
              name: "modelManageEdit",
              meta: {
                title: "模型管理编辑",
                icon: "ChatData-yewuchangjing",
                activeMenu: "/llm",
                groupTitle: "大模型管理",
                showSildeMenu: true, // 显示左侧菜单栏
                hide: true // 隐藏路由
              }
            }, {
              path: "dataSourceManage",
              component: () => import("@/pages/llm/dataSourceManage/index.vue"),
              name: "dataSourceManage",
              meta: {
                title: "数据源管理",
                icon: "ChatData-cangchupeizhi",
                activeMenu: "/llm",
                groupTitle: "大模型管理",
                showSildeMenu: true
              }
            }, {
              path: "dataSourceManageEdit",
              component: () => import("@/pages/llm/dataSourceManage/dataSourceManageEdit.vue"),
              name: "dataSourceManageEdit",
              meta: {
                title: "数据源管理编辑",
                icon: "ChatData-yewuchangjing",
                activeMenu: "/llm",
                groupTitle: "大模型管理",
                showSildeMenu: true, // 显示左侧菜单栏
                hide: true // 隐藏路由
              }
            }, {
              path: "dataSourceManageTableList",
              component: () => import("@/pages/llm/dataSourceManage/dataSourceManageTableList.vue"),
              name: "dataSourceManageTableList",
              meta: {
                title: "数据源管理表信息",
                icon: "ChatData-yewuchangjing",
                activeMenu: "/llm",
                groupTitle: "大模型管理",
                showSildeMenu: true, // 显示左侧菜单栏
                hide: true // 隐藏路由
              }
            }]
          },
          // 系统配置分组
          {
            path: "systemConfig",
            redirect: "/llm/systemConfig/workspaceManage",
            meta: {
              title: "系统配置",
              groupTitle: "系统配置",
              showSildeMenu: true
            },
            children: [{
              path: "workspaceManage",
              component: () => import("@/pages/llm/workspaceManage/index.vue"),
              name: "workspaceManage",
              meta: {
                title: "工作空间管理",
                icon: "ChatData-yingyong",
                activeMenu: "/llm",
                groupTitle: "系统管理",
                showSildeMenu: true
              }
            }, {
              path: "workspaceManageCreate",
              component: () => import("@/pages/llm/workspaceManage/workspaceManageCreate.vue"),
              name: "workspaceManageCreate",
              meta: {
                title: "空间新增",
                icon: "ChatData-yewuchangjing",
                activeMenu: "/llm",
                groupTitle: "系统管理",
                showSildeMenu: true, // 显示左侧菜单栏
                hide: true // 隐藏路由
              }
            },{
              path: "authorityManage",
              component: () => import("@/pages/llm/authorityManage/index.vue"),
              name: "authorityManage",
              meta: {
                title: "权限管理",
                icon: "ChatData-quanxian",
                activeMenu: "/llm",
                groupTitle: "系统管理",
                showSildeMenu: true
              }
            },]
          }
        ]
      }
    ]
  }
]

/**
 * @name 动态路由
 * @description 用来放置有权限 (Roles 属性) 的路由
 * @description 必须带有唯一的 Name 属性
 */
export const dynamicRoutes: RouteRecordRaw[] = []

/** 路由实例 */
export const router = createRouter({
  history: routerConfig.history,
  routes: routerConfig.thirdLevelRouteCache ? flatMultiLevelRoutes(constantRoutes) : constantRoutes
})

/** 重置路由 */
export function resetRouter() {
  try {
    // 注意：所有动态路由路由必须带有 Name 属性，否则可能会不能完全重置干净
    router.getRoutes().forEach((route) => {
      const { name, meta } = route
      if (name && meta.roles?.length) {
        router.hasRoute(name) && router.removeRoute(name)
      }
    })
  } catch {
    // 强制刷新浏览器也行，只是交互体验不是很好
    location.reload()
  }
}

// 注册路由导航守卫
registerNavigationGuard(router)
