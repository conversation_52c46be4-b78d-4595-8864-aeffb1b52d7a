<template>
  <div class="agent-manage-container" v-loading="loading">
    <div class="head">
      <div class="title">
        <!-- 切换助手 -->
        <back-select
          v-model="curEditAgent"
          v-if="isEdit || isView"
          :data="agentList"
          label="app_name"
          value="app_code"
          @change="handleAgentChange"
          @back="goBack"
        />
        <template v-else>
          <div class="back-icon-wrapper" @click="goBack">
            <i class="ChatData-fanhui iconfont"></i>
          </div>
          <span class="title-text">{{ titleText }}</span>
        </template>
      </div>
      <!-- 头部操作 -->
      <div class="btns">
        <!-- 编辑模式下 -->
        <el-button
          v-show="GetPermission(['agent_permissions:publish'],detailData.app_code)"
          type="primary"
          plain
          size="small"
          v-if="isEdit"
          @click="handleUnPublishAgent(detailData.published !== 'true')"
        >
          {{ detailData.published === "true" ? "取消发布" : "发布" }}
        </el-button>
        <!-- 不为查看模式下 -->
        <el-button type="primary" v-if="!isView" size="small" @click="submitAgentMange">
          {{
            isEdit ? "保存" : "确定"
          }}
        </el-button>
        <!-- 查看模式下 -->
        <el-button v-if="isView" :type="detailData.published === 'true' ? 'success' : 'primary'" size="small" plain>
          {{ detailData.published === 'true' ? '已发布' : '未发布' }}
        </el-button>
        <!-- 新增模式下 -->
        <el-button type="info" size="small" v-if="!isEdit && !isView" @click="goBack">
          取消
        </el-button>
        <!-- 不为新增模式 -->
        <el-tooltip content="开始对话" v-if="isEdit || isView">
          <i @click.stop="gotoChat" class="iconfont ChatData-duihua start-chart" />
        </el-tooltip>
        <!-- 编辑模式下 -->
        <el-dropdown
          @command="(cmd) => handleDropdownCommand(cmd)"
          v-if="isEdit"
        >
          <el-icon v-show="GetPermission(['agent_permissions:delete'],detailData.app_code)"><MoreFilled /></el-icon>

          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="delete" divided>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <div class="config">
      <el-tabs
        classs="agent-tabs"
        :class="(isEdit || isView) ? 'edit-tabs' : 'add-tabs'"
        v-model="agentTabVal"
      >
        <el-tab-pane
          label="助手信息"
          name="info"
          class="info-content" :class="[isView && 'info-content-view']"
        >
          <agent-info-detail
            ref="agentInfoDetailRef"
            :data="detailData"
            v-if="isView"
          />
          <template v-else>
            <div class="basic-info">
              <agent-basic-info ref="agentBasicInfoRef" />
            </div>
            <div class="model-config">
              <agent-model-config ref="agentModelConfigRef" />
            </div>
          </template>
        </el-tab-pane>
        <el-tab-pane
          label="记忆管理"
          name="memory"
          v-if="isEdit || isView"
          class="memory-content"
        >
          <memory-manage v-if="agentTabVal === 'memory'" :can-edit="!isView" ref="memoryManageRef" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, toRaw, computed, onMounted } from "vue";
import { Back } from "@element-plus/icons-vue";
import agentBasicInfo from "./agentBasicInfo.vue";
import agentModelConfig from "./agentModelConfig.vue";
import { useRouter, useRoute } from "vue-router";
import { clone, cloneDeep, get } from "lodash-es";
import {
  addAssistantApi,
  updateAssistantApi,
  getAssistantDetailApi,
  deleteAssistantApi,
  publishAssistantApi,
  unPublishAssistantApi,
} from "@/common/apis/agent";
import type * as Agent from "@/common/apis/agent/type";
import type { Agent as ChatAgent } from "@/common/apis/chat/type";

import { ElMessage } from "element-plus";
import backSelect from "./components/backSelect.vue";
import { getAgentListApi } from "@/common/apis/chat";
import memoryManage from "./memoryManage.vue";
import agentInfoDetail from "./agentInfoDetail.vue";

const router = useRouter();
const route = useRoute();
const agentList = ref<ChatAgent.TableData[]>([]); // 助手列表
const curEditAgent = ref<string>(); // 当前编辑助手
const detailData = ref<ChatAgent.TableData>({} as ChatAgent.TableData); // 编辑详情
const agentTabVal = ref<string>("info");

// 获取实例
const agentBasicInfoRef = ref();
const agentModelConfigRef = ref();
const memoryManageRef = ref()

// 是否为编辑
const isEdit = computed<boolean>(() => {
  return route?.query?.type === "edit";
});
// 是否为查看
const isView = computed<boolean>(() => {
  // 不存在编辑权限
  const { proxy } = getCurrentInstance();
  return !proxy.GetPermission(['agent_permissions:edit'],detailData.value.app_code) && !proxy.GetPermission(['agent:create'])
});
const titleText = computed<string>(() => {
  return isEdit.value ? "编辑助手" : "新增助手";
});
const loading = ref<boolean>(false);

provide("curEditAgent", curEditAgent)

// ---------------------------
// 助手切换 重新查询 助手信息或记忆管理
function handleAgentChange(app_code: string) {
  // if (agentTabVal.value === "info") {
    getAssistantDetail(app_code)
  // } else {
    if (memoryManageRef.value) {
      memoryManageRef.value.getQuestionList()
    }
  // }
  // console.log(app_code, 888);
}

// 取消发布 / 发布助手
function handleUnPublishAgent(isPublish: boolean = false) {
  ElMessageBox.confirm(`确定${isPublish ? "" : "取消"}发布该助手吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    let API = isPublish ? publishAssistantApi : unPublishAssistantApi;
    API({
      app_code: curEditAgent.value,
    })
      .then((res: any) => {
        ElMessage.success(`${isPublish ? "" : "取消"}发布成功`);
        getAssistantDetail(curEditAgent.value);
      })
      .catch((err: any) => {
        ElMessage.error("取消发布失败");
      });
  });
}

// 下拉菜单命令
function handleDropdownCommand(command: string) {
  switch (command) {
    case "delete":
      handleDeleteAgent();
      break;
  }
}
// 删除助手
function handleDeleteAgent() {
  ElMessageBox.confirm("确定删除该助手吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteAssistantApi({
      app_code: curEditAgent.value,
    })
      .then(async (res: any) => {
        ElMessage.success("删除成功");
        // 删除后重置
        await getAgentList();
        // 重新设置当前选项 默认为第一个
        curEditAgent.value = agentList.value[0]?.app_code;
        getAssistantDetail(curEditAgent.value);
      })
      .catch((err: any) => {
        ElMessage.error("删除失败");
      });
  });
}
// 跳转至对话页面
const gotoChat = () => {
  router.push({
    name: "Chat",
    query: {
      spaceCode: route.query.spaceCode || "",
      initAgent: curEditAgent.value,
    },
  });
};

// 后退到助手首页
const goBack = (): void => {
  router.push({
    name: "agent",
    query: {
      spaceCode: route.query.spaceCode || "",
    },
  });
};

// 提交新增 / 编辑助手
const submitAgentMange = async (): Promise<void> => {
  // 获取基础信息内抛出的 校验、获取数据方法
  const { validateForm, getData: getBasicData } = agentBasicInfoRef.value;
  // 获取模型配置内抛出的 校验、获取数据方法
  const { getData: getModelData, validateForm: validateModelForm } =
    agentModelConfigRef.value;
  // 开始校验
  let validBase: boolean = await validateForm();
  let validModel: boolean = await validateModelForm();

  if (!!validBase && !!validModel) {
    loading.value = true;
    // 固定参数
    let constParams = {
      team_mode: "native_app",
      language: "zh",
      workspace_id: route.query.spaceCode || "",
    };
    // 获取数据
    let basicData = cloneDeep(toRaw(getBasicData()));
    let modelData = cloneDeep(toRaw(getModelData()));
    let param_need: any[] = [
      {
        type: "knowledgeBase",
        value: basicData.knowledgeBase,
      },
      {
        type: "dataset",
        value: basicData.dataset,
      },
      {
        type: "temperature",
        value: basicData.temperature,
      },
      {
        type: "max_tokens",
        value: basicData.max_tokens,
      },
      {
        type: "admin",
        value: null,
      },
      {
        type: "user",
        value: null,
      },
    ];
    for (let key in modelData) {
      param_need.push({
        [key]: modelData[key],
      });
    }
    // 生成请求参数
    let reqParams = {
      ...constParams,
      ...basicData,
      param_need,
    };
    // 新增时，需先新增应用后调用编辑、编辑时可直接调用
    // 新增以返回的app_code为编辑的app_code，编辑直接使用本身
    const { app_name, app_describe } = basicData;
    let newAppData = !isEdit.value
      ? await handleCreateApp({ app_name, app_describe })
      : curEditAgent.value || get(route.query, "app_code", "");

    reqParams.app_code = newAppData;
    // 创建应用后继续编辑或本身
    console.log(newAppData, basicData, reqParams, 888);

    if (reqParams.app_code) {
      updateAssistantApi(reqParams)
        .then((res: any) => {
          ElMessage.success("操作成功");
          loading.value = false;
          goBack();
        })
        .catch((err: any) => {
          loading.value = false;
        });
    }
  }
};

// 创建助手前需要先创建应用
async function handleCreateApp(params: Agent.createAppData): Promise<string> {
  try {
    const res: any = await addAssistantApi({
      ...params,
      team_mode: "native_app",
      language: "zh",
      workspace_id: route.query.spaceCode || "",
    });
    return res?.data?.app_code;
  } catch (err) {
    throw err; // 抛出错误，让调用者可以处理
  }
}

// 获取助手详情
const getAssistantDetail = (customAppCode?: string) => {
  const { app_code } = route.query;
  loading.value = true;
  // 用自定义的app_code 或 route.query.app_code
  getAssistantDetailApi({
    app_code: (customAppCode || app_code) as string,
  })
    .then((res: any) => {
      detailData.value = cloneDeep(res.data || {});
      // 获取基础信息、模板配置内抛出的 重置表单方法
      const { resetForm: resetBasicForm } = agentBasicInfoRef.value;
      const { resetForm: resetModelForm, initModelConfig } =
        agentModelConfigRef.value;

      // 基础信息重置需要的数据
      let basicDataInit = cloneDeep(res.data || {});

      console.log(basicDataInit, 888);
      // 模型重置需要的数据
      let modelInit = Object.keys(initModelConfig).reduce((acc, key) => {
        acc[key] = {};
        return acc;
      }, {});

      // 重设模型所需数据
      const { param_need = [] } = res.data;
      let basicDataKey = [
        "knowledgeBase",
        "temperature",
        "max_tokens",
        "admin",
        "user",
        "dataset",
      ];
      param_need.forEach((item: any) => {
        if (basicDataKey.includes(item.type)) {
          basicDataInit[item.type] = item.value;
          // modelInit[item.type] = item.value;
        } else {
          for (let key in item) {
            if (modelInit[key]) {
              modelInit[key] = {
                ...item[key],
              };
            }
          }
        }
      });
      // 重设基础信息表单
      resetBasicForm(basicDataInit);
      // 重设模型配置表单
      resetModelForm(modelInit);
      // console.log(res, resetBasicForm, resetModelForm, modelInit, 888);
      loading.value = false;
    })
    .catch((err: any) => {
      loading.value = false;
    });
};

// 获取助手列表
async function getAgentList(params: object = {}) {
  loading.value = true;
  agentList.value = [];
  // 拼接查询参数
  let reqParams: object = {
    // app_name: app_name.value,
    // app_code: app_code.value,
    // published: published.value !== "all" ? published.value : "",
    published: "",
    workspace_id: route.query.spaceCode || "",
  };
  await getAgentListApi({
    page: 1,
    pageSize: 1000,
    ...reqParams,
    ...params,
  })
    .then((res: any) => {
      agentList.value = res?.data?.app_list ?? [];
      if(!agentList.value.length) {
        goBack()
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
}
onMounted(async () => {
  // 如果是编辑，则获取助手详情进行数据初始化
  if (isEdit.value || isView.value) {
    // 初始化选中、获取助手列表
    curEditAgent.value = (route?.query?.app_code ?? "") as string;
    await getAgentList();
    await getAssistantDetail();
  }
});
</script>

<style lang="scss" scoped>
.btns {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: end;
  * {
    margin: 0px;
  }
}
.agent-manage-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  .head {
    width: 100%;
    height: 50px;
    padding: 0 20px;
    box-sizing: border-box;
    box-shadow: 0px 1px 0px 0px #ededed;
    display: flex;
    align-items: center;
    .title {
      display: flex;
      align-items: center;
      gap: 15px;
      .back-icon-wrapper {
        padding: 5px;
        border-radius: 8px;
        color: #005ee0;
        /* font-weight: bold; */
        background: #ebf3fd;
        display: flex;
        align-items: center;
        cursor: pointer;
      }
      .title-text {
        font-weight: bold;
        font-size: 16px;
        color: #262626;
        line-height: 22px;
      }
    }
  }
  .config {
    flex: 1;
    overflow: hidden;

    padding-bottom: 10px;
    :deep(.info-content) {
      display: grid;
      grid-template-columns: 1fr 1fr;
      overflow: hidden;
      height: 100%;
      .basic-info,
      .model-config {
        padding: 20px;
        padding-bottom: 0px;
        overflow-y: auto;
      }
      .basic-info {
        border-right: 1px solid #ededed;
      }
    }
    :deep(.info-content-view) {
      width: 70%;
      display: flex;
      margin-left: 15%;
      overflow: auto;
      padding: 10px;
    }
    :deep(.memory-content) {
      height: 100%;
    }
  }
  :deep(.add-tabs) {
    height: 100%;
    .el-tabs__header {
      display: none;
    }
  }
  :deep(.edit-tabs) {
    height: 100%;
    padding: 5px 20px 0px 20px;
    .el-tabs__header {
      margin-bottom: 0px;
      .el-tabs__nav-wrap {
        &::after {
          height: 1px;
          background: #ededed;
        }
        .el-tabs__item {
          font-weight: bold;
        }
      }
    }
    .basic-info {
      padding: 20px 20px 20px 0px !important;
      overflow-x: hidden;
    }
    .model-config {
      padding-top: 20px !important;
    }
  }
}
.start-chart {
  color: #005ee0;
  font-size: 14px;
  cursor: pointer;
}
</style>
